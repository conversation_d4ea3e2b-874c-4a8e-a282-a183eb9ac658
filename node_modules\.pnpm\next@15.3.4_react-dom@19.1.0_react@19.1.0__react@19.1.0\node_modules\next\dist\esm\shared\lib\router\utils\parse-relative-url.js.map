{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/parse-relative-url.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\nimport { getLocationOrigin } from '../../utils'\nimport { searchParamsToUrlQuery } from './querystring'\n\nexport interface ParsedRelativeUrl {\n  hash: string\n  href: string\n  pathname: string\n  query: ParsedUrlQuery\n  search: string\n}\n\n/**\n * Parses path-relative urls (e.g. `/hello/world?foo=bar`). If url isn't path-relative\n * (e.g. `./hello`) then at least base must be.\n * Absolute urls are rejected with one exception, in the browser, absolute urls that are on\n * the current origin will be parsed as relative\n */\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery?: true\n): ParsedRelativeUrl\nexport function parseRelativeUrl(\n  url: string,\n  base: string | undefined,\n  parseQuery: false\n): Omit<ParsedRelativeUrl, 'query'>\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery = true\n): ParsedRelativeUrl | Omit<ParsedRelativeUrl, 'query'> {\n  const globalBase = new URL(\n    typeof window === 'undefined' ? 'http://n' : getLocationOrigin()\n  )\n\n  const resolvedBase = base\n    ? new URL(base, globalBase)\n    : url.startsWith('.')\n      ? new URL(\n          typeof window === 'undefined' ? 'http://n' : window.location.href\n        )\n      : globalBase\n\n  const { pathname, searchParams, search, hash, href, origin } = new URL(\n    url,\n    resolvedBase\n  )\n\n  if (origin !== globalBase.origin) {\n    throw new Error(`invariant: invalid relative URL, router received ${url}`)\n  }\n\n  return {\n    pathname,\n    query: parseQuery ? searchParamsToUrlQuery(searchParams) : undefined,\n    search,\n    hash,\n    href: href.slice(origin.length),\n  }\n}\n"], "names": ["getLocationOrigin", "searchParamsToUrlQuery", "parseRelativeUrl", "url", "base", "parse<PERSON><PERSON>y", "globalBase", "URL", "window", "resolvedBase", "startsWith", "location", "href", "pathname", "searchParams", "search", "hash", "origin", "Error", "query", "undefined", "slice", "length"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,cAAa;AAC/C,SAASC,sBAAsB,QAAQ,gBAAe;AA0BtD,OAAO,SAASC,iBACdC,GAAW,EACXC,IAAa,EACbC,UAAiB;IAAjBA,IAAAA,uBAAAA,aAAa;IAEb,MAAMC,aAAa,IAAIC,IACrB,OAAOC,WAAW,cAAc,aAAaR;IAG/C,MAAMS,eAAeL,OACjB,IAAIG,IAAIH,MAAME,cACdH,IAAIO,UAAU,CAAC,OACb,IAAIH,IACF,OAAOC,WAAW,cAAc,aAAaA,OAAOG,QAAQ,CAACC,IAAI,IAEnEN;IAEN,MAAM,EAAEO,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAEJ,IAAI,EAAEK,MAAM,EAAE,GAAG,IAAIV,IACjEJ,KACAM;IAGF,IAAIQ,WAAWX,WAAWW,MAAM,EAAE;QAChC,MAAM,qBAAoE,CAApE,IAAIC,MAAM,AAAC,sDAAmDf,MAA9D,qBAAA;mBAAA;wBAAA;0BAAA;QAAmE;IAC3E;IAEA,OAAO;QACLU;QACAM,OAAOd,aAAaJ,uBAAuBa,gBAAgBM;QAC3DL;QACAC;QACAJ,MAAMA,KAAKS,KAAK,CAACJ,OAAOK,MAAM;IAChC;AACF"}