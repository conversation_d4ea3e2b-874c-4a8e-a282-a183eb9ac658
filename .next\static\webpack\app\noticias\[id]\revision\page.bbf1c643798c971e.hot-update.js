"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/noticias/[id]/revision/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Plus)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M5 12h14\",\n            key: \"1ays0h\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 5v14\",\n            key: \"s699le\"\n        }\n    ]\n];\nconst Plus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"plus\", __iconNode);\n //# sourceMappingURL=plus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Send)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z\",\n            key: \"1ffxy3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21.854 2.147-10.94 10.939\",\n            key: \"12cjpa\"\n        }\n    ]\n];\nconst Send = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"send\", __iconNode);\n //# sourceMappingURL=send.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC41MjMuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9zZW5kLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLE1BQU0sVUFBdUI7SUFDbEM7UUFDRTtRQUNBO1lBQ0UsQ0FBRztZQUNILEdBQUs7UUFBQTtLQUVUO0lBQ0E7UUFBQyxDQUFRO1FBQUEsQ0FBRTtZQUFBLEVBQUcsNkJBQThCO1lBQUEsSUFBSztRQUFVO0tBQUE7Q0FDN0Q7QUFhTSxXQUFPLGtFQUFpQixTQUFRLENBQVUiLCJzb3VyY2VzIjpbIkc6XFxzcmNcXGljb25zXFxzZW5kLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgW1xuICAgICdwYXRoJyxcbiAgICB7XG4gICAgICBkOiAnTTE0LjUzNiAyMS42ODZhLjUuNSAwIDAgMCAuOTM3LS4wMjRsNi41LTE5YS40OTYuNDk2IDAgMCAwLS42MzUtLjYzNWwtMTkgNi41YS41LjUgMCAwIDAtLjAyNC45MzdsNy45MyAzLjE4YTIgMiAwIDAgMSAxLjExMiAxLjExeicsXG4gICAgICBrZXk6ICcxZmZ4eTMnLFxuICAgIH0sXG4gIF0sXG4gIFsncGF0aCcsIHsgZDogJ20yMS44NTQgMi4xNDctMTAuOTQgMTAuOTM5Jywga2V5OiAnMTJjanBhJyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBTZW5kXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVFF1TlRNMklESXhMalk0Tm1FdU5TNDFJREFnTUNBd0lDNDVNemN0TGpBeU5HdzJMalV0TVRsaExqUTVOaTQwT1RZZ01DQXdJREF0TGpZek5TMHVOak0xYkMweE9TQTJMalZoTGpVdU5TQXdJREFnTUMwdU1ESTBMamt6TjJ3M0xqa3pJRE11TVRoaE1pQXlJREFnTUNBeElERXVNVEV5SURFdU1URjZJaUF2UGdvZ0lEeHdZWFJvSUdROUltMHlNUzQ0TlRRZ01pNHhORGN0TVRBdU9UUWdNVEF1T1RNNUlpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3NlbmRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBTZW5kID0gY3JlYXRlTHVjaWRlSWNvbignc2VuZCcsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBTZW5kO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/noticias/[id]/revision/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RevisionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,Edit3,ExternalLink,FileText,Plus,RotateCcw,Save,Send,User,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RevisionPage() {\n    var _session_user, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = Array.isArray(params.id) ? params.id[0] : params.id;\n    const [noticia, setNoticia] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [versiones, setVersiones] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [expandedVersions, setExpandedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Set());\n    // Estados para generación incremental\n    const [diarios, setDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [showGenerateMore, setShowGenerateMore] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedNewDiarios, setSelectedNewDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Estados para edición de versiones\n    const [editingVersion, setEditingVersion] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        titulo: '',\n        volanta: '',\n        resumen: '',\n        contenido: ''\n    });\n    const [isRegenerating, setIsRegenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        status,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (session && id) {\n                loadData();\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        session,\n        id\n    ]);\n    // Debug: monitorear cambios en selectedNewDiarios\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            console.log('selectedNewDiarios changed:', selectedNewDiarios);\n        }\n    }[\"RevisionPage.useEffect\"], [\n        selectedNewDiarios\n    ]);\n    // Función para iniciar edición de versión\n    const startEditVersion = (version)=>{\n        setEditingVersion(version.id);\n        setEditForm({\n            titulo: version.titulo,\n            volanta: version.volanta || '',\n            resumen: version.resumen || '',\n            contenido: version.contenido\n        });\n    };\n    // Función para cancelar edición\n    const cancelEdit = ()=>{\n        setEditingVersion(null);\n        setEditForm({\n            titulo: '',\n            volanta: '',\n            resumen: '',\n            contenido: ''\n        });\n    };\n    // Función para guardar cambios de edición\n    const saveEditVersion = async (versionId)=>{\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions/\").concat(versionId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(editForm)\n            });\n            if (!response.ok) {\n                throw new Error('Error al actualizar la versión');\n            }\n            const updatedVersion = await response.json();\n            // Actualizar la versión en el estado\n            setVersiones((prev)=>prev.map((v)=>v.id === versionId ? updatedVersion : v));\n            // Limpiar estado de edición\n            cancelEdit();\n        } catch (error) {\n            console.error('Error saving version:', error);\n            alert('Error al guardar los cambios');\n        }\n    };\n    // Función para regenerar versión\n    const regenerateVersion = async (versionId)=>{\n        try {\n            setIsRegenerating(versionId);\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions/\").concat(versionId), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Error al regenerar la versión');\n            }\n            const regeneratedVersion = await response.json();\n            // Actualizar la versión en el estado\n            setVersiones((prev)=>prev.map((v)=>v.id === versionId ? regeneratedVersion : v));\n        } catch (error) {\n            console.error('Error regenerating version:', error);\n            alert('Error al regenerar la versión');\n        } finally{\n            setIsRegenerating(null);\n        }\n    };\n    // TODO: Add edit functions back\n    const loadData = async ()=>{\n        try {\n            // Cargar noticia\n            const noticiaResponse = await fetch(\"/api/noticias/\".concat(id));\n            if (noticiaResponse.ok) {\n                const noticiaData = await noticiaResponse.json();\n                setNoticia(noticiaData);\n            }\n            // Cargar versiones\n            const versionesResponse = await fetch(\"/api/noticias/\".concat(id, \"/versions\"));\n            if (versionesResponse.ok) {\n                const versionesData = await versionesResponse.json();\n                setVersiones(versionesData.versiones);\n                // Expandir todas las versiones por defecto\n                setExpandedVersions(new Set(versionesData.versiones.map((v)=>v.id)));\n            }\n            // Cargar diarios disponibles\n            const diariosResponse = await fetch('/api/diarios');\n            if (diariosResponse.ok) {\n                const diariosData = await diariosResponse.json();\n                setDiarios(diariosData.diarios);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos:', error);\n            alert('Error al cargar los datos');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVersionStateChange = async (versionId, estado)=>{\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    versionId,\n                    estado\n                })\n            });\n            if (response.ok) {\n                await loadData(); // Recargar datos\n                alert(\"✅ Estado actualizado a: \".concat(estado));\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al actualizar estado:', error);\n            alert('Error al actualizar el estado de la versión');\n        }\n    };\n    const toggleVersionExpansion = (versionId)=>{\n        const newExpanded = new Set(expandedVersions);\n        if (newExpanded.has(versionId)) {\n            newExpanded.delete(versionId);\n        } else {\n            newExpanded.add(versionId);\n        }\n        setExpandedVersions(newExpanded);\n    };\n    const getEstadoBadge = (estado)=>{\n        const badges = {\n            'GENERADA': 'bg-blue-100 text-blue-800',\n            'APROBADA': 'bg-green-100 text-green-800',\n            'RECHAZADA': 'bg-red-100 text-red-800',\n            'EN_REVISION': 'bg-yellow-100 text-yellow-800'\n        };\n        return badges[estado] || 'bg-gray-100 text-gray-800';\n    };\n    const getEstadoIcon = (estado)=>{\n        switch(estado){\n            case 'APROBADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 16\n                }, this);\n            case 'RECHAZADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 16\n                }, this);\n            case 'EN_REVISION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // Obtener diarios que ya tienen versiones generadas\n    const getDiariosConVersiones = ()=>{\n        return versiones.map((v)=>v.diario.id);\n    };\n    // Obtener diarios disponibles para generar (que no tienen versiones)\n    const getDiariosDisponibles = ()=>{\n        const diariosConVersiones = getDiariosConVersiones();\n        return diarios.filter((d)=>!diariosConVersiones.includes(d.id));\n    };\n    // Manejar generación incremental\n    const handleGenerateMore = async ()=>{\n        console.log('selectedNewDiarios:', selectedNewDiarios);\n        if (selectedNewDiarios.length === 0) {\n            alert('Selecciona al menos un diario para generar versiones');\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/generate-versions\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    diarioIds: selectedNewDiarios\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                alert(\"✅ \".concat(data.generadas, \" versiones generadas exitosamente\"));\n                // Recargar datos para mostrar las nuevas versiones\n                await loadData();\n                // Limpiar selección y cerrar modal\n                setSelectedNewDiarios([]);\n                setShowGenerateMore(false);\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al generar versiones:', error);\n            alert('Error al generar las versiones');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando revisi\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 345,\n            columnNumber: 7\n        }, this);\n    }\n    if (!noticia) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Noticia no encontrada\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 356,\n            columnNumber: 7\n        }, this);\n    }\n    // Verificar permisos\n    const canReview = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'ADMIN' || (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role) === 'EDITOR';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.back(),\n                                        className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Volver\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Revisi\\xf3n de Noticia\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"Revisa y gestiona las versiones generadas por IA\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 text-sm font-medium rounded-full \".concat(noticia.estado === 'BORRADOR' ? 'bg-gray-100 text-gray-800' : noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800' : noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                    children: noticia.estado\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:grid lg:grid-cols-12 gap-6 lg:gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-7 xl:col-span-8 order-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-4 bg-blue-50 border-b border-blue-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold text-blue-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-6 w-6 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Noticia Original\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 text-sm text-blue-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    noticia.user.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    new Date(noticia.createdAt).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Categor\\xeda\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    style: {\n                                                                        color: noticia.categoria.color\n                                                                    },\n                                                                    children: noticia.categoria.nombre\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Estado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: noticia.estado.replace('_', ' ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Destacada\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: noticia.destacada ? 'Sí' : 'No'\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        noticia.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Volanta\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-600 font-medium uppercase tracking-wide\",\n                                                                    children: noticia.volanta\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"T\\xedtulo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl font-bold text-gray-900 leading-tight\",\n                                                                    children: noticia.titulo\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        noticia.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Subt\\xedtulo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg text-gray-700 font-medium\",\n                                                                    children: noticia.subtitulo\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        noticia.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Resumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-base text-gray-700 leading-relaxed\",\n                                                                    children: noticia.resumen\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        noticia.imagenUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Imagen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: noticia.imagenUrl,\n                                                                    alt: noticia.imagenAlt || noticia.titulo,\n                                                                    className: \"w-full max-w-2xl h-64 object-cover rounded-lg mt-2\",\n                                                                    onError: (e)=>{\n                                                                        e.currentTarget.style.display = 'none';\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Contenido\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none\",\n                                                                    children: noticia.contenido\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-4 border-t border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                                children: [\n                                                                    noticia.autor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Autor:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 514,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \" \",\n                                                                            noticia.autor\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 513,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    noticia.fuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Fuente:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 519,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \" \",\n                                                                            noticia.fuente\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    noticia.urlFuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"md:col-span-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"URL Fuente:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            ' ',\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                href: noticia.urlFuente,\n                                                                                target: \"_blank\",\n                                                                                rel: \"noopener noreferrer\",\n                                                                                className: \"text-blue-600 hover:text-blue-800 underline\",\n                                                                                children: noticia.urlFuente\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 525,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-5 xl:col-span-4 order-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-bold text-gray-900 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Versiones IA (\",\n                                                    versiones.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Versiones reescritas autom\\xe1ticamente.\",\n                                                    canReview && ' Puedes aprobar o rechazar cada una.'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 13\n                                    }, this),\n                                    diarios.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 bg-white shadow rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-base font-semibold text-gray-900 mb-3\",\n                                                        children: \"Estado por Diario\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    getDiariosDisponibles().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowGenerateMore(!showGenerateMore),\n                                                        className: \"w-full flex items-center justify-center space-x-2 px-3 py-2 text-xs font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Generar M\\xe1s\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 gap-3 mb-4\",\n                                                children: diarios.map((diario)=>{\n                                                    const tieneVersion = getDiariosConVersiones().includes(diario.id);\n                                                    const version = versiones.find((v)=>v.diario.id === diario.id);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg border \".concat(tieneVersion ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: diario.nombre\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 589,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    tieneVersion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            tieneVersion && version ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getEstadoBadge(version.estado)),\n                                                                        children: version.estado\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: new Date(version.createdAt).toLocaleDateString('es-ES', {\n                                                                            day: '2-digit',\n                                                                            month: 'short'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 27\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Pendiente\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, diario.id, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this),\n                                            showGenerateMore && getDiariosDisponibles().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-semibold text-gray-900 mb-3\",\n                                                        children: \"Seleccionar Diarios\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 mb-4\",\n                                                        children: getDiariosDisponibles().map((diario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: selectedNewDiarios.includes(diario.id),\n                                                                        onChange: (e)=>{\n                                                                            if (e.target.checked) {\n                                                                                setSelectedNewDiarios([\n                                                                                    ...selectedNewDiarios,\n                                                                                    diario.id\n                                                                                ]);\n                                                                            } else {\n                                                                                setSelectedNewDiarios(selectedNewDiarios.filter((id)=>id !== diario.id));\n                                                                            }\n                                                                        },\n                                                                        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                                children: diario.nombre\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 640,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            diario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: diario.descripcion\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 642,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, diario.id, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 text-center\",\n                                                                children: [\n                                                                    selectedNewDiarios.length,\n                                                                    \" de \",\n                                                                    getDiariosDisponibles().length,\n                                                                    \" seleccionados\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleGenerateMore,\n                                                                        disabled: isGenerating || selectedNewDiarios.length === 0,\n                                                                        className: \"w-full flex items-center justify-center space-x-2 px-3 py-2 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 661,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Generando...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 662,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 666,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Generar\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 667,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowGenerateMore(false),\n                                                                        className: \"w-full px-3 py-2 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 19\n                                            }, this),\n                                            getDiariosDisponibles().length === 0 && versiones.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4 bg-green-50 rounded-lg border border-green-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-8 w-8 text-green-600 mx-auto mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-green-800\",\n                                                        children: \"\\xa1Todas las versiones han sido generadas!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-600\",\n                                                        children: \"Se han creado versiones para todos los diarios disponibles.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, this),\n                                    versiones.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white shadow rounded-lg p-6 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-400 mx-auto mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-900 mb-2\",\n                                                children: \"No hay versiones\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: \"Las versiones aparecer\\xe1n aqu\\xed una vez generadas.\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this),\n                    versiones.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-6 w-6 mr-2 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Versiones Generadas por IA (\",\n                                            versiones.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: [\n                                            \"Versiones reescritas autom\\xe1ticamente para diferentes diarios.\",\n                                            canReview && ' Puedes aprobar o rechazar cada versión individualmente.'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: versiones.map((version, index)=>{\n                                    var _session_user, _session_user1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-green-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-4 bg-green-50 border-b border-green-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>toggleVersionExpansion(version.id),\n                                                                        className: \"flex items-center space-x-2 text-green-900 hover:text-green-700 transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    getEstadoIcon(version.estado),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"text-lg font-bold\",\n                                                                                        children: version.diario.nombre\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 736,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 734,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            expandedVersions.has(version.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 741,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 743,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 text-xs font-medium rounded-full \".concat(getEstadoBadge(version.estado)),\n                                                                        children: version.estado\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 747,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            canReview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>startEditVersion(version),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors\",\n                                                                        title: \"Editar versi\\xf3n\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 761,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Editar\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 762,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>regenerateVersion(version.id),\n                                                                        disabled: isRegenerating === version.id,\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-purple-700 bg-purple-100 hover:bg-purple-200 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                                                        title: \"Regenerar con IA\",\n                                                                        children: [\n                                                                            isRegenerating === version.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-purple-700\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 771,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 773,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: isRegenerating === version.id ? 'Regenerando...' : 'Regenerar'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 775,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 764,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'APROBADA'),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 783,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Aprobar\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 784,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'RECHAZADA'),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 790,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Rechazar\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 791,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'EN_REVISION'),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 rounded-md transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 797,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"En Revisi\\xf3n\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 798,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center space-x-4 text-sm text-green-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Generado por \",\n                                                                    version.usuario.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 811,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    new Date(version.createdAt).toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 810,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            version.metadatos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    version.metadatos.tokens_usados,\n                                                                    \" tokens\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 805,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 19\n                                            }, this),\n                                            expandedVersions.has(version.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    editingVersion === version.id ? /* Formulario de edición */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                                        children: \"Editando versi\\xf3n\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveEditVersion(version.id),\n                                                                                className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 836,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Guardar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 837,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 832,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: cancelEdit,\n                                                                                className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 843,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Cancelar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 844,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 839,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1\",\n                                                                        children: \"Volanta\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 851,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: editForm.volanta,\n                                                                        onChange: (e)=>setEditForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    volanta: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"Volanta (opcional)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 854,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1\",\n                                                                        children: \"T\\xedtulo *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 865,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: editForm.titulo,\n                                                                        onChange: (e)=>setEditForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    titulo: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg font-semibold\",\n                                                                        placeholder: \"T\\xedtulo de la noticia\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 868,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1\",\n                                                                        children: \"Resumen\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 880,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        value: editForm.resumen,\n                                                                        onChange: (e)=>setEditForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    resumen: e.target.value\n                                                                                })),\n                                                                        rows: 3,\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"Resumen de la noticia (opcional)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 883,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 879,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1\",\n                                                                        children: \"Contenido *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 894,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        value: editForm.contenido,\n                                                                        onChange: (e)=>setEditForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    contenido: e.target.value\n                                                                                })),\n                                                                        rows: 12,\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"Contenido completo de la noticia\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 897,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 25\n                                                    }, this) : /* Vista normal */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            version.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Volanta\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 912,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-600 font-medium uppercase tracking-wide\",\n                                                                        children: version.volanta\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 913,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"T\\xedtulo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 920,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-xl font-bold text-gray-900 leading-tight\",\n                                                                        children: version.titulo\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 921,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 919,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            version.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Resumen\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 928,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-base text-gray-700 leading-relaxed\",\n                                                                        children: version.resumen\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 929,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 927,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Contenido\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 936,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none\",\n                                                                        children: version.contenido\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 937,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 935,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    version.metadatos && (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 border-t border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"Informaci\\xf3n T\\xe9cnica\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 947,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Modelo:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 950,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \" \",\n                                                                            version.metadatos.modelo\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 949,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Tokens:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 953,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \" \",\n                                                                            version.metadatos.tokens_usados\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 952,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Tiempo:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 956,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \" \",\n                                                                            version.metadatos.tiempo_generacion,\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 955,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 948,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 946,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    version.promptUsado && (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role) === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 border-t border-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                            className: \"group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                                    className: \"cursor-pointer text-xs font-medium text-gray-500 uppercase tracking-wide hover:text-gray-700\",\n                                                                    children: \"Ver Prompt Utilizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 p-3 bg-gray-50 rounded text-sm text-gray-700 font-mono whitespace-pre-wrap\",\n                                                                    children: version.promptUsado\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 969,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 965,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 964,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    version.diario.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 border-t border-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: version.diario.url,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"inline-flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_Edit3_ExternalLink_FileText_Plus_RotateCcw_Save_Send_User_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 985,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Ver en \",\n                                                                        version.diario.nombre\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 979,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 978,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 825,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, version.id, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, this);\n}\n_s(RevisionPage, \"JUFuksO6VxQ/86xqSLqaPbDOoMY=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = RevisionPage;\nvar _c;\n$RefreshReg$(_c, \"RevisionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx\n"));

/***/ })

});