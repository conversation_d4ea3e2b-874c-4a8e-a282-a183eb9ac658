/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/noticias/[id]/versions/[versionId]/route";
exports.ids = ["app/api/noticias/[id]/versions/[versionId]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_noticias_id_versions_versionId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/noticias/[id]/versions/[versionId]/route.ts */ \"(rsc)/./src/app/api/noticias/[id]/versions/[versionId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/noticias/[id]/versions/[versionId]/route\",\n        pathname: \"/api/noticias/[id]/versions/[versionId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/noticias/[id]/versions/[versionId]/route\"\n    },\n    resolvedPagePath: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\api\\\\noticias\\\\[id]\\\\versions\\\\[versionId]\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_noticias_id_versions_versionId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFDUTtBQUV6QyxNQUFNRSxVQUFVRixnREFBUUEsQ0FBQ0Msa0RBQVdBO0FBRU8iLCJzb3VyY2VzIjpbIkc6XFxERUwgU1VSIEZJTkFMXFxwYW5lbCB1bmlmaWNhZG8gdmVyc2lvbiAyXFxzcmNcXGFwcFxcYXBpXFxhdXRoXFxbLi4ubmV4dGF1dGhdXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTmV4dEF1dGggZnJvbSAnbmV4dC1hdXRoJztcclxuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tICdAL2xpYi9hdXRoJztcclxuXHJcbmNvbnN0IGhhbmRsZXIgPSBOZXh0QXV0aChhdXRoT3B0aW9ucyk7XHJcblxyXG5leHBvcnQgeyBoYW5kbGVyIGFzIEdFVCwgaGFuZGxlciBhcyBQT1NUIH07ICJdLCJuYW1lcyI6WyJOZXh0QXV0aCIsImF1dGhPcHRpb25zIiwiaGFuZGxlciIsIkdFVCIsIlBPU1QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/noticias/[id]/versions/[versionId]/route.ts":
/*!*****************************************************************!*\
  !*** ./src/app/api/noticias/[id]/versions/[versionId]/route.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/openai */ \"(rsc)/./src/lib/openai.ts\");\n\n\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_3__.PrismaClient();\n// PUT - Update AI-generated version\nasync function PUT(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        const id = parseInt(params.id);\n        const versionId = parseInt(params.versionId);\n        if (isNaN(id) || isNaN(versionId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID inválido'\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        const { titulo, volanta, resumen, contenido } = body;\n        // Verificar que la versión existe y pertenece a la noticia\n        const existingVersion = await prisma.versionNoticia.findFirst({\n            where: {\n                id: versionId,\n                noticiaId: id\n            }\n        });\n        if (!existingVersion) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Versión no encontrada'\n            }, {\n                status: 404\n            });\n        }\n        // Actualizar la versión\n        const updatedVersion = await prisma.versionNoticia.update({\n            where: {\n                id: versionId\n            },\n            data: {\n                titulo: titulo || existingVersion.titulo,\n                volanta: volanta !== undefined ? volanta : existingVersion.volanta,\n                resumen: resumen !== undefined ? resumen : existingVersion.resumen,\n                contenido: contenido || existingVersion.contenido,\n                updatedAt: new Date()\n            },\n            include: {\n                diario: true,\n                usuario: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedVersion);\n    } catch (error) {\n        console.error('Error updating version:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Regenerate AI version\nasync function POST(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        const id = parseInt(params.id);\n        const versionId = parseInt(params.versionId);\n        if (isNaN(id) || isNaN(versionId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID inválido'\n            }, {\n                status: 400\n            });\n        }\n        // Obtener la noticia original y la versión existente\n        const [noticia, existingVersion] = await Promise.all([\n            prisma.noticia.findUnique({\n                where: {\n                    id\n                },\n                include: {\n                    categoria: true\n                }\n            }),\n            prisma.versionNoticia.findFirst({\n                where: {\n                    id: versionId,\n                    noticiaId: id\n                },\n                include: {\n                    diario: true\n                }\n            })\n        ]);\n        if (!noticia || !existingVersion) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Noticia o versión no encontrada'\n            }, {\n                status: 404\n            });\n        }\n        // Regenerar usando el prompt del diario\n        const rewriteResponse = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_4__.rewriteNoticia)({\n            titulo: noticia.titulo,\n            subtitulo: noticia.subtitulo,\n            volanta: noticia.volanta,\n            contenido: noticia.contenido,\n            resumen: noticia.resumen,\n            prompt: existingVersion.diario.prompt,\n            diarioNombre: existingVersion.diario.nombre\n        });\n        // Actualizar la versión con el contenido regenerado\n        const updatedVersion = await prisma.versionNoticia.update({\n            where: {\n                id: versionId\n            },\n            data: {\n                titulo: rewriteResponse.titulo,\n                volanta: rewriteResponse.volanta,\n                resumen: rewriteResponse.resumen,\n                contenido: rewriteResponse.contenido,\n                metadatos: rewriteResponse.metadatos,\n                promptUsado: existingVersion.diario.prompt,\n                updatedAt: new Date()\n            },\n            include: {\n                diario: true,\n                usuario: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedVersion);\n    } catch (error) {\n        console.error('Error regenerating version:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error al regenerar la versión'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/noticias/[id]/versions/[versionId]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const user = await prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        }\n                    });\n                    if (!user) {\n                        return null;\n                    }\n                    const isPasswordValid = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        return null;\n                    }\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error during authentication:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    },\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rewriteNoticia: () => (/* binding */ rewriteNoticia),\n/* harmony export */   testOpenAIConnection: () => (/* binding */ testOpenAIConnection)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/.pnpm/openai@5.10.1_zod@3.25.75/node_modules/openai/index.mjs\");\n\n// Configuración de OpenAI\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY || 'your-openai-api-key-here'\n});\nasync function rewriteNoticia(request) {\n    const startTime = Date.now();\n    try {\n        // Construir el prompt completo\n        const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- NO generes subtítulo, solo volanta, título, resumen y contenido\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n        const completion = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional.\"\n                },\n                {\n                    role: \"user\",\n                    content: fullPrompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 2000\n        });\n        const responseText = completion.choices[0]?.message?.content;\n        if (!responseText) {\n            throw new Error('No se recibió respuesta de OpenAI');\n        }\n        // Intentar parsear la respuesta JSON\n        let parsedResponse;\n        try {\n            parsedResponse = JSON.parse(responseText);\n        } catch (parseError) {\n            // Si falla el parsing, intentar extraer JSON del texto\n            const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                parsedResponse = JSON.parse(jsonMatch[0]);\n            } else {\n                throw new Error('La respuesta de OpenAI no está en formato JSON válido');\n            }\n        }\n        const endTime = Date.now();\n        const generationTime = endTime - startTime;\n        return {\n            titulo: parsedResponse.titulo || request.titulo,\n            volanta: parsedResponse.volanta,\n            contenido: parsedResponse.contenido || request.contenido,\n            resumen: parsedResponse.resumen,\n            metadatos: {\n                modelo: completion.model,\n                tokens_usados: completion.usage?.total_tokens || 0,\n                tiempo_generacion: generationTime,\n                diario: request.diarioNombre\n            }\n        };\n    } catch (error) {\n        console.error('Error en rewriteNoticia:', error);\n        throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);\n    }\n}\nasync function testOpenAIConnection() {\n    try {\n        const completion = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: [\n                {\n                    role: \"user\",\n                    content: \"Responde solo con 'OK'\"\n                }\n            ],\n            max_tokens: 5\n        });\n        return completion.choices[0]?.message?.content?.includes('OK') || false;\n    } catch (error) {\n        console.error('Error testing OpenAI connection:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@babel+runtime@7.27.6","vendor-chunks/jose@4.15.9","vendor-chunks/openid-client@5.7.1","vendor-chunks/oauth@0.9.15","vendor-chunks/object-hash@2.2.0","vendor-chunks/preact@10.26.9","vendor-chunks/uuid@8.3.2","vendor-chunks/yallist@4.0.0","vendor-chunks/preact-render-to-string@5.2.6_preact@10.26.9","vendor-chunks/lru-cache@6.0.0","vendor-chunks/cookie@0.7.2","vendor-chunks/oidc-token-hash@5.1.0","vendor-chunks/@panva+hkdf@1.2.1","vendor-chunks/openai@5.10.1_zod@3.25.75"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fversions%2F%5BversionId%5D%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();