import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed de la base de datos...');

  // Limpiar la base de datos
  await prisma.noticia.deleteMany();
  await prisma.categoria.deleteMany();
  await prisma.correccion.deleteMany();
  await prisma.user.deleteMany();

  // Crear usuarios de prueba
  const adminPassword = await bcrypt.hash('admin123', 10);
  const editorPassword = await bcrypt.hash('editor123', 10);
  const userPassword = await bcrypt.hash('user123', 10);

  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Administrador',
      password: adminPassword,
      role: 'ADMIN',
    },
  });

  const editor = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON>',
      password: editorPassword,
      role: 'EDITOR',
    },
  });

  const user1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Usuario Prueba',
      password: userPassword,
      role: 'USER',
    },
  });

  console.log('✅ Usuarios creados:', { admin: admin.email, editor: editor.email, user: user1.email });

  // Crear categorías de noticias
  const categorias = await prisma.categoria.createMany({
    data: [
      { nombre: 'Política', descripcion: 'Noticias políticas y de gobierno', color: '#EF4444' },
      { nombre: 'Deportes', descripcion: 'Noticias deportivas nacionales e internacionales', color: '#22D3EE' },
      { nombre: 'Tecnología', descripcion: 'Avances y novedades tecnológicas', color: '#6366F1' },
      { nombre: 'Salud', descripcion: 'Noticias sobre salud y medicina', color: '#10B981' },
      { nombre: 'Economía', descripcion: 'Noticias económicas y financieras', color: '#F59E42' },
    ],
  });

  const categoriasDb = await prisma.categoria.findMany();

  // Crear noticias de ejemplo
  const noticias = [
    {
      titulo: 'Nuevo presidente asume el cargo',
      subtitulo: 'Ceremonia oficial en la capital',
      contenido: 'El nuevo presidente asumió el cargo en una ceremonia oficial que contó con la presencia de líderes internacionales y representantes de todos los sectores políticos. Durante su discurso inaugural, el mandatario enfatizó la importancia de la unidad nacional y el trabajo conjunto para enfrentar los desafíos que se presentan.',
      resumen: 'El país tiene nuevo presidente tras las elecciones.',
      imagenUrl: 'https://placehold.co/600x400/EF4444/FFFFFF?text=Política',
      imagenAlt: 'Presidente asumiendo el cargo',
      autor: 'Redacción Política',
      fuente: 'Agencia Nacional',
      urlFuente: 'https://agencianacional.com/politica',
      estado: 'PUBLICADA',
      destacada: true,
      publicada: true,
      fechaPublicacion: new Date('2024-06-01'),
      categoriaId: categoriasDb.find(c => c.nombre === 'Política')?.id,
      userId: admin.id,
    },
    {
      titulo: 'Equipo local gana el campeonato',
      subtitulo: 'Victoria histórica en la final',
      contenido: 'El equipo local se consagró campeón tras un partido emocionante que se definió en los últimos minutos. Los jugadores mostraron una determinación excepcional y el entrenador destacó el trabajo en equipo como clave del éxito. La ciudad entera celebró esta victoria histórica.',
      resumen: 'Celebración en la ciudad por el triunfo deportivo.',
      imagenUrl: 'https://placehold.co/600x400/22D3EE/FFFFFF?text=Deportes',
      imagenAlt: 'Equipo celebrando',
      autor: 'Deportes Hoy',
      fuente: 'Deportes TV',
      urlFuente: 'https://deportestv.com/final',
      estado: 'PUBLICADA',
      destacada: false,
      publicada: true,
      fechaPublicacion: new Date('2024-06-02'),
      categoriaId: categoriasDb.find(c => c.nombre === 'Deportes')?.id,
      userId: editor.id,
    },
    {
      titulo: 'Nuevo avance en inteligencia artificial',
      subtitulo: 'Tecnología que revoluciona el sector',
      contenido: 'Investigadores han desarrollado una IA capaz de procesar y analizar datos médicos con una precisión sin precedentes. Esta tecnología promete revolucionar el diagnóstico temprano de enfermedades y mejorar significativamente los resultados de los tratamientos médicos.',
      resumen: 'La IA sigue avanzando a pasos agigantados.',
      imagenUrl: 'https://placehold.co/600x400/6366F1/FFFFFF?text=Tecnología',
      imagenAlt: 'Imagen de inteligencia artificial',
      autor: 'Tech News',
      fuente: 'Tech Magazine',
      urlFuente: 'https://techmagazine.com/ia',
      estado: 'APROBADA',
      destacada: false,
      publicada: false,
      fechaPublicacion: new Date('2024-06-03'),
      categoriaId: categoriasDb.find(c => c.nombre === 'Tecnología')?.id,
      userId: user1.id,
    },
    {
      titulo: 'Nuevas recomendaciones de salud pública',
      subtitulo: 'Autoridades sanitarias emiten alerta',
      contenido: 'El ministerio de salud ha publicado nuevas recomendaciones para prevenir la propagación de enfermedades respiratorias durante la temporada de invierno. Las medidas incluyen vacunación, higiene personal y ventilación de espacios cerrados.',
      resumen: 'Recomendaciones para prevenir enfermedades.',
      imagenUrl: 'https://placehold.co/600x400/10B981/FFFFFF?text=Salud',
      imagenAlt: 'Médico dando recomendaciones',
      autor: 'Salud Hoy',
      fuente: 'Ministerio de Salud',
      urlFuente: 'https://minsalud.com/recomendaciones',
      estado: 'EN_REVISION',
      destacada: false,
      publicada: false,
      fechaPublicacion: new Date('2024-06-04'),
      categoriaId: categoriasDb.find(c => c.nombre === 'Salud')?.id,
      userId: admin.id,
    },
    {
      titulo: 'El dólar alcanza nuevo récord',
      subtitulo: 'Impacto en la economía nacional',
      contenido: 'El valor del dólar ha alcanzado un nuevo máximo histórico, generando preocupación entre los analistas económicos. Los expertos señalan que esta situación puede tener efectos significativos en la inflación y el poder adquisitivo de la población.',
      resumen: 'El dólar sube y afecta a los mercados.',
      imagenUrl: 'https://placehold.co/600x400/F59E42/FFFFFF?text=Economía',
      imagenAlt: 'Billetes de dólar',
      autor: 'Economía Global',
      fuente: 'Finanzas Hoy',
      urlFuente: 'https://finanzashoy.com/dolar',
      estado: 'BORRADOR',
      destacada: false,
      publicada: false,
      fechaPublicacion: new Date('2024-06-05'),
      categoriaId: categoriasDb.find(c => c.nombre === 'Economía')?.id,
      userId: editor.id,
    },
  ];

  for (const noticia of noticias) {
    await prisma.noticia.create({ data: noticia });
  }

  console.log('✅ Noticias de ejemplo creadas:', noticias.length);

  // Correcciones de ejemplo (opcional, para compatibilidad)
  const correcciones = [
    {
      titulo: 'Error en título de noticia deportiva',
      contenido: 'Se necesita corregir el título de la noticia sobre el partido de fútbol del domingo',
      medio: 'Diario Deportivo',
      fechaPublicacion: new Date('2024-01-15'),
      fechaCorreccion: new Date('2024-01-16'),
      estado: 'PENDIENTE' as const,
      prioridad: 'ALTA' as const,
      observaciones: 'Error crítico en el título principal',
      userId: admin.id,
    },
    {
      titulo: 'Corrección de datos económicos',
      contenido: 'Los datos del PIB mostrados en la gráfica son incorrectos',
      medio: 'Revista Económica',
      fechaPublicacion: new Date('2024-01-14'),
      fechaCorreccion: new Date('2024-01-15'),
      estado: 'EN_REVISION' as const,
      prioridad: 'URGENTE' as const,
      observaciones: 'Datos financieros incorrectos que pueden causar confusión',
      userId: user1.id,
    },
  ];

  for (const correccion of correcciones) {
    await prisma.correccion.create({ data: correccion });
  }

  console.log('✅ Correcciones de prueba creadas:', correcciones.length);

  console.log('🎉 Seed completado exitosamente!');
  console.log('\n📋 Credenciales de acceso:');
  console.log('👑 Admin: <EMAIL> / admin123');
  console.log('✏️  Editor: <EMAIL> / editor123');
  console.log('👤 Usuario: <EMAIL> / user123');
}

main()
  .catch((e) => {
    console.error('❌ Error durante el seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 