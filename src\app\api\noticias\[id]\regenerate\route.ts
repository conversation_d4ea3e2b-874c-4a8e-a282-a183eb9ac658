import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { generateNewsVersion } from '@/lib/ai-service';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que la noticia existe y está en estado EN_REVISION
    const noticia = await prisma.noticia.findUnique({
      where: { id },
      include: { user: true }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    if (noticia.estado !== 'EN_REVISION') {
      return NextResponse.json({ 
        error: 'Solo se pueden regenerar noticias en estado EN_REVISION' 
      }, { status: 400 });
    }

    // Verificar permisos (admin, editor, o propietario)
    const isAdmin = session.user?.role === 'ADMIN';
    const isEditor = session.user?.role === 'EDITOR';
    const isOwner = noticia.userId === session.user?.id;

    if (!isAdmin && !isEditor && !isOwner) {
      return NextResponse.json({ error: 'Sin permisos para regenerar versiones' }, { status: 403 });
    }

    // Obtener todos los diarios activos para regenerar
    const diarios = await prisma.diario.findMany({
      where: { isActive: true }
    });

    if (diarios.length === 0) {
      return NextResponse.json({ error: 'No hay diarios activos configurados' }, { status: 400 });
    }

    let versionesGeneradas = 0;
    let errores = [];

    // Generar versiones para cada diario
    for (const diario of diarios) {
      try {
        console.log(`🔄 Regenerando versión para ${diario.nombre}...`);
        
        await generateNewsVersion(noticia, diario, session.user?.id || 0);
        versionesGeneradas++;
        
        console.log(`✅ Versión regenerada para ${diario.nombre}`);
      } catch (error) {
        console.error(`❌ Error regenerando versión para ${diario.nombre}:`, error);
        errores.push(`${diario.nombre}: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      }
    }

    if (versionesGeneradas === 0) {
      return NextResponse.json({ 
        error: 'No se pudo regenerar ninguna versión',
        detalles: errores
      }, { status: 500 });
    }

    const mensaje = versionesGeneradas === diarios.length 
      ? `Se regeneraron ${versionesGeneradas} versiones exitosamente`
      : `Se regeneraron ${versionesGeneradas} de ${diarios.length} versiones. Algunos errores: ${errores.join(', ')}`;

    return NextResponse.json({
      message: mensaje,
      versionesGeneradas,
      totalDiarios: diarios.length,
      errores: errores.length > 0 ? errores : undefined
    });

  } catch (error) {
    console.error('Error al regenerar versiones:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
