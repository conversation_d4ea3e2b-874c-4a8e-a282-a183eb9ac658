'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { ArrowLeft, Users, Bot, Settings, BarChart3, Shield, Cpu } from 'lucide-react';

export default function AdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    } else if (session?.user?.role !== 'ADMIN') {
      router.push('/dashboard');
    }
  }, [status, session, router]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando panel de administración...</p>
        </div>
      </div>
    );
  }

  if (session?.user?.role !== 'ADMIN') {
    return null;
  }

  const adminSections = [
    {
      title: 'Gestión de Usuarios',
      description: 'Administrar usuarios, roles y permisos del sistema',
      icon: Users,
      href: '/admin/usuarios',
      color: 'bg-blue-500',
      stats: 'Usuarios activos'
    },
    {
      title: 'Configuración de Diarios',
      description: 'Gestionar diarios y prompts para reescritura automática',
      icon: Bot,
      href: '/admin/diarios',
      color: 'bg-green-500',
      stats: 'Diarios configurados'
    },
    {
      title: 'Proveedores de IA',
      description: 'Configurar OpenAI, Google Gemini y otros proveedores de IA',
      icon: Cpu,
      href: '/admin/ai-config',
      color: 'bg-purple-500',
      stats: 'Proveedores disponibles'
    },
    {
      title: 'Configuración General',
      description: 'Ajustes generales del sistema y configuraciones',
      icon: Settings,
      href: '/admin/configuracion',
      color: 'bg-purple-500',
      stats: 'Configuraciones'
    },
    {
      title: 'Reportes y Analytics',
      description: 'Estadísticas de uso y reportes del sistema',
      icon: BarChart3,
      href: '/admin/reportes',
      color: 'bg-orange-500',
      stats: 'Métricas disponibles'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/dashboard')}
                className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Volver al Dashboard
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Shield className="h-6 w-6 mr-2 text-red-600" />
                  Panel de Administración
                </h1>
                <p className="text-sm text-gray-600">
                  Gestión completa del sistema Panel Unificado V2
                </p>
              </div>
            </div>

            <div className="text-sm text-gray-600">
              Administrador: <span className="font-medium">{session.user.name}</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
          <h2 className="text-xl font-bold mb-2">
            Bienvenido al Panel de Administración
          </h2>
          <p className="text-blue-100">
            Desde aquí puedes gestionar todos los aspectos del sistema, incluyendo usuarios,
            configuración de IA, y ajustes generales.
          </p>
        </div>

        {/* Admin Sections Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {adminSections.map((section, index) => (
            <div
              key={index}
              onClick={() => router.push(section.href)}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer border border-gray-200 hover:border-gray-300"
            >
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className={`p-3 rounded-lg ${section.color} text-white mr-4`}>
                    <section.icon className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {section.title}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {section.stats}
                    </p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {section.description}
                </p>
                <div className="mt-4 flex items-center text-blue-600 text-sm font-medium">
                  Acceder →
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Resumen del Sistema
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">-</div>
              <div className="text-sm text-gray-600">Usuarios Totales</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">-</div>
              <div className="text-sm text-gray-600">Noticias Publicadas</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">-</div>
              <div className="text-sm text-gray-600">Versiones IA</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">3</div>
              <div className="text-sm text-gray-600">Diarios Activos</div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Actividad Reciente
          </h3>
          <div className="text-center py-8 text-gray-500">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p>Los registros de actividad aparecerán aquí</p>
          </div>
        </div>
      </main>
    </div>
  );
}