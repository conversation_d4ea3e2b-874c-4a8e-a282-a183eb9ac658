'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Correccion, CreateCorreccionData, UpdateCorreccionData } from '@/types/correccion';
import { CorreccionService } from '@/lib/correccionService';
import { X, Save, Loader2, Calendar, AlertCircle } from 'lucide-react';

interface CorreccionFormProps {
  correccion?: Correccion;
  onSave: (correccion: Correccion) => void;
  onCancel: () => void;
}

export default function CorreccionForm({ correccion, onSave, onCancel }: CorreccionFormProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const [formData, setFormData] = useState<CreateCorreccionData & UpdateCorreccionData>({
    titulo: '',
    contenido: '',
    medio: '',
    fechaPublicacion: new Date(),
    prioridad: 'MEDIA',
    observaciones: '',
  });

  const isEditing = !!correccion;

  useEffect(() => {
    if (correccion) {
      setFormData({
        titulo: correccion.titulo,
        contenido: correccion.contenido,
        medio: correccion.medio,
        fechaPublicacion: new Date(correccion.fechaPublicacion),
        prioridad: correccion.prioridad,
        estado: correccion.estado,
        observaciones: correccion.observaciones || '',
      });
    }
  }, [correccion]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      let savedCorreccion: Correccion;

      if (isEditing && correccion) {
        savedCorreccion = await CorreccionService.updateCorreccion(
          correccion.id.toString(),
          formData
        );
      } else {
        savedCorreccion = await CorreccionService.createCorreccion(formData as CreateCorreccionData);
      }

      onSave(savedCorreccion);
    } catch (err) {
      setError('Error al guardar la corrección');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900">
            {isEditing ? 'Editar Corrección' : 'Nueva Corrección'}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Título */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Título *
            </label>
            <input
              type="text"
              required
              value={formData.titulo}
              onChange={(e) => handleInputChange('titulo', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Título de la corrección"
            />
          </div>

          {/* Contenido */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Contenido *
            </label>
            <textarea
              required
              rows={4}
              value={formData.contenido}
              onChange={(e) => handleInputChange('contenido', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Descripción detallada de la corrección"
            />
          </div>

          {/* Medio */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Medio *
            </label>
            <input
              type="text"
              required
              value={formData.medio}
              onChange={(e) => handleInputChange('medio', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Nombre del medio de comunicación"
            />
          </div>

          {/* Fecha de Publicación */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fecha de Publicación *
            </label>
            <div className="relative">
              <input
                type="date"
                required
                value={formData.fechaPublicacion instanceof Date 
                  ? formData.fechaPublicacion.toISOString().split('T')[0]
                  : new Date(formData.fechaPublicacion).toISOString().split('T')[0]
                }
                onChange={(e) => handleInputChange('fechaPublicacion', new Date(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Calendar className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
          </div>

          {/* Prioridad */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prioridad *
            </label>
            <select
              required
              value={formData.prioridad}
              onChange={(e) => handleInputChange('prioridad', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="BAJA">Baja</option>
              <option value="MEDIA">Media</option>
              <option value="ALTA">Alta</option>
              <option value="URGENTE">Urgente</option>
            </select>
          </div>

          {/* Estado (solo para edición) */}
          {isEditing && session?.user.role === 'ADMIN' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estado
              </label>
              <select
                value={formData.estado || 'PENDIENTE'}
                onChange={(e) => handleInputChange('estado', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="PENDIENTE">Pendiente</option>
                <option value="EN_REVISION">En Revisión</option>
                <option value="COMPLETADA">Completada</option>
                <option value="RECHAZADA">Rechazada</option>
              </select>
            </div>
          )}

          {/* Observaciones */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Observaciones
            </label>
            <textarea
              rows={3}
              value={formData.observaciones}
              onChange={(e) => handleInputChange('observaciones', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Observaciones adicionales (opcional)"
            />
          </div>

          {/* Botones */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              {isEditing ? 'Actualizar' : 'Crear'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
} 