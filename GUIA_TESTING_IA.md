# 🧪 GUÍA DE TESTING - SISTEMA IA REESCRITURA
## Panel Unificado V2

---

## 🎯 OBJETIVO
Esta guía te ayudará a probar paso a paso todas las funcionalidades implementadas del sistema de reescritura automática con IA.

---

## ✅ CHECKLIST DE FUNCIONALIDADES A PROBAR

### **1. ACCESO AL PANEL DE ADMINISTRACIÓN**
- [ ] **Dashboard → Panel Admin** (botón nuevo agregado)
- [ ] **Página `/admin`** se carga correctamente
- [ ] **Configuración de IA** aparece en el grid
- [ ] **Click en "Configuración de IA"** lleva a `/admin/diarios`

### **2. CONFIGURACIÓN DE DIARIOS IA**
- [ ] **Página `/admin/diarios`** se carga
- [ ] **Se muestran 3 diarios** preconfigurados:
  - Telesoldiario
  - Zondadiario
  - Delsurdiario
- [ ] **Botón "Editar"** abre formulario de edición
- [ ] **Botón "Nuevo Diario"** abre formulario de creación
- [ ] **Prompts se pueden modificar** y guardar

### **3. FLUJO DE PUBLICACIÓN CON IA**
- [ ] **Crear nueva noticia** en estado BORRADOR
- [ ] **Abrir noticia creada** - debe mostrar botón "Publicar con IA"
- [ ] **Selector de diarios** aparece debajo del contenido
- [ ] **3 diarios están seleccionados** por defecto
- [ ] **Botones "Seleccionar/Deseleccionar todos"** funcionan
- [ ] **Click "Publicar con IA"** cambia estado a EN_REVISION

### **4. PÁGINA DE REVISIÓN**
- [ ] **Botón "Ver Revisión"** aparece cuando estado = EN_REVISION
- [ ] **Página `/noticias/[id]/revision`** se carga
- [ ] **Noticia original** se muestra arriba completa
- [ ] **Versiones generadas** se muestran abajo como elementos hijo
- [ ] **Versiones son expandibles/colapsables**
- [ ] **Botones Aprobar/Rechazar** funcionan (solo admin/editor)

### **5. GENERACIÓN CON IA (Requiere API Key)**
- [ ] **Configurar OPENAI_API_KEY** en `.env.local`
- [ ] **Proceso de generación** se ejecuta sin errores
- [ ] **Versiones se crean** en base de datos
- [ ] **Metadatos de generación** se muestran correctamente

---

## 🚀 PASOS DETALLADOS PARA TESTING

### **PASO 1: Verificar Acceso Admin**

1. **Iniciar sesión como admin:**
   - Email: `<EMAIL>`
   - Password: `admin123`

2. **Ir al Dashboard:**
   - Verificar que aparece el botón **"Panel Admin"**
   - Click en **"Panel Admin"**

3. **En la página `/admin`:**
   - Verificar que se muestra el grid con 4 secciones
   - Click en **"Configuración de IA"**

### **PASO 2: Configurar Diarios**

1. **En `/admin/diarios`:**
   - Verificar que se muestran 3 diarios preconfigurados
   - Click en **"Editar"** en cualquier diario
   - Modificar el prompt y guardar
   - Verificar que se actualiza correctamente

2. **Crear nuevo diario:**
   - Click en **"Nuevo Diario"**
   - Completar formulario:
     - Nombre: "Diario Test"
     - Descripción: "Diario de prueba"
     - Prompt: "Reescribe esta noticia para un diario de prueba..."
   - Guardar y verificar que aparece en la lista

### **PASO 3: Probar Flujo de Publicación**

1. **Crear noticia de prueba:**
   - Ir a **"Noticias" → "Nueva Noticia"**
   - Completar todos los campos:
     - Volanta: "Prueba IA"
     - Título: "Noticia de prueba para sistema IA"
     - Contenido: "Este es el contenido de prueba para verificar el sistema de reescritura automática con inteligencia artificial."
     - Categoría: Cualquiera
   - Guardar (queda en estado BORRADOR)

2. **Abrir la noticia creada:**
   - Click en la noticia desde la lista
   - Verificar que aparece el botón **"Publicar con IA"**
   - Verificar que aparece el **selector de diarios** debajo

3. **Configurar generación:**
   - Verificar que los diarios están seleccionados
   - Probar botones "Seleccionar/Deseleccionar todos"
   - Dejar al menos 1 diario seleccionado

4. **Publicar con IA:**
   - Click en **"Publicar con IA"**
   - Verificar que aparece "Generando..." con spinner
   - Esperar a que termine el proceso

### **PASO 4: Revisar Versiones Generadas**

1. **Después de la generación:**
   - Verificar que el estado cambió a **EN_REVISION**
   - Verificar que aparece el botón **"Ver Revisión"**
   - Click en **"Ver Revisión"**

2. **En la página de revisión:**
   - Verificar estructura jerárquica:
     - **Arriba:** Noticia original completa
     - **Abajo:** Versiones generadas
   - Click en cada versión para expandir/colapsar
   - Verificar que se muestra el contenido reescrito
   - Verificar metadatos de generación

3. **Aprobar/Rechazar versiones (como admin/editor):**
   - Click en **"Aprobar"** en una versión
   - Verificar que el estado cambia a "APROBADA"
   - Click en **"Rechazar"** en otra versión
   - Verificar que el estado cambia a "RECHAZADA"

---

## 🔧 CONFIGURACIÓN REQUERIDA

### **Variables de Entorno**
```env
# En .env.local
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3010
DATABASE_URL="file:./dev.db"

# REQUERIDO para funcionalidad IA
OPENAI_API_KEY=sk-tu-clave-de-openai-aqui
```

### **Usuarios de Prueba**
- **Admin:** <EMAIL> / admin123
- **Editor:** <EMAIL> / editor123
- **Usuario:** <EMAIL> / user123

---

## 🐛 PROBLEMAS COMUNES Y SOLUCIONES

### **1. No aparece el botón "Panel Admin"**
- **Causa:** Usuario no es admin
- **Solución:** Iniciar sesió<NAME_EMAIL>

### **2. No aparece el selector de diarios**
- **Causa:** Noticia no está en estado BORRADOR
- **Solución:** Crear nueva noticia o cambiar estado

### **3. Error al generar versiones**
- **Causa:** OPENAI_API_KEY no configurada
- **Solución:** Agregar clave válida en .env.local

### **4. No se muestran las versiones generadas**
- **Causa:** Error en la generación o base de datos
- **Solución:** Revisar logs del servidor y base de datos

### **5. Botones Aprobar/Rechazar no aparecen**
- **Causa:** Usuario no es admin/editor
- **Solución:** Iniciar sesión con rol apropiado

---

## 📊 VERIFICACIÓN DE BASE DE DATOS

### **Tablas a verificar:**
```sql
-- Diarios configurados
SELECT * FROM diarios;

-- Versiones generadas
SELECT * FROM versiones_noticias;

-- Estados de noticias
SELECT id, titulo, estado FROM noticias WHERE estado = 'EN_REVISION';
```

### **Usando Prisma Studio:**
```bash
pnpm db:studio
```

---

## ✅ CHECKLIST FINAL

- [ ] Panel de administración accesible
- [ ] Configuración de diarios funcional
- [ ] Selector de diarios visible
- [ ] Botón "Publicar con IA" funciona
- [ ] Estado cambia a EN_REVISION
- [ ] Página de revisión se carga
- [ ] Estructura jerárquica correcta
- [ ] Versiones expandibles/colapsables
- [ ] Botones aprobar/rechazar funcionan
- [ ] Metadatos se muestran correctamente

---

**¡Si todos los elementos están marcados, el sistema está funcionando correctamente!** ✅