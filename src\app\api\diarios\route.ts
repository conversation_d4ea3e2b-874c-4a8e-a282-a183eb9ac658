import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/diarios - Obtener diarios activos
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const diarios = await prisma.diario.findMany({
      where: { isActive: true },
      select: {
        id: true,
        nombre: true,
        descripcion: true,
        aiProvider: true,
        aiModel: true,
        useGlobalConfig: true,
      },
      orderBy: { nombre: 'asc' },
    });

    return NextResponse.json({ diarios });

  } catch (error) {
    console.error('Error al obtener diarios:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}