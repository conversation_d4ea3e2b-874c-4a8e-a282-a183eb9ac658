import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/correcciones/[id] - Obtener una corrección específica
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const correccion = await prisma.correccion.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    if (!correccion) {
      return NextResponse.json({ error: 'Corrección no encontrada' }, { status: 404 });
    }

    // Verificar permisos: solo admin puede ver todas, usuarios solo las suyas
    if (session.user.role !== 'ADMIN' && correccion.userId !== parseInt(session.user.id)) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 403 });
    }

    return NextResponse.json(correccion);
  } catch (error) {
    console.error('Error al obtener corrección:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/correcciones/[id] - Actualizar una corrección
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que la corrección existe y el usuario tiene permisos
    const existingCorreccion = await prisma.correccion.findUnique({
      where: { id },
    });

    if (!existingCorreccion) {
      return NextResponse.json({ error: 'Corrección no encontrada' }, { status: 404 });
    }

    // Verificar permisos: solo admin puede editar todas, usuarios solo las suyas
    if (session.user.role !== 'ADMIN' && existingCorreccion.userId !== parseInt(session.user.id)) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 403 });
    }

    const body = await request.json();
    const { titulo, contenido, medio, fechaPublicacion, estado, prioridad, observaciones } = body;

    const correccion = await prisma.correccion.update({
      where: { id },
      data: {
        ...(titulo && { titulo }),
        ...(contenido && { contenido }),
        ...(medio && { medio }),
        ...(fechaPublicacion && { fechaPublicacion: new Date(fechaPublicacion) }),
        ...(estado && { estado }),
        ...(prioridad && { prioridad }),
        ...(observaciones !== undefined && { observaciones }),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json(correccion);
  } catch (error) {
    console.error('Error al actualizar corrección:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/correcciones/[id] - Eliminar una corrección
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Solo admin puede eliminar correcciones
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 403 });
    }

    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const correccion = await prisma.correccion.findUnique({
      where: { id },
    });

    if (!correccion) {
      return NextResponse.json({ error: 'Corrección no encontrada' }, { status: 404 });
    }

    await prisma.correccion.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Corrección eliminada exitosamente' });
  } catch (error) {
    console.error('Error al eliminar corrección:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 