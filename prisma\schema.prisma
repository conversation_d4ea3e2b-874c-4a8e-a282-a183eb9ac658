// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// Modelo de Usuario
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String
  password  String
  role      Role     @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  noticias Noticia[]
  correcciones Correccion[]
  boardMemberships BoardMember[]
  createdBoards Board[] @relation("BoardCreator")

  @@map("users")
}

// Modelo de Categoría
model Categoria {
  id          Int      @id @default(autoincrement())
  nombre      String   @unique
  descripcion String?
  color       String   @default("#3B82F6")
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  noticias Noticia[]

  @@map("categorias")
}

// Modelo de Noticia
model Noticia {
  id          Int      @id @default(autoincrement())
  titulo      String
  subtitulo   String?
  volanta     String?
  contenido   String
  resumen     String?
  imagenUrl   String?
  imagenAlt   String?
  autor       String?
  fuente      String?
  urlFuente   String?
  estado      EstadoNoticia @default(BORRADOR)
  destacada   Boolean  @default(false)
  publicada   Boolean  @default(false)
  fechaPublicacion DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  categoriaId Int?
  categoria   Categoria? @relation(fields: [categoriaId], references: [id])
  userId      Int
  user        User @relation(fields: [userId], references: [id])

  @@map("noticias")
}

// Modelo de Corrección (mantenido para compatibilidad)
model Correccion {
  id                Int           @id @default(autoincrement())
  titulo            String
  contenido         String
  medio             String
  fechaPublicacion  DateTime
  fechaCorreccion   DateTime
  estado            Estado        @default(PENDIENTE)
  prioridad         Prioridad     @default(MEDIA)
  imagenUrl         String?
  observaciones     String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relaciones
  userId Int
  user   User @relation(fields: [userId], references: [id])

  @@map("correcciones")
}

// Modelo de Board/Equipo
model Board {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  createdById Int
  createdBy   User @relation("BoardCreator", fields: [createdById], references: [id])
  members     BoardMember[]
  noticias    Noticia[]

  @@map("boards")
}

// Modelo de Membresía de Board
model BoardMember {
  id        Int      @id @default(autoincrement())
  role      BoardRole @default(MEMBER)
  joinedAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  userId  Int
  user    User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  boardId Int
  board   Board @relation(fields: [boardId], references: [id], onDelete: Cascade)

  // Un usuario solo puede tener un rol por board
  @@unique([userId, boardId])
  @@map("board_members")
}

// Enum para roles de usuario
enum Role {
  ADMIN
  EDITOR
  USER
}

// Enum para estados de noticia
enum EstadoNoticia {
  BORRADOR
  EN_REVISION
  APROBADA
  PUBLICADA
  ARCHIVADA
}

// Enum para estados de corrección
enum Estado {
  PENDIENTE
  EN_REVISION
  COMPLETADA
  RECHAZADA
}

// Enum para prioridades
enum Prioridad {
  BAJA
  MEDIA
  ALTA
  URGENTE
}
