"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/noticias/[id]/page",{

/***/ "(app-pages-browser)/./src/app/noticias/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/noticias/[id]/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NoticiaDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,FileText,Send,Trash2,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NoticiaDetailPage() {\n    var _noticia_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = Array.isArray(params.id) ? params.id[0] : params.id;\n    const [noticia, setNoticia] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [diarios, setDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedDiarios, setSelectedDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [versiones, setVersiones] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [showVersiones, setShowVersiones] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [loadingVersiones, setLoadingVersiones] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NoticiaDetailPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"NoticiaDetailPage.useEffect\"], [\n        status,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NoticiaDetailPage.useEffect\": ()=>{\n            if (session && id) {\n                loadNoticia();\n                loadDiarios();\n            }\n        }\n    }[\"NoticiaDetailPage.useEffect\"], [\n        session,\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NoticiaDetailPage.useEffect\": ()=>{\n            if (noticia && noticia.estado === 'EN_REVISION') {\n                loadVersiones();\n            }\n        }\n    }[\"NoticiaDetailPage.useEffect\"], [\n        noticia\n    ]);\n    const loadNoticia = async ()=>{\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id));\n            if (response.ok) {\n                const data = await response.json();\n                setNoticia(data);\n            } else {\n                alert('No se pudo cargar la noticia');\n                router.push('/noticias');\n            }\n        } catch (error) {\n            console.error('Error al cargar noticia:', error);\n            alert('Error al cargar la noticia');\n            router.push('/noticias');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDiarios = async ()=>{\n        try {\n            const response = await fetch('/api/diarios');\n            if (response.ok) {\n                const data = await response.json();\n                setDiarios(data.diarios);\n                // Seleccionar todos los diarios por defecto\n                setSelectedDiarios(data.diarios.map((d)=>d.id));\n            }\n        } catch (error) {\n            console.error('Error al cargar diarios:', error);\n        }\n    };\n    const loadVersiones = async ()=>{\n        if (!id) return;\n        setLoadingVersiones(true);\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions\"));\n            if (response.ok) {\n                const data = await response.json();\n                setVersiones(data.versiones);\n                if (data.versiones.length > 0) {\n                    setShowVersiones(true);\n                }\n            }\n        } catch (error) {\n            console.error('Error al cargar versiones:', error);\n        } finally{\n            setLoadingVersiones(false);\n        }\n    };\n    const handleGenerateVersions = async ()=>{\n        if (selectedDiarios.length === 0) {\n            alert('Selecciona al menos un diario para generar versiones');\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/generate-versions\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    diarioIds: selectedDiarios\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                alert(\"✅ \".concat(data.message));\n                // Recargar la noticia para actualizar el estado\n                await loadNoticia();\n                // Cargar las versiones generadas\n                await loadVersiones();\n            } else {\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al generar versiones:', error);\n            alert('Error al generar versiones con IA');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handleVersionStateChange = async (versionId, estado)=>{\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    versionId,\n                    estado\n                })\n            });\n            if (response.ok) {\n                // Recargar versiones\n                await loadVersiones();\n                alert(\"✅ Estado actualizado a: \".concat(estado));\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al actualizar estado:', error);\n            alert('Error al actualizar el estado de la versión');\n        }\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta noticia?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                alert('Noticia eliminada exitosamente');\n                router.push('/noticias');\n            } else {\n                alert('Error al eliminar la noticia');\n            }\n        } catch (error) {\n            console.error('Error al eliminar noticia:', error);\n            alert('Error al eliminar la noticia');\n        }\n    };\n    const getEstadoColor = (estado)=>{\n        switch(estado){\n            case 'PUBLICADA':\n                return 'bg-green-100 text-green-800';\n            case 'APROBADA':\n                return 'bg-blue-100 text-blue-800';\n            case 'EN_REVISION':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'BORRADOR':\n                return 'bg-gray-100 text-gray-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getEstadoIcon = (estado)=>{\n        switch(estado){\n            case 'PUBLICADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 16\n                }, this);\n            case 'APROBADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            case 'EN_REVISION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n            case 'BORRADOR':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (status === 'loading' || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session || !noticia) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/noticias'),\n                                        className: \"mr-4 text-gray-600 hover:text-gray-900\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Detalles de la Noticia\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/noticias/\".concat(id, \"/editar\")),\n                                        className: \"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Editar\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    noticia.estado === 'BORRADOR' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleGenerateVersions,\n                                        disabled: isGenerating,\n                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Generando...\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Publicar con IA\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this),\n                                    noticia.estado === 'EN_REVISION' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRegenerateWithAI,\n                                        disabled: isGenerating,\n                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Regenerando...\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Regenerar con IA\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDelete,\n                                        className: \"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-red-700 hover:text-red-900 hover:bg-red-50 rounded-md transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Eliminar\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 sm:px-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium \".concat(getEstadoColor(noticia.estado)),\n                                                            children: [\n                                                                getEstadoIcon(noticia.estado),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-1\",\n                                                                    children: noticia.estado.replace('_', ' ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        noticia.destacada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Destacada\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"Creada el \",\n                                                        new Date(noticia.createdAt).toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: noticia.titulo\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        noticia.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600 mb-4\",\n                                            children: noticia.subtitulo\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                            children: [\n                                                noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium\",\n                                                    style: {\n                                                        backgroundColor: \"\".concat(noticia.categoria.color, \"20\"),\n                                                        color: noticia.categoria.color\n                                                    },\n                                                    children: noticia.categoria.nombre\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this),\n                                                noticia.autor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"por \",\n                                                        noticia.autor\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this),\n                                                noticia.fuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Fuente: \",\n                                                        noticia.fuente\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this),\n                                                noticia.fechaPublicacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Publicada: \",\n                                                        new Date(noticia.fechaPublicacion).toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this),\n                                noticia.imagenUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: noticia.imagenUrl,\n                                        alt: noticia.imagenAlt || noticia.titulo,\n                                        className: \"w-full h-64 object-cover rounded-lg\",\n                                        onError: (e)=>{\n                                            e.currentTarget.style.display = 'none';\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: [\n                                        noticia.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                    children: \"Resumen\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 leading-relaxed\",\n                                                    children: noticia.resumen\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                    children: \"Contenido\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-700 leading-relaxed whitespace-pre-wrap\",\n                                                    children: noticia.contenido\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this),\n                                        noticia.urlFuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: noticia.urlFuente,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                                children: \"Ver fuente original →\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 bg-white shadow rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Informaci\\xf3n Adicional\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Autor:\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: noticia.autor || 'No especificado'\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Fuente:\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: noticia.fuente || 'No especificada'\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Creada por:\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: ((_noticia_user = noticia.user) === null || _noticia_user === void 0 ? void 0 : _noticia_user.name) || 'Usuario'\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Estado:\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: noticia.estado.replace('_', ' ')\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Destacada:\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: noticia.destacada ? 'Sí' : 'No'\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Publicada:\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: noticia.publicada ? 'Sí' : 'No'\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this),\n                        noticia.estado === 'BORRADOR' && diarios.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 bg-white shadow rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Configurar Generaci\\xf3n con IA\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-4\",\n                                    children: \"Selecciona los diarios para los cuales quieres generar versiones reescritas autom\\xe1ticamente:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: diarios.map((diario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: selectedDiarios.includes(diario.id),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setSelectedDiarios([\n                                                                ...selectedDiarios,\n                                                                diario.id\n                                                            ]);\n                                                        } else {\n                                                            setSelectedDiarios(selectedDiarios.filter((id)=>id !== diario.id));\n                                                        }\n                                                    },\n                                                    className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: diario.nombre\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        diario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: diario.descripcion\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, diario.id, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                selectedDiarios.length,\n                                                \" de \",\n                                                diarios.length,\n                                                \" diarios seleccionados\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedDiarios([]),\n                                                    className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                                    children: \"Deseleccionar todos\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedDiarios(diarios.map((d)=>d.id)),\n                                                    className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                                    children: \"Seleccionar todos\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 13\n                        }, this),\n                        (noticia.estado === 'EN_REVISION' || versiones.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 bg-white shadow rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowVersiones(!showVersiones),\n                                        className: \"flex items-center justify-between w-full text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Versiones Generadas por IA (\",\n                                                    versiones.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this),\n                                            showVersiones ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_FileText_Send_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this),\n                                showVersiones && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: loadingVersiones ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-600\",\n                                                children: \"Cargando versiones...\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 21\n                                    }, this) : versiones.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"No hay versiones generadas a\\xfan.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: versiones.map((version, index)=>{\n                                            var _session_user, _session_user1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900\",\n                                                                        children: version.diario.nombre\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(version.estado === 'GENERADA' ? 'bg-blue-100 text-blue-800' : version.estado === 'APROBADA' ? 'bg-green-100 text-green-800' : version.estado === 'RECHAZADA' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                        children: version.estado\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'ADMIN' || (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role) === 'EDITOR') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'APROBADA'),\n                                                                        className: \"px-3 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded\",\n                                                                        children: \"Aprobar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'RECHAZADA'),\n                                                                        className: \"px-3 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded\",\n                                                                        children: \"Rechazar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            version.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Volanta\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-700 font-medium\",\n                                                                        children: version.volanta\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"T\\xedtulo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-lg font-bold text-gray-900\",\n                                                                        children: version.titulo\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            version.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Subt\\xedtulo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-base text-gray-700\",\n                                                                        children: version.subtitulo\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            version.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Resumen\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: version.resumen\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Contenido\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-700 whitespace-pre-wrap mt-1\",\n                                                                        children: version.contenido\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            version.metadatos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 pt-4 border-t border-gray-100\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Informaci\\xf3n de Generaci\\xf3n\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-1 text-xs text-gray-500 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    \"Modelo: \",\n                                                                                    version.metadatos.modelo\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 641,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    \"Tokens usados: \",\n                                                                                    version.metadatos.tokens_usados\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 642,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    \"Tiempo: \",\n                                                                                    version.metadatos.tiempo_generacion,\n                                                                                    \"ms\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    \"Generado por: \",\n                                                                                    version.usuario.name\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 644,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    \"Fecha: \",\n                                                                                    new Date(version.createdAt).toLocaleString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 645,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 640,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, version.id, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 25\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\page.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(NoticiaDetailPage, \"H1HA8ycm5GLUrFI6KeISM3mQf+Q=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = NoticiaDetailPage;\nvar _c;\n$RefreshReg$(_c, \"NoticiaDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/noticias/[id]/page.tsx\n"));

/***/ })

});