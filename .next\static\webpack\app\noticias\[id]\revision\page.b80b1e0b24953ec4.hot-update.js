"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/noticias/[id]/revision/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ArrowLeft)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m12 19-7-7 7-7\",\n            key: \"1l729n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 12H5\",\n            key: \"x3x0zl\"\n        }\n    ]\n];\nconst ArrowLeft = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"arrow-left\", __iconNode);\n //# sourceMappingURL=arrow-left.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/noticias/[id]/revision/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RevisionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,CheckCircle,Clock,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RevisionPage() {\n    var _session_user, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = Array.isArray(params.id) ? params.id[0] : params.id;\n    const [noticia, setNoticia] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [versiones, setVersiones] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [expandedVersions, setExpandedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Set());\n    // Estados para generación incremental\n    const [diarios, setDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [showGenerateMore, setShowGenerateMore] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedNewDiarios, setSelectedNewDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Estados para edición de versiones\n    const [editingVersion, setEditingVersion] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        titulo: '',\n        volanta: '',\n        resumen: '',\n        contenido: ''\n    });\n    const [isRegenerating, setIsRegenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        status,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (session && id) {\n                loadData();\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        session,\n        id\n    ]);\n    // Debug: monitorear cambios en selectedNewDiarios\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            console.log('selectedNewDiarios changed:', selectedNewDiarios);\n        }\n    }[\"RevisionPage.useEffect\"], [\n        selectedNewDiarios\n    ]);\n    // Función para iniciar edición de versión\n    const startEditVersion = (version)=>{\n        setEditingVersion(version.id);\n        setEditForm({\n            titulo: version.titulo,\n            volanta: version.volanta || '',\n            resumen: version.resumen || '',\n            contenido: version.contenido\n        });\n    };\n    // Función para cancelar edición\n    const cancelEdit = ()=>{\n        setEditingVersion(null);\n        setEditForm({\n            titulo: '',\n            volanta: '',\n            resumen: '',\n            contenido: ''\n        });\n    };\n    // Función para guardar cambios de edición\n    const saveEditVersion = async (versionId)=>{\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions/\").concat(versionId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(editForm)\n            });\n            if (!response.ok) {\n                throw new Error('Error al actualizar la versión');\n            }\n            const updatedVersion = await response.json();\n            // Actualizar la versión en el estado\n            setVersiones((prev)=>prev.map((v)=>v.id === versionId ? updatedVersion : v));\n            // Limpiar estado de edición\n            cancelEdit();\n        } catch (error) {\n            console.error('Error saving version:', error);\n            alert('Error al guardar los cambios');\n        }\n    };\n    // Función para regenerar versión\n    const regenerateVersion = async (versionId)=>{\n        try {\n            setIsRegenerating(versionId);\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions/\").concat(versionId), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Error al regenerar la versión');\n            }\n            const regeneratedVersion = await response.json();\n            // Actualizar la versión en el estado\n            setVersiones((prev)=>prev.map((v)=>v.id === versionId ? regeneratedVersion : v));\n        } catch (error) {\n            console.error('Error regenerating version:', error);\n            alert('Error al regenerar la versión');\n        } finally{\n            setIsRegenerating(null);\n        }\n    };\n    // TODO: Add edit functions back\n    const loadData = async ()=>{\n        try {\n            // Cargar noticia\n            const noticiaResponse = await fetch(\"/api/noticias/\".concat(id));\n            if (noticiaResponse.ok) {\n                const noticiaData = await noticiaResponse.json();\n                setNoticia(noticiaData);\n            }\n            // Cargar versiones\n            const versionesResponse = await fetch(\"/api/noticias/\".concat(id, \"/versions\"));\n            if (versionesResponse.ok) {\n                const versionesData = await versionesResponse.json();\n                setVersiones(versionesData.versiones);\n                // Expandir todas las versiones por defecto\n                setExpandedVersions(new Set(versionesData.versiones.map((v)=>v.id)));\n            }\n            // Cargar diarios disponibles\n            const diariosResponse = await fetch('/api/diarios');\n            if (diariosResponse.ok) {\n                const diariosData = await diariosResponse.json();\n                setDiarios(diariosData.diarios);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos:', error);\n            alert('Error al cargar los datos');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVersionStateChange = async (versionId, estado)=>{\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    versionId,\n                    estado\n                })\n            });\n            if (response.ok) {\n                await loadData(); // Recargar datos\n                alert(\"✅ Estado actualizado a: \".concat(estado));\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al actualizar estado:', error);\n            alert('Error al actualizar el estado de la versión');\n        }\n    };\n    const toggleVersionExpansion = (versionId)=>{\n        const newExpanded = new Set(expandedVersions);\n        if (newExpanded.has(versionId)) {\n            newExpanded.delete(versionId);\n        } else {\n            newExpanded.add(versionId);\n        }\n        setExpandedVersions(newExpanded);\n    };\n    const getEstadoBadge = (estado)=>{\n        const badges = {\n            'GENERADA': 'bg-blue-100 text-blue-800',\n            'APROBADA': 'bg-green-100 text-green-800',\n            'RECHAZADA': 'bg-red-100 text-red-800',\n            'EN_REVISION': 'bg-yellow-100 text-yellow-800'\n        };\n        return badges[estado] || 'bg-gray-100 text-gray-800';\n    };\n    const getEstadoIcon = (estado)=>{\n        switch(estado){\n            case 'APROBADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 16\n                }, this);\n            case 'RECHAZADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 16\n                }, this);\n            case 'EN_REVISION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // Obtener diarios que ya tienen versiones generadas\n    const getDiariosConVersiones = ()=>{\n        return versiones.map((v)=>v.diario.id);\n    };\n    // Obtener diarios disponibles para generar (que no tienen versiones)\n    const getDiariosDisponibles = ()=>{\n        const diariosConVersiones = getDiariosConVersiones();\n        return diarios.filter((d)=>!diariosConVersiones.includes(d.id));\n    };\n    // Manejar generación incremental\n    const handleGenerateMore = async ()=>{\n        console.log('selectedNewDiarios:', selectedNewDiarios);\n        if (selectedNewDiarios.length === 0) {\n            alert('Selecciona al menos un diario para generar versiones');\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/generate-versions\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    diarioIds: selectedNewDiarios\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                alert(\"✅ \".concat(data.generadas, \" versiones generadas exitosamente\"));\n                // Recargar datos para mostrar las nuevas versiones\n                await loadData();\n                // Limpiar selección y cerrar modal\n                setSelectedNewDiarios([]);\n                setShowGenerateMore(false);\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al generar versiones:', error);\n            alert('Error al generar las versiones');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando revisi\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 345,\n            columnNumber: 7\n        }, this);\n    }\n    if (!noticia) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Noticia no encontrada\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 356,\n            columnNumber: 7\n        }, this);\n    }\n    // Verificar permisos\n    const canReview = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'ADMIN' || (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role) === 'EDITOR';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.back(),\n                                        className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Volver\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Revisi\\xf3n de Noticia\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"Revisa y gestiona las versiones generadas por IA\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 text-sm font-medium rounded-full \".concat(noticia.estado === 'BORRADOR' ? 'bg-gray-100 text-gray-800' : noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800' : noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                    children: noticia.estado\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"P\\xe1gina en construcci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"Las versiones aparecer\\xe1n aqu\\xed pronto.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, this);\n}\n_s(RevisionPage, \"JUFuksO6VxQ/86xqSLqaPbDOoMY=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = RevisionPage;\nvar _c;\n$RefreshReg$(_c, \"RevisionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx\n"));

/***/ })

});