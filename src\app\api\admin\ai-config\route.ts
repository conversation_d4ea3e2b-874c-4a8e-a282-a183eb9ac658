import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { testOpenAIConnection, testGeminiConnection } from '@/lib/ai-service';

const prisma = new PrismaClient();

// GET - Obtener configuración de IA
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const config = await prisma.aIConfig.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    });

    if (!config) {
      // Retornar configuración por defecto
      return NextResponse.json({
        openaiApiKey: process.env.OPENAI_API_KEY ? '***' : null,
        openaiModel: 'gpt-3.5-turbo',
        openaiMaxTokens: 2000,
        openaiTemperature: 0.7,
        geminiApiKey: process.env.GEMINI_API_KEY ? '***' : null,
        geminiModel: 'gemini-pro',
        geminiMaxTokens: 2000,
        geminiTemperature: 0.7,
        defaultProvider: 'OPENAI',
        isActive: true
      });
    }

    // Ocultar las API keys completas por seguridad
    return NextResponse.json({
      ...config,
      openaiApiKey: config.openaiApiKey ? '***' : null,
      geminiApiKey: config.geminiApiKey ? '***' : null
    });

  } catch (error) {
    console.error('Error getting AI config:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear o actualizar configuración de IA
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const {
      openaiApiKey,
      openaiModel,
      openaiMaxTokens,
      openaiTemperature,
      geminiApiKey,
      geminiModel,
      geminiMaxTokens,
      geminiTemperature,
      defaultProvider
    } = body;

    // Validaciones básicas
    if (!openaiModel || !geminiModel || !defaultProvider) {
      return NextResponse.json(
        { error: 'Campos requeridos faltantes' },
        { status: 400 }
      );
    }

    if (!['OPENAI', 'GEMINI'].includes(defaultProvider)) {
      return NextResponse.json(
        { error: 'Proveedor por defecto inválido' },
        { status: 400 }
      );
    }

    // Desactivar configuraciones anteriores
    await prisma.aIConfig.updateMany({
      where: { isActive: true },
      data: { isActive: false }
    });

    // Crear nueva configuración
    const newConfig = await prisma.aIConfig.create({
      data: {
        openaiApiKey: openaiApiKey === '***' ? undefined : openaiApiKey,
        openaiModel,
        openaiMaxTokens: parseInt(openaiMaxTokens) || 2000,
        openaiTemperature: parseFloat(openaiTemperature) || 0.7,
        geminiApiKey: geminiApiKey === '***' ? undefined : geminiApiKey,
        geminiModel,
        geminiMaxTokens: parseInt(geminiMaxTokens) || 2000,
        geminiTemperature: parseFloat(geminiTemperature) || 0.7,
        defaultProvider,
        isActive: true
      }
    });

    // Ocultar las API keys en la respuesta
    return NextResponse.json({
      ...newConfig,
      openaiApiKey: newConfig.openaiApiKey ? '***' : null,
      geminiApiKey: newConfig.geminiApiKey ? '***' : null
    });

  } catch (error) {
    console.error('Error saving AI config:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
