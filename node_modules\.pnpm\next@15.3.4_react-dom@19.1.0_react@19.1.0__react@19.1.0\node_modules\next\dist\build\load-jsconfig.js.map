{"version": 3, "sources": ["../../src/build/load-jsconfig.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport * as Log from './output/log'\nimport { getTypeScriptConfiguration } from '../lib/typescript/getTypeScriptConfiguration'\nimport { readFileSync } from 'fs'\nimport isError from '../lib/is-error'\nimport { hasNecessaryDependencies } from '../lib/has-necessary-dependencies'\n\nlet TSCONFIG_WARNED = false\n\nexport function parseJsonFile(filePath: string) {\n  const JSON5 = require('next/dist/compiled/json5')\n  const contents = readFileSync(filePath, 'utf8')\n\n  // Special case an empty file\n  if (contents.trim() === '') {\n    return {}\n  }\n\n  try {\n    return JSON5.parse(contents)\n  } catch (err) {\n    if (!isError(err)) throw err\n    const { codeFrameColumns } = require('next/dist/compiled/babel/code-frame')\n    const codeFrame = codeFrameColumns(\n      String(contents),\n      {\n        start: {\n          line: (err as Error & { lineNumber?: number }).lineNumber || 0,\n          column: (err as Error & { columnNumber?: number }).columnNumber || 0,\n        },\n      },\n      { message: err.message, highlightCode: true }\n    )\n    throw new Error(`Failed to parse \"${filePath}\":\\n${codeFrame}`)\n  }\n}\n\nexport type ResolvedBaseUrl =\n  | { baseUrl: string; isImplicit: boolean }\n  | undefined\n\nexport type JsConfig = { compilerOptions: Record<string, any> } | undefined\n\nexport default async function loadJsConfig(\n  dir: string,\n  config: NextConfigComplete\n): Promise<{\n  useTypeScript: boolean\n  jsConfig: JsConfig\n  jsConfigPath?: string\n  resolvedBaseUrl: ResolvedBaseUrl\n}> {\n  let typeScriptPath: string | undefined\n  try {\n    const deps = await hasNecessaryDependencies(dir, [\n      {\n        pkg: 'typescript',\n        file: 'typescript/lib/typescript.js',\n        exportsRestrict: true,\n      },\n    ])\n    typeScriptPath = deps.resolved.get('typescript')\n  } catch {}\n  const tsConfigPath = path.join(dir, config.typescript.tsconfigPath)\n  const useTypeScript = Boolean(typeScriptPath && fs.existsSync(tsConfigPath))\n\n  let implicitBaseurl\n  let jsConfig: { compilerOptions: Record<string, any> } | undefined\n  // jsconfig is a subset of tsconfig\n  if (useTypeScript) {\n    if (\n      config.typescript.tsconfigPath !== 'tsconfig.json' &&\n      TSCONFIG_WARNED === false\n    ) {\n      TSCONFIG_WARNED = true\n      Log.info(`Using tsconfig file: ${config.typescript.tsconfigPath}`)\n    }\n\n    const ts = (await Promise.resolve(\n      require(typeScriptPath!)\n    )) as typeof import('typescript')\n    const tsConfig = await getTypeScriptConfiguration(ts, tsConfigPath, true)\n    jsConfig = { compilerOptions: tsConfig.options }\n    implicitBaseurl = path.dirname(tsConfigPath)\n  }\n\n  const jsConfigPath = path.join(dir, 'jsconfig.json')\n  if (!useTypeScript && fs.existsSync(jsConfigPath)) {\n    jsConfig = parseJsonFile(jsConfigPath)\n    implicitBaseurl = path.dirname(jsConfigPath)\n  }\n\n  let resolvedBaseUrl: ResolvedBaseUrl\n  if (jsConfig?.compilerOptions?.baseUrl) {\n    resolvedBaseUrl = {\n      baseUrl: path.resolve(dir, jsConfig.compilerOptions.baseUrl),\n      isImplicit: false,\n    }\n  } else {\n    if (implicitBaseurl) {\n      resolvedBaseUrl = {\n        baseUrl: implicitBaseurl,\n        isImplicit: true,\n      }\n    }\n  }\n\n  return {\n    useTypeScript,\n    jsConfig,\n    resolvedBaseUrl,\n    jsConfigPath: useTypeScript\n      ? tsConfigPath\n      : fs.existsSync(jsConfigPath)\n        ? jsConfigPath\n        : undefined,\n  }\n}\n"], "names": ["loadJsConfig", "parseJsonFile", "TSCONFIG_WARNED", "filePath", "JSON5", "require", "contents", "readFileSync", "trim", "parse", "err", "isError", "codeFrameColumns", "codeFrame", "String", "start", "line", "lineNumber", "column", "columnNumber", "message", "highlightCode", "Error", "dir", "config", "jsConfig", "typeScriptPath", "deps", "hasNecessaryDependencies", "pkg", "file", "exportsRestrict", "resolved", "get", "tsConfigPath", "path", "join", "typescript", "tsconfigPath", "useTypeScript", "Boolean", "fs", "existsSync", "implicit<PERSON><PERSON><PERSON>l", "Log", "info", "ts", "Promise", "resolve", "tsConfig", "getTypeScriptConfiguration", "compilerOptions", "options", "dirname", "jsConfigPath", "resolvedBaseUrl", "baseUrl", "isImplicit", "undefined"], "mappings": ";;;;;;;;;;;;;;;IA6CA,OA0EC;eA1E6BA;;IAlCdC,aAAa;eAAbA;;;6DAXC;4DACF;6DAEM;4CACsB;gEAEvB;0CACqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,IAAIC,kBAAkB;AAEf,SAASD,cAAcE,QAAgB;IAC5C,MAAMC,QAAQC,QAAQ;IACtB,MAAMC,WAAWC,IAAAA,gBAAY,EAACJ,UAAU;IAExC,6BAA6B;IAC7B,IAAIG,SAASE,IAAI,OAAO,IAAI;QAC1B,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOJ,MAAMK,KAAK,CAACH;IACrB,EAAE,OAAOI,KAAK;QACZ,IAAI,CAACC,IAAAA,gBAAO,EAACD,MAAM,MAAMA;QACzB,MAAM,EAAEE,gBAAgB,EAAE,GAAGP,QAAQ;QACrC,MAAMQ,YAAYD,iBAChBE,OAAOR,WACP;YACES,OAAO;gBACLC,MAAM,AAACN,IAAwCO,UAAU,IAAI;gBAC7DC,QAAQ,AAACR,IAA0CS,YAAY,IAAI;YACrE;QACF,GACA;YAAEC,SAASV,IAAIU,OAAO;YAAEC,eAAe;QAAK;QAE9C,MAAM,qBAAyD,CAAzD,IAAIC,MAAM,CAAC,iBAAiB,EAAEnB,SAAS,IAAI,EAAEU,WAAW,GAAxD,qBAAA;mBAAA;wBAAA;0BAAA;QAAwD;IAChE;AACF;AAQe,eAAeb,aAC5BuB,GAAW,EACXC,MAA0B;QAgDtBC;IAzCJ,IAAIC;IACJ,IAAI;QACF,MAAMC,OAAO,MAAMC,IAAAA,kDAAwB,EAACL,KAAK;YAC/C;gBACEM,KAAK;gBACLC,MAAM;gBACNC,iBAAiB;YACnB;SACD;QACDL,iBAAiBC,KAAKK,QAAQ,CAACC,GAAG,CAAC;IACrC,EAAE,OAAM,CAAC;IACT,MAAMC,eAAeC,aAAI,CAACC,IAAI,CAACb,KAAKC,OAAOa,UAAU,CAACC,YAAY;IAClE,MAAMC,gBAAgBC,QAAQd,kBAAkBe,WAAE,CAACC,UAAU,CAACR;IAE9D,IAAIS;IACJ,IAAIlB;IACJ,mCAAmC;IACnC,IAAIc,eAAe;QACjB,IACEf,OAAOa,UAAU,CAACC,YAAY,KAAK,mBACnCpC,oBAAoB,OACpB;YACAA,kBAAkB;YAClB0C,KAAIC,IAAI,CAAC,CAAC,qBAAqB,EAAErB,OAAOa,UAAU,CAACC,YAAY,EAAE;QACnE;QAEA,MAAMQ,KAAM,MAAMC,QAAQC,OAAO,CAC/B3C,QAAQqB;QAEV,MAAMuB,WAAW,MAAMC,IAAAA,sDAA0B,EAACJ,IAAIZ,cAAc;QACpET,WAAW;YAAE0B,iBAAiBF,SAASG,OAAO;QAAC;QAC/CT,kBAAkBR,aAAI,CAACkB,OAAO,CAACnB;IACjC;IAEA,MAAMoB,eAAenB,aAAI,CAACC,IAAI,CAACb,KAAK;IACpC,IAAI,CAACgB,iBAAiBE,WAAE,CAACC,UAAU,CAACY,eAAe;QACjD7B,WAAWxB,cAAcqD;QACzBX,kBAAkBR,aAAI,CAACkB,OAAO,CAACC;IACjC;IAEA,IAAIC;IACJ,IAAI9B,6BAAAA,4BAAAA,SAAU0B,eAAe,qBAAzB1B,0BAA2B+B,OAAO,EAAE;QACtCD,kBAAkB;YAChBC,SAASrB,aAAI,CAACa,OAAO,CAACzB,KAAKE,SAAS0B,eAAe,CAACK,OAAO;YAC3DC,YAAY;QACd;IACF,OAAO;QACL,IAAId,iBAAiB;YACnBY,kBAAkB;gBAChBC,SAASb;gBACTc,YAAY;YACd;QACF;IACF;IAEA,OAAO;QACLlB;QACAd;QACA8B;QACAD,cAAcf,gBACVL,eACAO,WAAE,CAACC,UAAU,CAACY,gBACZA,eACAI;IACR;AACF"}