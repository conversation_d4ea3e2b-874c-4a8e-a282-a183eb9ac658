
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  password: 'password',
  role: 'role',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoriaScalarFieldEnum = {
  id: 'id',
  nombre: 'nombre',
  descripcion: 'descripcion',
  color: 'color',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NoticiaScalarFieldEnum = {
  id: 'id',
  titulo: 'titulo',
  subtitulo: 'subtitulo',
  volanta: 'volanta',
  contenido: 'contenido',
  resumen: 'resumen',
  imagenUrl: 'imagenUrl',
  imagenAlt: 'imagenAlt',
  autor: 'autor',
  fuente: 'fuente',
  urlFuente: 'urlFuente',
  estado: 'estado',
  destacada: 'destacada',
  publicada: 'publicada',
  fechaPublicacion: 'fechaPublicacion',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  categoriaId: 'categoriaId',
  userId: 'userId'
};

exports.Prisma.CorreccionScalarFieldEnum = {
  id: 'id',
  titulo: 'titulo',
  contenido: 'contenido',
  medio: 'medio',
  fechaPublicacion: 'fechaPublicacion',
  fechaCorreccion: 'fechaCorreccion',
  estado: 'estado',
  prioridad: 'prioridad',
  imagenUrl: 'imagenUrl',
  observaciones: 'observaciones',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.BoardScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdById: 'createdById'
};

exports.Prisma.BoardMemberScalarFieldEnum = {
  id: 'id',
  role: 'role',
  joinedAt: 'joinedAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  boardId: 'boardId'
};

exports.Prisma.DiarioScalarFieldEnum = {
  id: 'id',
  nombre: 'nombre',
  descripcion: 'descripcion',
  prompt: 'prompt',
  isActive: 'isActive',
  aiProvider: 'aiProvider',
  aiModel: 'aiModel',
  useGlobalConfig: 'useGlobalConfig',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VersionNoticiaScalarFieldEnum = {
  id: 'id',
  titulo: 'titulo',
  subtitulo: 'subtitulo',
  volanta: 'volanta',
  contenido: 'contenido',
  resumen: 'resumen',
  estado: 'estado',
  promptUsado: 'promptUsado',
  metadatos: 'metadatos',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  noticiaId: 'noticiaId',
  diarioId: 'diarioId',
  generadaPor: 'generadaPor'
};

exports.Prisma.AIConfigScalarFieldEnum = {
  id: 'id',
  openaiApiKey: 'openaiApiKey',
  openaiModel: 'openaiModel',
  openaiMaxTokens: 'openaiMaxTokens',
  openaiTemperature: 'openaiTemperature',
  geminiApiKey: 'geminiApiKey',
  geminiModel: 'geminiModel',
  geminiMaxTokens: 'geminiMaxTokens',
  geminiTemperature: 'geminiTemperature',
  defaultProvider: 'defaultProvider',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.Role = exports.$Enums.Role = {
  ADMIN: 'ADMIN',
  EDITOR: 'EDITOR',
  USER: 'USER'
};

exports.EstadoNoticia = exports.$Enums.EstadoNoticia = {
  BORRADOR: 'BORRADOR',
  EN_REVISION: 'EN_REVISION',
  APROBADA: 'APROBADA',
  PUBLICADA: 'PUBLICADA',
  ARCHIVADA: 'ARCHIVADA'
};

exports.Estado = exports.$Enums.Estado = {
  PENDIENTE: 'PENDIENTE',
  EN_REVISION: 'EN_REVISION',
  COMPLETADA: 'COMPLETADA',
  RECHAZADA: 'RECHAZADA'
};

exports.Prioridad = exports.$Enums.Prioridad = {
  BAJA: 'BAJA',
  MEDIA: 'MEDIA',
  ALTA: 'ALTA',
  URGENTE: 'URGENTE'
};

exports.BoardRole = exports.$Enums.BoardRole = {
  ADMIN: 'ADMIN',
  MEMBER: 'MEMBER'
};

exports.AIProvider = exports.$Enums.AIProvider = {
  OPENAI: 'OPENAI',
  GEMINI: 'GEMINI'
};

exports.EstadoVersion = exports.$Enums.EstadoVersion = {
  GENERADA: 'GENERADA',
  APROBADA: 'APROBADA',
  RECHAZADA: 'RECHAZADA',
  EN_REVISION: 'EN_REVISION'
};

exports.Prisma.ModelName = {
  User: 'User',
  Categoria: 'Categoria',
  Noticia: 'Noticia',
  Correccion: 'Correccion',
  Board: 'Board',
  BoardMember: 'BoardMember',
  Diario: 'Diario',
  VersionNoticia: 'VersionNoticia',
  AIConfig: 'AIConfig'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
