{"version": 3, "sources": ["../../src/server/node-environment.ts"], "sourcesContent": ["// This file should be imported before any others. It sets up the environment\n// for later imports to work properly.\n\nimport './node-environment-baseline'\n// Import as early as possible so that unexpected errors in other extensions are properly formatted.\n// Has to come after baseline since error-inspect requires AsyncLocalStorage that baseline provides.\nimport './node-environment-extensions/error-inspect'\nimport './node-environment-extensions/random'\nimport './node-environment-extensions/date'\nimport './node-environment-extensions/web-crypto'\nimport './node-environment-extensions/node-crypto'\n"], "names": [], "mappings": "AAAA,6EAA6E;AAC7E,sCAAsC;AAEtC,OAAO,8BAA6B;AACpC,oGAAoG;AACpG,oGAAoG;AACpG,OAAO,8CAA6C;AACpD,OAAO,uCAAsC;AAC7C,OAAO,qCAAoC;AAC3C,OAAO,2CAA0C;AACjD,OAAO,4CAA2C"}