# 📚 Documentación Completa del Sistema de IA - Panel Unificado v2

## 🎯 Resumen Ejecutivo

El Panel Unificado v2 cuenta con un **sistema completo de reescritura de noticias con IA** que soporta múltiples proveedores (OpenAI y Google Gemini) con configuración flexible por diario y global.

---

## 🏗️ Arquitectura del Sistema

### 📊 Base de Datos (Prisma Schema)

#### Modelos Principales:
- **`AIConfig`**: Configuración global de IA
- **`Diario`**: Configuración específica por diario
- **`NoticiaVersion`**: Versiones generadas por IA
- **`Noticia`**: Noticias principales con estados

#### Estados de Noticia:
- `BORRADOR` → `EN_REVISION` → `PUBLICADA` → `ARCHIVADA`

### 🔧 Servicios Core

#### `src/lib/ai-service.ts`
- **Configuración de IA**: Gestión de API keys y modelos
- **Integración OpenAI**: GPT-3.5, GPT-4, GPT-4o, O1
- **Integración Gemini**: Gemini 2.5, 2.0, 1.5
- **Pruebas de conexión**: Validación automática de APIs
- **Migración automática**: Actualización de modelos obsoletos

---

## 🚀 Funcionalidades Implementadas

### ✅ 1. Sistema de Reescritura con IA

#### Flujo de Trabajo:
1. **Creación**: Noticia en estado `BORRADOR`
2. **Activación**: Cambio a `EN_REVISION` activa IA
3. **Generación**: IA crea versiones automáticamente
4. **Revisión**: Editor selecciona versión final
5. **Publicación**: Estado `PUBLICADA`

#### Características:
- **Regeneración manual**: Botón "Regenerar con IA"
- **Múltiples versiones**: Historial completo
- **Configuración flexible**: Por diario o global
- **Fallback inteligente**: OpenAI ↔ Gemini

### ✅ 2. Configuración Multi-Proveedor

#### Proveedores Soportados:
- **OpenAI**: 12 modelos (GPT-3.5 a O1)
- **Google Gemini**: 9 modelos (1.5 a 2.5)

#### Configuración Global (`/admin/ai-config`):
- API Keys por proveedor
- Modelos predeterminados
- Parámetros (tokens, temperatura)
- Proveedor por defecto
- Pruebas de conexión

#### Configuración por Diario (`/admin/diarios`):
- Override de configuración global
- Prompts personalizados por diario
- Proveedor específico por diario
- Configuración heredada

### ✅ 3. Interface de Usuario

#### Panel de Revisión:
- **Vista jerárquica**: Original → Versiones IA
- **Comparación visual**: Lado a lado
- **Selección fácil**: Click para activar versión
- **Regeneración**: Botón contextual
- **Historial completo**: Todas las versiones

#### Panel de Administración:
- **Configuración global**: Centralizada
- **Gestión por diario**: Específica
- **Pruebas en vivo**: Validación inmediata
- **Monitoreo**: Logs y estados

---

## 🔧 Configuración Técnica

### 🌐 Variables de Entorno

```env
# OpenAI
OPENAI_API_KEY=sk-...

# Google Gemini
GEMINI_API_KEY=...

# Base de datos
DATABASE_URL="postgresql://..."

# NextAuth
NEXTAUTH_SECRET=...
NEXTAUTH_URL=http://localhost:3010
```

### 📦 Dependencias Principales

```json
{
  "@google/generative-ai": "^0.24.1",
  "openai": "^4.67.3",
  "@prisma/client": "^6.1.0",
  "next": "15.3.4",
  "react": "19.1.0"
}
```

### 🗄️ Schema de Base de Datos

```prisma
model AIConfig {
  id                Int      @id @default(autoincrement())
  openaiApiKey      String?
  openaiModel       String   @default("gpt-3.5-turbo")
  openaiMaxTokens   Int      @default(2000)
  openaiTemperature Float    @default(0.7)
  geminiApiKey      String?
  geminiModel       String   @default("gemini-2.5-flash")
  geminiMaxTokens   Int      @default(2000)
  geminiTemperature Float    @default(0.7)
  defaultProvider   AIProvider @default(OPENAI)
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

model Diario {
  id                Int      @id @default(autoincrement())
  nombre            String   @unique
  descripcion       String?
  promptPersonalizado String?
  aiProvider        AIProvider?
  aiModel           String?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  noticias          Noticia[]
}

model NoticiaVersion {
  id          Int      @id @default(autoincrement())
  noticiaId   Int
  titulo      String
  contenido   String
  aiProvider  AIProvider
  aiModel     String
  isSelected  Boolean  @default(false)
  createdAt   DateTime @default(now())
  noticia     Noticia  @relation(fields: [noticiaId], references: [id], onDelete: Cascade)
}

enum AIProvider {
  OPENAI
  GEMINI
}

enum EstadoNoticia {
  BORRADOR
  EN_REVISION
  PUBLICADA
  ARCHIVADA
}
```

---

## 📋 Modelos Disponibles

### 🤖 OpenAI Models (12 modelos)
```
- gpt-3.5-turbo, gpt-3.5-turbo-16k
- gpt-4, gpt-4-turbo, gpt-4-turbo-preview
- gpt-4o, gpt-4o-mini
- gpt-4o-2024-11-20, gpt-4o-2024-08-06, gpt-4o-2024-05-13
- o1-preview, o1-mini
```

### 🧠 Google Gemini Models (9 modelos)
```
Gemini 2.5 (Más Recientes):
- gemini-2.5-pro ⭐ (Más potente)
- gemini-2.5-flash ⭐ (Más rápido - PREDETERMINADO)
- gemini-2.5-flash-lite-preview-06-17

Gemini 2.0:
- gemini-2.0-flash
- gemini-2.0-flash-lite

Gemini 1.5 (Compatibilidad):
- gemini-1.5-flash
- gemini-1.5-flash-8b
- gemini-1.5-pro
```

---

## 🔄 Flujos de Trabajo

### 📝 Creación de Noticia
1. Editor crea noticia → Estado: `BORRADOR`
2. Editor completa contenido
3. Editor cambia estado a `EN_REVISION`
4. **Sistema activa IA automáticamente**
5. IA genera versiones según configuración
6. Editor revisa y selecciona versión
7. Editor publica → Estado: `PUBLICADA`

### 🔄 Regeneración Manual
1. Editor abre noticia en revisión
2. Click en "Regenerar con IA"
3. Sistema genera nueva versión
4. Nueva versión aparece en lista
5. Editor puede seleccionar cualquier versión

### ⚙️ Configuración de IA
1. Admin accede a `/admin/ai-config`
2. Configura API keys y modelos
3. Prueba conexiones
4. Guarda configuración global
5. Opcionalmente configura por diario en `/admin/diarios`

---

## 🚨 Manejo de Errores

### 🔧 Fallback Automático
- Si OpenAI falla → Intenta con Gemini
- Si Gemini falla → Intenta con OpenAI
- Si ambos fallan → Notifica error al usuario

### 📊 Logging Completo
- Todas las operaciones se registran
- Errores detallados en consola
- Estados de conexión monitoreados

### 🛡️ Validaciones
- API keys válidas antes de usar
- Modelos disponibles verificados
- Configuración consistente validada

---

## 🎯 Próximas Mejoras Sugeridas

### 🔮 Funcionalidades Futuras
- [ ] Análisis de sentimiento
- [ ] Generación de imágenes con IA
- [ ] Traducción automática
- [ ] Resúmenes automáticos
- [ ] Programación de publicaciones
- [ ] Métricas de rendimiento de IA
- [ ] A/B testing de versiones
- [ ] Integración con más proveedores (Claude, etc.)

### 🔧 Mejoras Técnicas
- [ ] Cache de respuestas IA
- [ ] Rate limiting inteligente
- [ ] Optimización de prompts
- [ ] Monitoreo de costos
- [ ] Backup automático de versiones

---

## 📞 Soporte y Mantenimiento

### 🔍 Debugging
- Logs en `/api/admin/ai-config/test`
- Estados en base de datos
- Configuración en tiempo real

### 🔄 Actualizaciones
- Modelos se actualizan automáticamente
- Migración de configuraciones obsoletas
- Compatibilidad hacia atrás mantenida

---

**✅ Estado Actual: COMPLETAMENTE FUNCIONAL**
**🚀 Última Actualización: Enero 2025**
**📋 Modelos: Actualizados según documentación oficial**
