"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/diarios/page",{

/***/ "(app-pages-browser)/./src/app/admin/diarios/page.tsx":
/*!****************************************!*\
  !*** ./src/app/admin/diarios/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDiariosPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDiariosPage() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [diarios, setDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [editingDiario, setEditingDiario] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [showNewForm, setShowNewForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [testingPrompt, setTestingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Form state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        nombre: '',\n        descripcion: '',\n        prompt: '',\n        isActive: true,\n        aiProvider: 'OPENAI',\n        aiModel: '',\n        useGlobalConfig: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AdminDiariosPage.useEffect\": ()=>{\n            var _session_user;\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            } else if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== 'ADMIN') {\n                router.push('/dashboard');\n            }\n        }\n    }[\"AdminDiariosPage.useEffect\"], [\n        status,\n        session,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AdminDiariosPage.useEffect\": ()=>{\n            var _session_user;\n            if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'ADMIN') {\n                loadDiarios();\n            }\n        }\n    }[\"AdminDiariosPage.useEffect\"], [\n        session\n    ]);\n    const loadDiarios = async ()=>{\n        try {\n            const response = await fetch('/api/admin/diarios-config');\n            if (response.ok) {\n                const data = await response.json();\n                setDiarios(data.diarios);\n            } else {\n                alert('Error al cargar diarios');\n            }\n        } catch (error) {\n            console.error('Error al cargar diarios:', error);\n            alert('Error al cargar diarios');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEdit = (diario)=>{\n        setEditingDiario(diario);\n        setFormData({\n            nombre: diario.nombre,\n            descripcion: diario.descripcion || '',\n            prompt: diario.prompt,\n            isActive: diario.isActive\n        });\n        setShowNewForm(false);\n    };\n    const handleNew = ()=>{\n        setEditingDiario(null);\n        setFormData({\n            nombre: '',\n            descripcion: '',\n            prompt: 'Reescribe la siguiente noticia manteniendo la información principal pero adaptando el estilo y tono:',\n            isActive: true\n        });\n        setShowNewForm(true);\n    };\n    const handleCancel = ()=>{\n        setEditingDiario(null);\n        setShowNewForm(false);\n        setFormData({\n            nombre: '',\n            descripcion: '',\n            prompt: '',\n            isActive: true\n        });\n    };\n    const handleSave = async ()=>{\n        if (!formData.nombre.trim() || !formData.prompt.trim()) {\n            alert('Nombre y prompt son requeridos');\n            return;\n        }\n        setSaving(true);\n        try {\n            const url = editingDiario ? '/api/admin/diarios-config' : '/api/admin/diarios-config';\n            const method = editingDiario ? 'PUT' : 'POST';\n            const body = editingDiario ? {\n                ...formData,\n                diarioId: editingDiario.id\n            } : formData;\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(body)\n            });\n            if (response.ok) {\n                alert(editingDiario ? '✅ Diario actualizado' : '✅ Diario creado');\n                await loadDiarios();\n                handleCancel();\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al guardar:', error);\n            alert('Error al guardar el diario');\n        } finally{\n            setSaving(false);\n        }\n    };\n    const testPrompt = async (diario)=>{\n        setTestingPrompt(diario.id);\n        try {\n            // Simular test del prompt (aquí podrías hacer una llamada real a OpenAI)\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            alert(\"✅ Prompt de \".concat(diario.nombre, \" probado exitosamente\"));\n        } catch (error) {\n            alert(\"❌ Error al probar prompt de \".concat(diario.nombre));\n        } finally{\n            setTestingPrompt(null);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando configuraci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/admin'),\n                                        className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Volver al Admin\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-6 w-6 mr-2 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Configuraci\\xf3n de Diarios IA\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Gestiona los prompts y configuraci\\xf3n para la reescritura autom\\xe1tica\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleNew,\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Nuevo Diario\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    (editingDiario || showNewForm) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 bg-white shadow-lg rounded-lg p-6 border-l-4 border-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: editingDiario ? \"Editar \".concat(editingDiario.nombre) : 'Crear Nuevo Diario'\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCancel,\n                                        className: \"text-gray-500 hover:text-gray-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: [\n                                                    \"Nombre del Diario \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.nombre,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        nombre: e.target.value\n                                                    }),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                                                placeholder: \"Ej: Telesoldiario\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Descripci\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.descripcion,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        descripcion: e.target.value\n                                                    }),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                                                placeholder: \"Descripci\\xf3n breve del diario\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: [\n                                                    \"Prompt de Reescritura \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.prompt,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        prompt: e.target.value\n                                                    }),\n                                                rows: 6,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm\",\n                                                placeholder: \"Instrucciones para la IA sobre c\\xf3mo reescribir las noticias para este diario...\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-xs text-gray-500\",\n                                                children: \"Este prompt se usar\\xe1 para instruir a la IA sobre c\\xf3mo adaptar las noticias al estilo de este diario.\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"isActive\",\n                                                checked: formData.isActive,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        isActive: e.target.checked\n                                                    }),\n                                                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"isActive\",\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Diario activo (disponible para generar versiones)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-end space-x-4 pt-4 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancel,\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSave,\n                                                disabled: saving,\n                                                className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                                children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Guardando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: editingDiario ? 'Actualizar' : 'Crear'\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: [\n                                        \"Diarios Configurados (\",\n                                        diarios.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            diarios.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No hay diarios configurados\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Crea tu primer diario para comenzar a generar versiones con IA.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNew,\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Crear Primer Diario\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y divide-gray-200\",\n                                children: diarios.map((diario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                    children: diario.nombre\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(diario.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                    children: diario.isActive ? 'Activo' : 'Inactivo'\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        diario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-3\",\n                                                            children: diario.descripcion\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Prompt de Reescritura\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1 p-3 bg-gray-50 rounded text-sm text-gray-700 font-mono max-h-32 overflow-y-auto\",\n                                                                    children: diario.prompt\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Creado: \",\n                                                                new Date(diario.createdAt).toLocaleDateString(),\n                                                                \" | Actualizado: \",\n                                                                new Date(diario.updatedAt).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 ml-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>testPrompt(diario),\n                                                            disabled: testingPrompt === diario.id,\n                                                            className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                                            children: testingPrompt === diario.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Probando...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Probar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleEdit(diario),\n                                                            className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Editar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, diario.id, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600 mt-0.5 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                                            children: \"Informaci\\xf3n sobre los Prompts\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-800 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Los prompts definen c\\xf3mo la IA reescribir\\xe1 las noticias para cada diario.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• S\\xe9 espec\\xedfico sobre el tono, estilo y formato deseado.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Puedes incluir instrucciones sobre la audiencia objetivo del diario.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Los cambios en los prompts afectar\\xe1n todas las futuras generaciones.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDiariosPage, \"MP2FfKW++jDhbkfZ93KTbajIDJI=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminDiariosPage;\nvar _c;\n$RefreshReg$(_c, \"AdminDiariosPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/diarios/page.tsx\n"));

/***/ })

});