import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    // Verificar si ya existe un administrador
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (existingAdmin) {
      console.log('Ya existe un usuario administrador:', existingAdmin.email);
      return;
    }

    // Crear contraseña hasheada
    const password = 'admin123';
    const hashedPassword = await bcrypt.hash(password, 12);

    // Crear usuario administrador
    const admin = await prisma.user.create({
      data: {
        name: 'Administrador',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
        isActive: true
      }
    });

    console.log('Usuario administrador creado exitosamente:');
    console.log('Email:', admin.email);
    console.log('Contraseña:', password);
    console.log('Rol:', admin.role);

  } catch (error) {
    console.error('Error al crear administrador:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();
