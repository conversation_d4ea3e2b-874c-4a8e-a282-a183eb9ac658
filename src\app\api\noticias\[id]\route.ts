import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/noticias/[id] - Obtener una noticia específica
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const noticia = await prisma.noticia.findUnique({
      where: { id },
      include: {
        categoria: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    return NextResponse.json(noticia);
  } catch (error) {
    console.error('Error al obtener noticia:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/noticias/[id] - Actualizar una noticia
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que la noticia existe
    const noticiaExistente = await prisma.noticia.findUnique({
      where: { id },
    });

    if (!noticiaExistente) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Verificar permisos (solo el autor o admin puede editar)
    if (noticiaExistente.userId !== parseInt(session.user.id) && session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No tienes permisos para editar esta noticia' }, { status: 403 });
    }

    // Verificar si es FormData o JSON
    const contentType = request.headers.get('content-type');
    
    let formData: any = {};
    
    if (contentType?.includes('multipart/form-data')) {
      // Manejar FormData
      const formDataObj = await request.formData();
      formData = {
        volanta: formDataObj.get('volanta') as string,
        titulo: formDataObj.get('titulo') as string,
        resumen: formDataObj.get('resumen') as string,
        contenido: formDataObj.get('contenido') as string,
        imagenUrl: formDataObj.get('imagenUrl') as string,
        categoriaId: formDataObj.get('categoriaId') as string,
      };
    } else {
      // Manejar JSON
      formData = await request.json();
    }

    const {
      volanta,
      titulo,
      resumen,
      contenido,
      imagenUrl,
      categoriaId,
    } = formData;

    // Validaciones básicas
    if (!volanta || !titulo || !contenido) {
      return NextResponse.json(
        { error: 'Volanta, título y contenido son requeridos' },
        { status: 400 }
      );
    }

    // Verificar que la categoría existe si se proporciona
    if (categoriaId) {
      const categoria = await prisma.categoria.findUnique({
        where: { id: parseInt(categoriaId) }
      });

      if (!categoria) {
        return NextResponse.json(
          { error: 'Categoría no encontrada' },
          { status: 404 }
        );
      }
    }

    // Actualizar la noticia
    const noticia = await prisma.noticia.update({
      where: { id },
      data: {
        volanta,
        titulo,
        resumen,
        contenido,
        imagenUrl,
        categoriaId: categoriaId ? parseInt(categoriaId) : null,
      },
      include: {
        categoria: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(noticia);
  } catch (error) {
    console.error('Error al actualizar noticia:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/noticias/[id] - Eliminar una noticia
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que la noticia existe
    const noticiaExistente = await prisma.noticia.findUnique({
      where: { id },
    });

    if (!noticiaExistente) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Verificar permisos (solo el autor o admin puede eliminar)
    if (noticiaExistente.userId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No tienes permisos para eliminar esta noticia' }, { status: 403 });
    }

    // Eliminar la noticia
    await prisma.noticia.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Noticia eliminada correctamente' });
  } catch (error) {
    console.error('Error al eliminar noticia:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 