'use client';

import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ArrowLeft, Edit, Trash2, Clock, CheckCircle, FileText, TrendingUp, Send, Bot, ChevronDown, ChevronUp } from 'lucide-react';

interface Noticia {
  id: number;
  titulo: string;
  subtitulo?: string;
  contenido: string;
  resumen?: string;
  imagenUrl?: string;
  imagenAlt?: string;
  autor?: string;
  fuente?: string;
  urlFuente?: string;
  estado: string;
  destacada: boolean;
  publicada: boolean;
  fechaPublicacion?: string;
  createdAt: string;
  categoria?: {
    id: number;
    nombre: string;
    color: string;
  };
  user?: {
    name: string;
    email: string;
  };
}

export default function NoticiaDetailPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const id = Array.isArray(params.id) ? params.id[0] : params.id;
  const [noticia, setNoticia] = useState<Noticia | null>(null);
  const [loading, setLoading] = useState(true);
  const [diarios, setDiarios] = useState<any[]>([]);
  const [selectedDiarios, setSelectedDiarios] = useState<number[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [versiones, setVersiones] = useState<any[]>([]);
  const [showVersiones, setShowVersiones] = useState(false);
  const [loadingVersiones, setLoadingVersiones] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session && id) {
      loadNoticia();
      loadDiarios();
    }
  }, [session, id]);

  useEffect(() => {
    if (noticia && noticia.estado === 'EN_REVISION') {
      loadVersiones();
    }
  }, [noticia]);

  const loadNoticia = async () => {
    try {
      const response = await fetch(`/api/noticias/${id}`);
      if (response.ok) {
        const data = await response.json();
        setNoticia(data);
      } else {
        alert('No se pudo cargar la noticia');
        router.push('/noticias');
      }
    } catch (error) {
      console.error('Error al cargar noticia:', error);
      alert('Error al cargar la noticia');
      router.push('/noticias');
    } finally {
      setLoading(false);
    }
  };

  const loadDiarios = async () => {
    try {
      const response = await fetch('/api/diarios');
      if (response.ok) {
        const data = await response.json();
        setDiarios(data.diarios);
        // Seleccionar todos los diarios por defecto
        setSelectedDiarios(data.diarios.map((d: any) => d.id));
      }
    } catch (error) {
      console.error('Error al cargar diarios:', error);
    }
  };

  const loadVersiones = async () => {
    if (!id) return;

    setLoadingVersiones(true);
    try {
      const response = await fetch(`/api/noticias/${id}/versions`);
      if (response.ok) {
        const data = await response.json();
        setVersiones(data.versiones);
        if (data.versiones.length > 0) {
          setShowVersiones(true);
        }
      }
    } catch (error) {
      console.error('Error al cargar versiones:', error);
    } finally {
      setLoadingVersiones(false);
    }
  };

  const handleGenerateVersions = async () => {
    if (selectedDiarios.length === 0) {
      alert('Selecciona al menos un diario para generar versiones');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch(`/api/noticias/${id}/generate-versions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          diarioIds: selectedDiarios,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        alert(`✅ ${data.message}`);
        // Recargar la noticia para actualizar el estado
        await loadNoticia();
        // Cargar las versiones generadas
        await loadVersiones();
      } else {
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error al generar versiones:', error);
      alert('Error al generar versiones con IA');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleVersionStateChange = async (versionId: number, estado: string) => {
    try {
      const response = await fetch(`/api/noticias/${id}/versions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          versionId,
          estado,
        }),
      });

      if (response.ok) {
        // Recargar versiones
        await loadVersiones();
        alert(`✅ Estado actualizado a: ${estado}`);
      } else {
        const data = await response.json();
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error al actualizar estado:', error);
      alert('Error al actualizar el estado de la versión');
    }
  };

  const handleDelete = async () => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta noticia?')) {
      return;
    }

    try {
      const response = await fetch(`/api/noticias/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        alert('Noticia eliminada exitosamente');
        router.push('/noticias');
      } else {
        alert('Error al eliminar la noticia');
      }
    } catch (error) {
      console.error('Error al eliminar noticia:', error);
      alert('Error al eliminar la noticia');
    }
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'PUBLICADA':
        return 'bg-green-100 text-green-800';
      case 'APROBADA':
        return 'bg-blue-100 text-blue-800';
      case 'EN_REVISION':
        return 'bg-yellow-100 text-yellow-800';
      case 'BORRADOR':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'PUBLICADA':
        return <CheckCircle className="h-4 w-4" />;
      case 'APROBADA':
        return <CheckCircle className="h-4 w-4" />;
      case 'EN_REVISION':
        return <Clock className="h-4 w-4" />;
      case 'BORRADOR':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || !noticia) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/noticias')}
                className="mr-4 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">Detalles de la Noticia</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push(`/noticias/${id}/editar`)}
                className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
              >
                <Edit className="h-4 w-4" />
                <span>Editar</span>
              </button>
              
              <button
                onClick={handleDelete}
                className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-red-700 hover:text-red-900 hover:bg-red-50 rounded-md transition-colors"
              >
                <Trash2 className="h-4 w-4" />
                <span>Eliminar</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Noticia Content */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            {/* Header */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getEstadoColor(noticia.estado)}`}>
                    {getEstadoIcon(noticia.estado)}
                    <span className="ml-1">{noticia.estado.replace('_', ' ')}</span>
                  </span>
                  
                  {noticia.destacada && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      Destacada
                    </span>
                  )}
                </div>
                
                <div className="text-sm text-gray-500">
                  Creada el {new Date(noticia.createdAt).toLocaleDateString()}
                </div>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {noticia.titulo}
              </h1>
              
              {noticia.subtitulo && (
                <p className="text-xl text-gray-600 mb-4">
                  {noticia.subtitulo}
                </p>
              )}

              <div className="flex items-center space-x-4 text-sm text-gray-500">
                {noticia.categoria && (
                  <span
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                    style={{ backgroundColor: `${noticia.categoria.color}20`, color: noticia.categoria.color }}
                  >
                    {noticia.categoria.nombre}
                  </span>
                )}
                
                {noticia.autor && (
                  <span>por {noticia.autor}</span>
                )}
                
                {noticia.fuente && (
                  <span>Fuente: {noticia.fuente}</span>
                )}
                
                {noticia.fechaPublicacion && (
                  <span>Publicada: {new Date(noticia.fechaPublicacion).toLocaleDateString()}</span>
                )}
              </div>
            </div>

            {/* Image */}
            {noticia.imagenUrl && (
              <div className="px-6 py-4">
                <img
                  src={noticia.imagenUrl}
                  alt={noticia.imagenAlt || noticia.titulo}
                  className="w-full h-64 object-cover rounded-lg"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
            )}

            {/* Content */}
            <div className="px-6 py-4">
              {noticia.resumen && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Resumen</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {noticia.resumen}
                  </p>
                </div>
              )}

              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Contenido</h3>
                <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {noticia.contenido}
                </div>
              </div>

              {/* Source Link */}
              {noticia.urlFuente && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <a
                    href={noticia.urlFuente}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Ver fuente original →
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Metadata */}
          <div className="mt-6 bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Información Adicional</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm font-medium text-gray-500">Autor:</span>
                <p className="text-sm text-gray-900">{noticia.autor || 'No especificado'}</p>
              </div>
              
              <div>
                <span className="text-sm font-medium text-gray-500">Fuente:</span>
                <p className="text-sm text-gray-900">{noticia.fuente || 'No especificada'}</p>
              </div>
              
              <div>
                <span className="text-sm font-medium text-gray-500">Creada por:</span>
                <p className="text-sm text-gray-900">{noticia.user?.name || 'Usuario'}</p>
              </div>
              
              <div>
                <span className="text-sm font-medium text-gray-500">Estado:</span>
                <p className="text-sm text-gray-900">{noticia.estado.replace('_', ' ')}</p>
              </div>
              
              <div>
                <span className="text-sm font-medium text-gray-500">Destacada:</span>
                <p className="text-sm text-gray-900">{noticia.destacada ? 'Sí' : 'No'}</p>
              </div>
              
              <div>
                <span className="text-sm font-medium text-gray-500">Publicada:</span>
                <p className="text-sm text-gray-900">{noticia.publicada ? 'Sí' : 'No'}</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 