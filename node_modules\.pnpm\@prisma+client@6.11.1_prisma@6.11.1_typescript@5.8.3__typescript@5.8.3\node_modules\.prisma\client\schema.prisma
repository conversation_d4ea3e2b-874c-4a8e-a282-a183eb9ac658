// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// Modelo de Usuario
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String
  password  String
  role      Role     @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  noticias           Noticia[]
  correcciones       Correccion[]
  boardMemberships   BoardMember[]
  createdBoards      Board[]          @relation("BoardCreator")
  versionesGeneradas VersionNoticia[]

  @@map("users")
}

// Modelo de Categoría
model Categoria {
  id          Int      @id @default(autoincrement())
  nombre      String   @unique
  descripcion String?
  color       String   @default("#3B82F6")
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  noticias Noticia[]

  @@map("categorias")
}

// Modelo de Noticia
model Noticia {
  id               Int           @id @default(autoincrement())
  titulo           String
  subtitulo        String?
  volanta          String?
  contenido        String
  resumen          String?
  imagenUrl        String?
  imagenAlt        String?
  autor            String?
  fuente           String?
  urlFuente        String?
  estado           EstadoNoticia @default(BORRADOR)
  destacada        Boolean       @default(false)
  publicada        Boolean       @default(false)
  fechaPublicacion DateTime?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt

  // Relaciones
  categoriaId Int?
  categoria   Categoria?       @relation(fields: [categoriaId], references: [id])
  userId      Int
  user        User             @relation(fields: [userId], references: [id])
  versiones   VersionNoticia[]

  @@map("noticias")
}

// Modelo de Corrección (mantenido para compatibilidad)
model Correccion {
  id               Int       @id @default(autoincrement())
  titulo           String
  contenido        String
  medio            String
  fechaPublicacion DateTime
  fechaCorreccion  DateTime
  estado           Estado    @default(PENDIENTE)
  prioridad        Prioridad @default(MEDIA)
  imagenUrl        String?
  observaciones    String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // Relaciones
  userId Int
  user   User @relation(fields: [userId], references: [id])

  @@map("correcciones")
}

// Modelo de Board/Equipo
model Board {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  createdById Int
  createdBy   User          @relation("BoardCreator", fields: [createdById], references: [id])
  members     BoardMember[]

  @@map("boards")
}

// Modelo de Membresía de Board
model BoardMember {
  id        Int       @id @default(autoincrement())
  role      BoardRole @default(MEMBER)
  joinedAt  DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relaciones
  userId  Int
  user    User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  boardId Int
  board   Board @relation(fields: [boardId], references: [id], onDelete: Cascade)

  // Un usuario solo puede tener un rol por board
  @@unique([userId, boardId])
  @@map("board_members")
}

// Enum para roles de usuario
enum Role {
  ADMIN
  EDITOR
  USER
}

// Enum para estados de noticia
enum EstadoNoticia {
  BORRADOR
  EN_REVISION
  APROBADA
  PUBLICADA
  ARCHIVADA
}

// Enum para estados de corrección
enum Estado {
  PENDIENTE
  EN_REVISION
  COMPLETADA
  RECHAZADA
}

// Enum para prioridades
enum Prioridad {
  BAJA
  MEDIA
  ALTA
  URGENTE
}

// Enum para roles de board
enum BoardRole {
  ADMIN
  MEMBER
}

// Modelo de Diario
model Diario {
  id          Int     @id @default(autoincrement())
  nombre      String  @unique
  descripcion String?
  prompt      String  @default("Reescribe la siguiente noticia manteniendo la información principal pero adaptando el estilo y tono:")
  isActive    Boolean @default(true)

  // Configuración de IA
  aiProvider      AIProvider @default(OPENAI) // Proveedor de IA (OpenAI o Gemini)
  aiModel         String? // Modelo específico (ej: gpt-4, gemini-pro)
  useGlobalConfig Boolean    @default(true) // Si usa configuración global o específica

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  versiones VersionNoticia[]

  @@map("diarios")
}

// Modelo de Versión de Noticia generada por IA
model VersionNoticia {
  id          Int           @id @default(autoincrement())
  titulo      String
  subtitulo   String?
  volanta     String?
  contenido   String
  resumen     String?
  estado      EstadoVersion @default(GENERADA)
  promptUsado String?
  metadatos   String? // JSON con información adicional de la generación
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relaciones
  noticiaId   Int
  noticia     Noticia @relation(fields: [noticiaId], references: [id], onDelete: Cascade)
  diarioId    Int
  diario      Diario  @relation(fields: [diarioId], references: [id])
  generadaPor Int
  usuario     User    @relation(fields: [generadaPor], references: [id])

  @@map("versiones_noticias")
}

// Modelo de Configuración Global de IA
model AIConfig {
  id Int @id @default(autoincrement())

  // Configuración OpenAI
  openaiApiKey      String?
  openaiModel       String  @default("gpt-3.5-turbo")
  openaiMaxTokens   Int     @default(2000)
  openaiTemperature Float   @default(0.7)

  // Configuración Google Gemini
  geminiApiKey      String?
  geminiModel       String  @default("gemini-pro")
  geminiMaxTokens   Int     @default(2000)
  geminiTemperature Float   @default(0.7)

  // Configuración global
  defaultProvider AIProvider @default(OPENAI)
  isActive        Boolean    @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("ai_config")
}

// Enum para proveedores de IA
enum AIProvider {
  OPENAI
  GEMINI

  @@map("ai_provider")
}

// Enum para estados de versiones generadas
enum EstadoVersion {
  GENERADA
  APROBADA
  RECHAZADA
  EN_REVISION
}
