'use client';

import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { LogOut, User, Shield, FileText, Settings, Plus, List, Newspaper, TrendingUp, Clock, CheckCircle, Users } from 'lucide-react';
import { ThemeToggle } from '@/components/theme-toggle';

interface NoticiaStats {
  total: number;
  publicadas: number;
  borrador: number;
  enRevision: number;
  aprobadas: number;
  destacadas: number;
  porCategoria: Array<{
    categoriaId: number | null;
    categoriaNombre: string;
    categoriaColor: string;
    count: number;
  }>;
  recientes: Array<{
    id: number;
    titulo: string;
    estado: string;
    createdAt: string;
    categoria?: {
      nombre: string;
      color: string;
    };
    user?: {
      name: string;
    };
  }>;
}

export default function Dashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<NoticiaStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session) {
      loadStats();
      checkAdminStatus();
    }
  }, [session]);

  const checkAdminStatus = async () => {
    try {
      const response = await fetch('/api/admin/users?limit=1');
      setIsAdmin(response.ok && response.status !== 403);
    } catch (error) {
      setIsAdmin(false);
    }
  };

  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/noticias/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error al cargar estadísticas:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 dark:border-blue-400"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/auth/signin' });
  };

  const navigateToNoticias = () => {
    router.push('/noticias');
  };

  const navigateToNewNoticia = () => {
    router.push('/noticias/nueva');
  };

  const navigateToAdminUsuarios = () => {
    router.push('/admin/usuarios');
  };

  const navigateToAdmin = () => {
    router.push('/admin');
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'PUBLICADA':
        return 'text-green-600 dark:text-green-400';
      case 'APROBADA':
        return 'text-blue-600 dark:text-blue-400';
      case 'EN_REVISION':
        return 'text-yellow-500 dark:text-yellow-400';
      case 'BORRADOR':
        return 'text-gray-500 dark:text-gray-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'PUBLICADA':
        return <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />;
      case 'APROBADA':
        return <CheckCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />;
      case 'EN_REVISION':
        return <Clock className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />;
      case 'BORRADOR':
        return <FileText className="h-4 w-4 text-gray-500 dark:text-gray-400" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500 dark:text-gray-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Newspaper className="h-5 w-5 text-white" />
              </div>
              <h1 className="ml-3 text-xl font-semibold text-gray-900 dark:text-gray-100">
                Sapia Noticias v2.0
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-gray-400" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {session.user?.name || 'Usuario'}
                </span>
              </div>
              <button
                onClick={handleSignOut}
                className="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <LogOut className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Dashboard</h2>
          <div className="flex space-x-3">
            <button
              onClick={navigateToNoticias}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <List className="-ml-1 mr-2 h-5 w-5" />
              Ver Noticias
            </button>
            {isAdmin && (
              <>
                <button
                  onClick={navigateToAdmin}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <Shield className="-ml-1 mr-2 h-5 w-5" />
                  Panel Admin
                </button>
                <button
                  onClick={navigateToAdminUsuarios}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <Users className="-ml-1 mr-2 h-5 w-5" />
                  Usuarios
                </button>
              </>
            )}
            <button
              onClick={navigateToNewNoticia}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="-ml-1 mr-2 h-5 w-5" />
              Nueva Noticia
            </button>
          </div>
        </div>

        {/* Stats */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Estadísticas Generales</h3>
          <div className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Newspaper className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                        Noticias Totales
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {stats?.total || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                        Publicadas
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {stats?.publicadas || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                        En Revisión
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {stats?.enRevision || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                        Destacadas
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {stats?.destacadas || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent News */}
          <div className="mt-8 bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Noticias Recientes</h3>
            </div>
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {stats?.recientes && stats.recientes.length > 0 ? (
                stats.recientes.map((noticia) => (
                  <div key={noticia.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getEstadoIcon(noticia.estado)}
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-md">
                            {noticia.titulo}
                          </h4>
                          <div className="flex items-center space-x-2 mt-1">
                            {noticia.categoria && (
                              <span
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                style={{ backgroundColor: `${noticia.categoria.color}20`, color: noticia.categoria.color }}
                              >
                                {noticia.categoria.nombre}
                              </span>
                            )}
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {new Date(noticia.createdAt).toLocaleDateString()}
                            </span>
                            {noticia.user && (
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                por {noticia.user.name}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <span className={`text-xs font-medium ${getEstadoColor(noticia.estado)}`}>
                        {noticia.estado.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                  No hay noticias recientes
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}