'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ArrowLeft, Bot, Save, Plus, Edit, Trash2, TestTube, AlertCircle, CheckCircle } from 'lucide-react';

const OPENAI_MODELS = [
  'gpt-3.5-turbo',
  'gpt-3.5-turbo-16k',
  'gpt-4',
  'gpt-4-turbo',
  'gpt-4-turbo-preview',
  'gpt-4o',
  'gpt-4o-mini'
];

const GEMINI_MODELS = [
  'gemini-pro',
  'gemini-pro-vision',
  'gemini-1.5-pro',
  'gemini-1.5-flash'
];

interface Diario {
  id: number;
  nombre: string;
  descripcion?: string;
  prompt: string;
  isActive: boolean;
  aiProvider: 'OPENAI' | 'GEMINI';
  aiModel?: string;
  useGlobalConfig: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function AdminDiariosPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [diarios, setDiarios] = useState<Diario[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingDiario, setEditingDiario] = useState<Diario | null>(null);
  const [showNewForm, setShowNewForm] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testingPrompt, setTestingPrompt] = useState<number | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    nombre: '',
    descripcion: '',
    prompt: '',
    isActive: true,
    aiProvider: 'OPENAI' as 'OPENAI' | 'GEMINI',
    aiModel: '',
    useGlobalConfig: true,
  });

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    } else if (session?.user?.role !== 'ADMIN') {
      router.push('/dashboard');
    }
  }, [status, session, router]);

  useEffect(() => {
    if (session?.user?.role === 'ADMIN') {
      loadDiarios();
    }
  }, [session]);

  const loadDiarios = async () => {
    try {
      const response = await fetch('/api/admin/diarios-config');
      if (response.ok) {
        const data = await response.json();
        setDiarios(data.diarios);
      } else {
        alert('Error al cargar diarios');
      }
    } catch (error) {
      console.error('Error al cargar diarios:', error);
      alert('Error al cargar diarios');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (diario: Diario) => {
    setEditingDiario(diario);
    setFormData({
      nombre: diario.nombre,
      descripcion: diario.descripcion || '',
      prompt: diario.prompt,
      isActive: diario.isActive,
      aiProvider: diario.aiProvider || 'OPENAI',
      aiModel: diario.aiModel || '',
      useGlobalConfig: diario.useGlobalConfig !== undefined ? diario.useGlobalConfig : true,
    });
    setShowNewForm(false);
  };

  const handleNew = () => {
    setEditingDiario(null);
    setFormData({
      nombre: '',
      descripcion: '',
      prompt: 'Reescribe la siguiente noticia manteniendo la información principal pero adaptando el estilo y tono:',
      isActive: true,
      aiProvider: 'OPENAI',
      aiModel: '',
      useGlobalConfig: true,
    });
    setShowNewForm(true);
  };

  const handleCancel = () => {
    setEditingDiario(null);
    setShowNewForm(false);
    setFormData({
      nombre: '',
      descripcion: '',
      prompt: '',
      isActive: true,
    });
  };

  const handleSave = async () => {
    if (!formData.nombre.trim() || !formData.prompt.trim()) {
      alert('Nombre y prompt son requeridos');
      return;
    }

    setSaving(true);
    try {
      const url = editingDiario
        ? '/api/admin/diarios-config'
        : '/api/admin/diarios-config';

      const method = editingDiario ? 'PUT' : 'POST';
      const body = editingDiario
        ? { ...formData, diarioId: editingDiario.id }
        : formData;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        alert(editingDiario ? '✅ Diario actualizado' : '✅ Diario creado');
        await loadDiarios();
        handleCancel();
      } else {
        const data = await response.json();
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error al guardar:', error);
      alert('Error al guardar el diario');
    } finally {
      setSaving(false);
    }
  };

  const testPrompt = async (diario: Diario) => {
    setTestingPrompt(diario.id);
    try {
      // Simular test del prompt (aquí podrías hacer una llamada real a OpenAI)
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert(`✅ Prompt de ${diario.nombre} probado exitosamente`);
    } catch (error) {
      alert(`❌ Error al probar prompt de ${diario.nombre}`);
    } finally {
      setTestingPrompt(null);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando configuración...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin')}
                className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Volver al Admin
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Bot className="h-6 w-6 mr-2 text-blue-600" />
                  Configuración de Diarios IA
                </h1>
                <p className="text-sm text-gray-600">
                  Gestiona los prompts y configuración para la reescritura automática
                </p>
              </div>
            </div>

            <button
              onClick={handleNew}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Nuevo Diario</span>
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Form de edición/creación */}
        {(editingDiario || showNewForm) && (
          <div className="mb-8 bg-white shadow-lg rounded-lg p-6 border-l-4 border-blue-500">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900">
                {editingDiario ? `Editar ${editingDiario.nombre}` : 'Crear Nuevo Diario'}
              </h2>
              <button
                onClick={handleCancel}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            <div className="space-y-6">
              {/* Nombre */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre del Diario <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.nombre}
                  onChange={(e) => setFormData({ ...formData, nombre: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ej: Telesoldiario"
                />
              </div>

              {/* Descripción */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descripción
                </label>
                <input
                  type="text"
                  value={formData.descripcion}
                  onChange={(e) => setFormData({ ...formData, descripcion: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Descripción breve del diario"
                />
              </div>

              {/* Prompt */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Prompt de Reescritura <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={formData.prompt}
                  onChange={(e) => setFormData({ ...formData, prompt: e.target.value })}
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                  placeholder="Instrucciones para la IA sobre cómo reescribir las noticias para este diario..."
                />
                <p className="mt-1 text-xs text-gray-500">
                  Este prompt se usará para instruir a la IA sobre cómo adaptar las noticias al estilo de este diario.
                </p>
              </div>

              {/* Configuración de IA */}
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Bot className="h-5 w-5 mr-2 text-blue-600" />
                  Configuración de IA
                </h3>

                {/* Usar configuración global */}
                <div className="mb-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="useGlobalConfig"
                      checked={formData.useGlobalConfig}
                      onChange={(e) => setFormData({ ...formData, useGlobalConfig: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="useGlobalConfig" className="ml-2 block text-sm text-gray-900">
                      Usar configuración global de IA
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-500 ml-6">
                    Si está marcado, usará la configuración global. Si no, podrás configurar un proveedor específico para este diario.
                  </p>
                </div>

                {/* Configuración específica (solo si no usa global) */}
                {!formData.useGlobalConfig && (
                  <div className="space-y-4 ml-6 p-4 bg-gray-50 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Proveedor de IA */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Proveedor de IA
                        </label>
                        <select
                          value={formData.aiProvider}
                          onChange={(e) => setFormData({
                            ...formData,
                            aiProvider: e.target.value as 'OPENAI' | 'GEMINI',
                            aiModel: '' // Reset model when changing provider
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="OPENAI">OpenAI</option>
                          <option value="GEMINI">Google Gemini</option>
                        </select>
                      </div>

                      {/* Modelo específico */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Modelo Específico (Opcional)
                        </label>
                        <select
                          value={formData.aiModel}
                          onChange={(e) => setFormData({ ...formData, aiModel: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">Usar modelo por defecto</option>
                          {formData.aiProvider === 'OPENAI'
                            ? OPENAI_MODELS.map(model => (
                                <option key={model} value={model}>{model}</option>
                              ))
                            : GEMINI_MODELS.map(model => (
                                <option key={model} value={model}>{model}</option>
                              ))
                          }
                        </select>
                        <p className="mt-1 text-xs text-gray-500">
                          Si no se especifica, se usará el modelo configurado globalmente para este proveedor.
                        </p>
                      </div>
                    </div>

                    {/* Indicador del proveedor seleccionado */}
                    <div className="flex items-center space-x-2 text-sm">
                      <div className={`w-3 h-3 rounded-full ${
                        formData.aiProvider === 'OPENAI' ? 'bg-green-500' : 'bg-purple-500'
                      }`}></div>
                      <span className="text-gray-600">
                        Este diario usará {formData.aiProvider === 'OPENAI' ? 'OpenAI' : 'Google Gemini'}
                        {formData.aiModel && ` con el modelo ${formData.aiModel}`}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Estado activo */}
              <div className="flex items-center border-t border-gray-200 pt-6">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                  Diario activo (disponible para generar versiones)
                </label>
              </div>

              {/* Botones */}
              <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSave}
                  disabled={saving}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Guardando...</span>
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      <span>{editingDiario ? 'Actualizar' : 'Crear'}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Lista de diarios */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Diarios Configurados ({diarios.length})
            </h2>
          </div>

          {diarios.length === 0 ? (
            <div className="p-8 text-center">
              <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No hay diarios configurados</h3>
              <p className="text-gray-600 mb-4">
                Crea tu primer diario para comenzar a generar versiones con IA.
              </p>
              <button
                onClick={handleNew}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto"
              >
                <Plus className="h-4 w-4" />
                <span>Crear Primer Diario</span>
              </button>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {diarios.map((diario) => (
                <div key={diario.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {diario.nombre}
                        </h3>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          diario.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {diario.isActive ? 'Activo' : 'Inactivo'}
                        </span>
                      </div>

                      {diario.descripcion && (
                        <p className="text-sm text-gray-600 mb-3">
                          {diario.descripcion}
                        </p>
                      )}

                      <div className="mb-3">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Prompt de Reescritura
                        </span>
                        <div className="mt-1 p-3 bg-gray-50 rounded text-sm text-gray-700 font-mono max-h-32 overflow-y-auto">
                          {diario.prompt}
                        </div>
                      </div>

                      {/* Configuración de IA */}
                      <div className="mb-3">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Configuración de IA
                        </span>
                        <div className="mt-1 flex items-center space-x-4">
                          {diario.useGlobalConfig ? (
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              <span className="text-sm text-gray-600">Configuración Global</span>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-2">
                                <div className={`w-2 h-2 rounded-full ${
                                  diario.aiProvider === 'OPENAI' ? 'bg-green-500' : 'bg-purple-500'
                                }`}></div>
                                <span className="text-sm text-gray-600">
                                  {diario.aiProvider === 'OPENAI' ? 'OpenAI' : 'Google Gemini'}
                                </span>
                              </div>
                              {diario.aiModel && (
                                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                  {diario.aiModel}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="text-xs text-gray-500">
                        Creado: {new Date(diario.createdAt).toLocaleDateString()} |
                        Actualizado: {new Date(diario.updatedAt).toLocaleDateString()}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => testPrompt(diario)}
                        disabled={testingPrompt === diario.id}
                        className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
                      >
                        {testingPrompt === diario.id ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-700"></div>
                            <span>Probando...</span>
                          </>
                        ) : (
                          <>
                            <TestTube className="h-3 w-3" />
                            <span>Probar</span>
                          </>
                        )}
                      </button>

                      <button
                        onClick={() => handleEdit(diario)}
                        className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                      >
                        <Edit className="h-3 w-3" />
                        <span>Editar</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Información adicional */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-blue-900 mb-2">
                Información sobre los Prompts
              </h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p>• Los prompts definen cómo la IA reescribirá las noticias para cada diario.</p>
                <p>• Sé específico sobre el tono, estilo y formato deseado.</p>
                <p>• Puedes incluir instrucciones sobre la audiencia objetivo del diario.</p>
                <p>• Los cambios en los prompts afectarán todas las futuras generaciones.</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}