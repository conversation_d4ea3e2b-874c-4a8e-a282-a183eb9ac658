"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/noticias/page",{

/***/ "(app-pages-browser)/./src/app/noticias/page.tsx":
/*!***********************************!*\
  !*** ./src/app/noticias/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NoticiasPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _components_theme_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-toggle */ \"(app-pages-browser)/./src/components/theme-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NoticiasPage() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [noticias, setNoticias] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [categorias, setCategorias] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [selectedCategoria, setSelectedCategoria] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [selectedEstado, setSelectedEstado] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(1);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NoticiasPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"NoticiasPage.useEffect\"], [\n        status,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NoticiasPage.useEffect\": ()=>{\n            if (session) {\n                loadCategorias();\n                loadNoticias();\n            }\n        }\n    }[\"NoticiasPage.useEffect\"], [\n        session,\n        currentPage,\n        search,\n        selectedCategoria,\n        selectedEstado\n    ]);\n    const loadCategorias = async ()=>{\n        try {\n            const response = await fetch('/api/categorias');\n            if (response.ok) {\n                const data = await response.json();\n                setCategorias(data);\n            }\n        } catch (error) {\n            console.error('Error al cargar categorías:', error);\n        }\n    };\n    const loadNoticias = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: '10'\n            });\n            if (search) params.append('search', search);\n            if (selectedCategoria) params.append('categoria', selectedCategoria);\n            if (selectedEstado) params.append('estado', selectedEstado);\n            const response = await fetch(\"/api/noticias?\".concat(params));\n            if (response.ok) {\n                const data = await response.json();\n                setNoticias(data.noticias);\n                setPagination(data.pagination);\n            }\n        } catch (error) {\n            console.error('Error al cargar noticias:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta noticia?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                loadNoticias();\n            } else {\n                alert('Error al eliminar la noticia');\n            }\n        } catch (error) {\n            console.error('Error al eliminar noticia:', error);\n            alert('Error al eliminar la noticia');\n        }\n    };\n    const handleSignOut = async ()=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n            callbackUrl: '/auth/signin'\n        });\n    };\n    const getEstadoColor = (estado)=>{\n        switch(estado){\n            case 'PUBLICADA':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'APROBADA':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n            case 'EN_REVISION':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'BORRADOR':\n                return 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n            default:\n                return 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n        }\n    };\n    const getEstadoIcon = (estado)=>{\n        switch(estado){\n            case 'PUBLICADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 16\n                }, this);\n            case 'APROBADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 16\n                }, this);\n            case 'EN_REVISION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 16\n                }, this);\n            case 'BORRADOR':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (status === 'loading' || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 dark:border-blue-400\"\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 sticky top-0 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center cursor-pointer\",\n                                    onClick: ()=>router.push('/dashboard'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"ml-3 text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: \"Panel de Noticias\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                children: ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || 'Usuario'\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSignOut,\n                                        className: \"p-1 rounded-full text-gray-400 hover:text-gray-500 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-gray-100\",\n                                children: \"Lista de Noticias\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/noticias/nueva'),\n                                className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"-ml-1 mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Nueva Noticia\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Buscar por t\\xedtulo...\",\n                                            value: search,\n                                            onChange: (e)=>setSearch(e.target.value),\n                                            className: \"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200 focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategoria,\n                                        onChange: (e)=>setSelectedCategoria(e.target.value),\n                                        className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200 focus:ring-blue-500 focus:border-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Todas las categor\\xedas\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            categorias.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: cat.id.toString(),\n                                                    children: cat.nombre\n                                                }, cat.id, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedEstado,\n                                        onChange: (e)=>setSelectedEstado(e.target.value),\n                                        className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200 focus:ring-blue-500 focus:border-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Todos los estados\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"BORRADOR\",\n                                                children: \"Borrador\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"EN_REVISION\",\n                                                children: \"En Revisi\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"APROBADA\",\n                                                children: \"Aprobada\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PUBLICADA\",\n                                                children: \"Publicada\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full p-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mr-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Filtros Avanzados\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 md:space-y-4\",\n                        children: noticias.length > 0 ? noticias.map((noticia)=>{\n                            var _noticia_user, _noticia_user1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"block md:hidden p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-20 h-16 flex-shrink-0\",\n                                                        children: noticia.imagenUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: noticia.imagenUrl,\n                                                            alt: noticia.titulo,\n                                                            className: \"w-full h-full object-cover rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-6 w-6 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: new Date(noticia.createdAt).toLocaleDateString('es-ES', {\n                                                                            day: '2-digit',\n                                                                            month: 'short'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-0.5 rounded-full text-xs font-medium \".concat(noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : noticia.estado === 'APROBADA' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'),\n                                                                        children: noticia.estado === 'PUBLICADA' ? 'Publicado' : noticia.estado === 'APROBADA' ? 'Aprobada' : noticia.estado === 'EN_REVISION' ? 'En Revisión' : 'Borrador'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer line-clamp-2 mb-2\",\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id)),\n                                                                children: noticia.titulo\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 dark:text-gray-300\",\n                                                                children: [\n                                                                    \"Por: \",\n                                                                    ((_noticia_user = noticia.user) === null || _noticia_user === void 0 ? void 0 : _noticia_user.name) || 'N/A'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this),\n                                            noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-sm\",\n                                                        style: {\n                                                            backgroundColor: noticia.categoria.color\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-blue-600 dark:text-blue-400 font-medium\",\n                                                        children: noticia.categoria.nombre\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id)),\n                                                                className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors duration-200\",\n                                                                title: \"Ver noticia\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Ver\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id, \"/editar\")),\n                                                                className: \"flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors duration-200\",\n                                                                title: \"Editar noticia\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Editar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDelete(noticia.id),\n                                                                className: \"p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200\",\n                                                                title: \"Eliminar noticia\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: noticia.imagenUrl ? 1 : 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center p-4 lg:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-start space-y-2 w-28 lg:w-32 xl:w-36 flex-shrink-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400 font-medium\",\n                                                        children: [\n                                                            new Date(noticia.createdAt).toLocaleDateString('es-ES', {\n                                                                day: '2-digit',\n                                                                month: 'short'\n                                                            }),\n                                                            \", \",\n                                                            new Date(noticia.createdAt).toLocaleTimeString('es-ES', {\n                                                                hour: '2-digit',\n                                                                minute: '2-digit'\n                                                            }),\n                                                            \" hs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 lg:px-3 py-1 rounded-full text-xs font-medium \".concat(noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : noticia.estado === 'APROBADA' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'),\n                                                        children: noticia.estado === 'PUBLICADA' ? 'Publicado' : noticia.estado === 'APROBADA' ? 'Aprobada' : noticia.estado === 'EN_REVISION' ? 'En Revisión' : 'Borrador'\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-600 dark:text-gray-300 font-medium\",\n                                                        children: [\n                                                            \"Por: \",\n                                                            ((_noticia_user1 = noticia.user) === null || _noticia_user1 === void 0 ? void 0 : _noticia_user1.name) || 'N/A'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 px-3 lg:px-4 xl:px-6 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm lg:text-base xl:text-lg font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer line-clamp-2 mb-2\",\n                                                        onClick: ()=>router.push(\"/noticias/\".concat(noticia.id)),\n                                                        children: noticia.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-sm\",\n                                                                style: {\n                                                                    backgroundColor: noticia.categoria.color\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-blue-600 dark:text-blue-400 font-medium\",\n                                                                children: noticia.categoria.nombre\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-16 lg:w-24 lg:h-20 xl:w-28 xl:h-22 flex-shrink-0 ml-3 lg:ml-4\",\n                                                children: noticia.imagenUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: noticia.imagenUrl,\n                                                    alt: noticia.titulo,\n                                                    className: \"w-full h-full object-cover rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 lg:h-8 lg:w-8 text-gray-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center space-y-3 ml-3 lg:ml-4 xl:ml-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 lg:space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id)),\n                                                                className: \"p-2 lg:p-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200\",\n                                                                title: \"Ver noticia\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id, \"/editar\")),\n                                                                className: \"p-2 lg:p-2.5 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200\",\n                                                                title: \"Editar noticia\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDelete(noticia.id),\n                                                                className: \"p-2 lg:p-2.5 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200\",\n                                                                title: \"Eliminar noticia\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center space-y-1 text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-200 dark:border-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: noticia.imagenUrl ? 1 : 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, noticia.id, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-8 text-center text-gray-500 dark:text-gray-400\",\n                            children: \"No se encontraron noticias\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    pagination && pagination.pages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 md:mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 px-4 md:px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                    children: [\n                                        \"Mostrando p\\xe1gina \",\n                                        pagination.page,\n                                        \" de \",\n                                        pagination.pages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                            disabled: currentPage === 1,\n                                            className: \"px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Anterior\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(Math.min(pagination.pages, currentPage + 1)),\n                                            disabled: currentPage === pagination.pages,\n                                            className: \"px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Siguiente\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s(NoticiasPage, \"R7WPURfPh6LohfV7Y8VK8xmZ5Bo=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NoticiasPage;\nvar _c;\n$RefreshReg$(_c, \"NoticiasPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/noticias/page.tsx\n"));

/***/ })

});