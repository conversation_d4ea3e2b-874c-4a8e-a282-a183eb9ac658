{"name": "panel-unificado-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3010", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:push": "prisma db push", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.10.1", "@radix-ui/react-slot": "^1.2.3", "@shadcn/ui": "^0.0.4", "@types/bcrypt": "^5.0.2", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "lucide-react": "^0.523.0", "next": "15.3.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.19.4", "@types/react": "^19.1.8", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}}