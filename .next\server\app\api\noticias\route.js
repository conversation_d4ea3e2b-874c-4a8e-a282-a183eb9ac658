/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/noticias/route";
exports.ids = ["app/api/noticias/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2Froute&page=%2Fapi%2Fnoticias%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2Froute&page=%2Fapi%2Fnoticias%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_DEL_SUR_FINAL_project_bolt_sb1_mtrb5vnz_panel_unificado_v2_src_app_api_noticias_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/noticias/route.ts */ \"(rsc)/./src/app/api/noticias/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/noticias/route\",\n        pathname: \"/api/noticias\",\n        filename: \"route\",\n        bundlePath: \"app/api/noticias/route\"\n    },\n    resolvedPagePath: \"G:\\\\DEL SUR FINAL\\\\project-bolt-sb1-mtrb5vnz\\\\panel-unificado-v2\\\\src\\\\app\\\\api\\\\noticias\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_DEL_SUR_FINAL_project_bolt_sb1_mtrb5vnz_panel_unificado_v2_src_app_api_noticias_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjRfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZub3RpY2lhcyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGbm90aWNpYXMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZub3RpY2lhcyUyRnJvdXRlLnRzJmFwcERpcj1HJTNBJTVDREVMJTIwU1VSJTIwRklOQUwlNUNwcm9qZWN0LWJvbHQtc2IxLW10cmI1dm56JTVDcGFuZWwtdW5pZmljYWRvLXYyJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1HJTNBJTVDREVMJTIwU1VSJTIwRklOQUwlNUNwcm9qZWN0LWJvbHQtc2IxLW10cmI1dm56JTVDcGFuZWwtdW5pZmljYWRvLXYyJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNtRDtBQUNoSTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRzpcXFxcREVMIFNVUiBGSU5BTFxcXFxwcm9qZWN0LWJvbHQtc2IxLW10cmI1dm56XFxcXHBhbmVsLXVuaWZpY2Fkby12MlxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxub3RpY2lhc1xcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvbm90aWNpYXMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9ub3RpY2lhc1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvbm90aWNpYXMvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJHOlxcXFxERUwgU1VSIEZJTkFMXFxcXHByb2plY3QtYm9sdC1zYjEtbXRyYjV2bnpcXFxccGFuZWwtdW5pZmljYWRvLXYyXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXG5vdGljaWFzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2Froute&page=%2Fapi%2Fnoticias%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/noticias/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/noticias/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n// GET /api/noticias - Obtener todas las noticias\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const categoria = searchParams.get('categoria');\n        const estado = searchParams.get('estado');\n        const search = searchParams.get('search');\n        const skip = (page - 1) * limit;\n        // Construir filtros\n        const where = {};\n        if (categoria) {\n            where.categoriaId = parseInt(categoria);\n        }\n        if (estado) {\n            where.estado = estado;\n        }\n        if (search) {\n            where.OR = [\n                {\n                    titulo: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    volanta: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    contenido: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        // Obtener noticias con relaciones\n        const noticias = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.findMany({\n            where,\n            include: {\n                categoria: true,\n                user: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            skip,\n            take: limit\n        });\n        // Contar total para paginación\n        const total = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.count({\n            where\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            noticias,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Error al obtener noticias:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/noticias - Crear nueva noticia\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        console.log('Session user ID:', session.user.id, 'Type:', typeof session.user.id);\n        // Verificar si es FormData o JSON\n        const contentType = request.headers.get('content-type');\n        let formData = {};\n        if (contentType?.includes('multipart/form-data')) {\n            // Manejar FormData\n            const formDataObj = await request.formData();\n            formData = {\n                volanta: formDataObj.get('volanta'),\n                titulo: formDataObj.get('titulo'),\n                resumen: formDataObj.get('resumen'),\n                contenido: formDataObj.get('contenido'),\n                imagenUrl: formDataObj.get('imagenUrl'),\n                categoriaId: formDataObj.get('categoriaId')\n            };\n        } else {\n            // Manejar JSON\n            formData = await request.json();\n        }\n        console.log('Form data received:', formData);\n        const { volanta, titulo, resumen, contenido, imagenUrl, categoriaId } = formData;\n        // Validaciones básicas\n        if (!volanta || !titulo || !contenido) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Volanta, título y contenido son requeridos'\n            }, {\n                status: 400\n            });\n        }\n        // Verificar que el usuario existe\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: parseInt(session.user.id)\n            }\n        });\n        if (!user) {\n            console.log('User not found with ID:', session.user.id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Usuario no encontrado. Por favor, cierre sesión e inicie sesión nuevamente.'\n            }, {\n                status: 401\n            });\n        }\n        console.log('User found:', user.id, user.email);\n        // Verificar que la categoría existe si se proporciona\n        if (categoriaId) {\n            const categoria = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.categoria.findUnique({\n                where: {\n                    id: parseInt(categoriaId)\n                }\n            });\n            if (!categoria) {\n                console.log('Category not found with ID:', categoriaId);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Categoría no encontrada'\n                }, {\n                    status: 404\n                });\n            }\n            console.log('Category found:', categoria.id, categoria.nombre);\n        }\n        const dataToCreate = {\n            volanta,\n            titulo,\n            resumen,\n            contenido,\n            imagenUrl,\n            categoriaId: categoriaId ? parseInt(categoriaId) : null,\n            estado: 'BORRADOR',\n            userId: parseInt(session.user.id)\n        };\n        console.log('Data to create:', dataToCreate);\n        // Crear la noticia\n        const noticia = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.create({\n            data: dataToCreate,\n            include: {\n                categoria: true,\n                user: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        console.log('Noticia created successfully:', noticia.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(noticia, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error al crear noticia:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/noticias/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const user = await prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        }\n                    });\n                    if (!user) {\n                        return null;\n                    }\n                    const isPasswordValid = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        return null;\n                    }\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error during authentication:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    },\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ2tFO0FBQ3BCO0FBQ2xCO0FBRTVCLE1BQU1HLFNBQVMsSUFBSUYsd0RBQVlBO0FBRXhCLE1BQU1HLGNBQStCO0lBQzFDQyxXQUFXO1FBQ1RMLDJFQUFtQkEsQ0FBQztZQUNsQk0sTUFBTTtZQUNOQyxhQUFhO2dCQUNYQyxPQUFPO29CQUFFQyxPQUFPO29CQUFTQyxNQUFNO2dCQUFRO2dCQUN2Q0MsVUFBVTtvQkFBRUYsT0FBTztvQkFBWUMsTUFBTTtnQkFBVztZQUNsRDtZQUNBLE1BQU1FLFdBQVVMLFdBQVc7Z0JBQ3pCLElBQUksQ0FBQ0EsYUFBYUMsU0FBUyxDQUFDRCxhQUFhSSxVQUFVO29CQUNqRCxPQUFPO2dCQUNUO2dCQUVBLElBQUk7b0JBQ0YsTUFBTUUsT0FBTyxNQUFNVixPQUFPVSxJQUFJLENBQUNDLFVBQVUsQ0FBQzt3QkFDeENDLE9BQU87NEJBQ0xQLE9BQU9ELFlBQVlDLEtBQUs7d0JBQzFCO29CQUNGO29CQUVBLElBQUksQ0FBQ0ssTUFBTTt3QkFDVCxPQUFPO29CQUNUO29CQUVBLE1BQU1HLGtCQUFrQixNQUFNZCxxREFBYyxDQUMxQ0ssWUFBWUksUUFBUSxFQUNwQkUsS0FBS0YsUUFBUTtvQkFHZixJQUFJLENBQUNLLGlCQUFpQjt3QkFDcEIsT0FBTztvQkFDVDtvQkFFQSxPQUFPO3dCQUNMRSxJQUFJTCxLQUFLSyxFQUFFLENBQUNDLFFBQVE7d0JBQ3BCWCxPQUFPSyxLQUFLTCxLQUFLO3dCQUNqQkYsTUFBTU8sS0FBS1AsSUFBSTt3QkFDZmMsTUFBTVAsS0FBS08sSUFBSTtvQkFDakI7Z0JBQ0YsRUFBRSxPQUFPQyxPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtvQkFDOUMsT0FBTztnQkFDVDtZQUNGO1FBQ0Y7S0FDRDtJQUNERSxTQUFTO1FBQ1BDLFVBQVU7SUFDWjtJQUNBQyxXQUFXO1FBQ1QsTUFBTUMsS0FBSSxFQUFFQyxLQUFLLEVBQUVkLElBQUksRUFBRTtZQUN2QixJQUFJQSxNQUFNO2dCQUNSYyxNQUFNUCxJQUFJLEdBQUdQLEtBQUtPLElBQUk7Z0JBQ3RCTyxNQUFNVCxFQUFFLEdBQUdMLEtBQUtLLEVBQUU7WUFDcEI7WUFDQSxPQUFPUztRQUNUO1FBQ0EsTUFBTUosU0FBUSxFQUFFQSxPQUFPLEVBQUVJLEtBQUssRUFBRTtZQUM5QixJQUFJQSxTQUFTSixRQUFRVixJQUFJLEVBQUU7Z0JBQ3pCVSxRQUFRVixJQUFJLENBQUNLLEVBQUUsR0FBR1MsTUFBTVQsRUFBRTtnQkFDMUJLLFFBQVFWLElBQUksQ0FBQ08sSUFBSSxHQUFHTyxNQUFNUCxJQUFJO1lBQ2hDO1lBQ0EsT0FBT0c7UUFDVDtJQUNGO0lBQ0FLLE9BQU87UUFDTEMsUUFBUTtJQUNWO0lBQ0FDLFFBQVFDLFFBQVFDLEdBQUcsQ0FBQ0MsZUFBZSxJQUFJO0FBQ3pDLEVBQUUiLCJzb3VyY2VzIjpbIkc6XFxERUwgU1VSIEZJTkFMXFxwcm9qZWN0LWJvbHQtc2IxLW10cmI1dm56XFxwYW5lbC11bmlmaWNhZG8tdjJcXHNyY1xcbGliXFxhdXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRBdXRoT3B0aW9ucyB9IGZyb20gJ25leHQtYXV0aCc7XHJcbmltcG9ydCBDcmVkZW50aWFsc1Byb3ZpZGVyIGZyb20gJ25leHQtYXV0aC9wcm92aWRlcnMvY3JlZGVudGlhbHMnO1xyXG5pbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XHJcbmltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0JztcclxuXHJcbmNvbnN0IHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmV4cG9ydCBjb25zdCBhdXRoT3B0aW9uczogTmV4dEF1dGhPcHRpb25zID0ge1xyXG4gIHByb3ZpZGVyczogW1xyXG4gICAgQ3JlZGVudGlhbHNQcm92aWRlcih7XHJcbiAgICAgIG5hbWU6ICdjcmVkZW50aWFscycsXHJcbiAgICAgIGNyZWRlbnRpYWxzOiB7XHJcbiAgICAgICAgZW1haWw6IHsgbGFiZWw6ICdFbWFpbCcsIHR5cGU6ICdlbWFpbCcgfSxcclxuICAgICAgICBwYXNzd29yZDogeyBsYWJlbDogJ1Bhc3N3b3JkJywgdHlwZTogJ3Bhc3N3b3JkJyB9XHJcbiAgICAgIH0sXHJcbiAgICAgIGFzeW5jIGF1dGhvcml6ZShjcmVkZW50aWFscykge1xyXG4gICAgICAgIGlmICghY3JlZGVudGlhbHM/LmVtYWlsIHx8ICFjcmVkZW50aWFscz8ucGFzc3dvcmQpIHtcclxuICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBwcmlzbWEudXNlci5maW5kVW5pcXVlKHtcclxuICAgICAgICAgICAgd2hlcmU6IHtcclxuICAgICAgICAgICAgICBlbWFpbDogY3JlZGVudGlhbHMuZW1haWxcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgaWYgKCF1c2VyKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGNvbnN0IGlzUGFzc3dvcmRWYWxpZCA9IGF3YWl0IGJjcnlwdC5jb21wYXJlKFxyXG4gICAgICAgICAgICBjcmVkZW50aWFscy5wYXNzd29yZCxcclxuICAgICAgICAgICAgdXNlci5wYXNzd29yZFxyXG4gICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICBpZiAoIWlzUGFzc3dvcmRWYWxpZCkge1xyXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBpZDogdXNlci5pZC50b1N0cmluZygpLFxyXG4gICAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICAgICAgbmFtZTogdXNlci5uYW1lLFxyXG4gICAgICAgICAgICByb2xlOiB1c2VyLnJvbGUsXHJcbiAgICAgICAgICB9O1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkdXJpbmcgYXV0aGVudGljYXRpb246JywgZXJyb3IpO1xyXG4gICAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9KVxyXG4gIF0sXHJcbiAgc2Vzc2lvbjoge1xyXG4gICAgc3RyYXRlZ3k6ICdqd3QnLFxyXG4gIH0sXHJcbiAgY2FsbGJhY2tzOiB7XHJcbiAgICBhc3luYyBqd3QoeyB0b2tlbiwgdXNlciB9KSB7XHJcbiAgICAgIGlmICh1c2VyKSB7XHJcbiAgICAgICAgdG9rZW4ucm9sZSA9IHVzZXIucm9sZTtcclxuICAgICAgICB0b2tlbi5pZCA9IHVzZXIuaWQ7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIHRva2VuO1xyXG4gICAgfSxcclxuICAgIGFzeW5jIHNlc3Npb24oeyBzZXNzaW9uLCB0b2tlbiB9KSB7XHJcbiAgICAgIGlmICh0b2tlbiAmJiBzZXNzaW9uLnVzZXIpIHtcclxuICAgICAgICBzZXNzaW9uLnVzZXIuaWQgPSB0b2tlbi5pZDtcclxuICAgICAgICBzZXNzaW9uLnVzZXIucm9sZSA9IHRva2VuLnJvbGU7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIHNlc3Npb247XHJcbiAgICB9LFxyXG4gIH0sXHJcbiAgcGFnZXM6IHtcclxuICAgIHNpZ25JbjogJy9hdXRoL3NpZ25pbicsXHJcbiAgfSxcclxuICBzZWNyZXQ6IHByb2Nlc3MuZW52Lk5FWFRBVVRIX1NFQ1JFVCB8fCAneW91ci1zZWNyZXQta2V5LWNoYW5nZS1pbi1wcm9kdWN0aW9uJyxcclxufTsgIl0sIm5hbWVzIjpbIkNyZWRlbnRpYWxzUHJvdmlkZXIiLCJQcmlzbWFDbGllbnQiLCJiY3J5cHQiLCJwcmlzbWEiLCJhdXRoT3B0aW9ucyIsInByb3ZpZGVycyIsIm5hbWUiLCJjcmVkZW50aWFscyIsImVtYWlsIiwibGFiZWwiLCJ0eXBlIiwicGFzc3dvcmQiLCJhdXRob3JpemUiLCJ1c2VyIiwiZmluZFVuaXF1ZSIsIndoZXJlIiwiaXNQYXNzd29yZFZhbGlkIiwiY29tcGFyZSIsImlkIiwidG9TdHJpbmciLCJyb2xlIiwiZXJyb3IiLCJjb25zb2xlIiwic2Vzc2lvbiIsInN0cmF0ZWd5IiwiY2FsbGJhY2tzIiwiand0IiwidG9rZW4iLCJwYWdlcyIsInNpZ25JbiIsInNlY3JldCIsInByb2Nlc3MiLCJlbnYiLCJORVhUQVVUSF9TRUNSRVQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkc6XFxERUwgU1VSIEZJTkFMXFxwcm9qZWN0LWJvbHQtc2IxLW10cmI1dm56XFxwYW5lbC11bmlmaWNhZG8tdjJcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcclxuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZDtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hOyAiXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@babel+runtime@7.27.6","vendor-chunks/jose@4.15.9","vendor-chunks/openid-client@5.7.1","vendor-chunks/oauth@0.9.15","vendor-chunks/object-hash@2.2.0","vendor-chunks/preact@10.26.9","vendor-chunks/uuid@8.3.2","vendor-chunks/yallist@4.0.0","vendor-chunks/preact-render-to-string@5.2.6_preact@10.26.9","vendor-chunks/lru-cache@6.0.0","vendor-chunks/cookie@0.7.2","vendor-chunks/oidc-token-hash@5.1.0","vendor-chunks/@panva+hkdf@1.2.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2Froute&page=%2Fapi%2Fnoticias%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();