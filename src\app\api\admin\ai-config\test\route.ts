import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { testOpenAIConnection, testGeminiConnection } from '@/lib/ai-service';

// POST - Probar conexión con proveedores de IA
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { provider, apiKey } = body;

    if (!provider || !['OPENAI', 'GEMINI'].includes(provider)) {
      return NextResponse.json(
        { error: 'Proveedor inválido' },
        { status: 400 }
      );
    }

    let isConnected = false;
    let error = null;

    try {
      console.log(`🧪 Iniciando prueba de conexión para ${provider}`);

      if (provider === 'OPENAI') {
        isConnected = await testOpenAIConnection();
      } else if (provider === 'GEMINI') {
        isConnected = await testGeminiConnection();
      }

      console.log(`🧪 Resultado de prueba ${provider}:`, { isConnected, error });

    } catch (testError) {
      console.error(`❌ Error en prueba de ${provider}:`, testError);
      error = testError instanceof Error ? testError.message : 'Error desconocido';
    }

    return NextResponse.json({
      provider,
      isConnected,
      error,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error testing AI connection:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
