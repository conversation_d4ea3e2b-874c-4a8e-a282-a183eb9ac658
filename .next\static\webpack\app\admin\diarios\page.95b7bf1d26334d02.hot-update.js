"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/diarios/page",{

/***/ "(app-pages-browser)/./src/app/admin/diarios/page.tsx":
/*!****************************************!*\
  !*** ./src/app/admin/diarios/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDiariosPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Bot,Edit,Plus,Save,TestTube!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDiariosPage() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [diarios, setDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [editingDiario, setEditingDiario] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [showNewForm, setShowNewForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [testingPrompt, setTestingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Form state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        nombre: '',\n        descripcion: '',\n        prompt: '',\n        isActive: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AdminDiariosPage.useEffect\": ()=>{\n            var _session_user;\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            } else if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== 'ADMIN') {\n                router.push('/dashboard');\n            }\n        }\n    }[\"AdminDiariosPage.useEffect\"], [\n        status,\n        session,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AdminDiariosPage.useEffect\": ()=>{\n            var _session_user;\n            if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'ADMIN') {\n                loadDiarios();\n            }\n        }\n    }[\"AdminDiariosPage.useEffect\"], [\n        session\n    ]);\n    const loadDiarios = async ()=>{\n        try {\n            const response = await fetch('/api/admin/diarios-config');\n            if (response.ok) {\n                const data = await response.json();\n                setDiarios(data.diarios);\n            } else {\n                alert('Error al cargar diarios');\n            }\n        } catch (error) {\n            console.error('Error al cargar diarios:', error);\n            alert('Error al cargar diarios');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEdit = (diario)=>{\n        setEditingDiario(diario);\n        setFormData({\n            nombre: diario.nombre,\n            descripcion: diario.descripcion || '',\n            prompt: diario.prompt,\n            isActive: diario.isActive\n        });\n        setShowNewForm(false);\n    };\n    const handleNew = ()=>{\n        setEditingDiario(null);\n        setFormData({\n            nombre: '',\n            descripcion: '',\n            prompt: 'Reescribe la siguiente noticia manteniendo la información principal pero adaptando el estilo y tono:',\n            isActive: true\n        });\n        setShowNewForm(true);\n    };\n    const handleCancel = ()=>{\n        setEditingDiario(null);\n        setShowNewForm(false);\n        setFormData({\n            nombre: '',\n            descripcion: '',\n            prompt: '',\n            isActive: true\n        });\n    };\n    const handleSave = async ()=>{\n        if (!formData.nombre.trim() || !formData.prompt.trim()) {\n            alert('Nombre y prompt son requeridos');\n            return;\n        }\n        setSaving(true);\n        try {\n            const url = editingDiario ? '/api/admin/diarios-config' : '/api/admin/diarios-config';\n            const method = editingDiario ? 'PUT' : 'POST';\n            const body = editingDiario ? {\n                ...formData,\n                diarioId: editingDiario.id\n            } : formData;\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(body)\n            });\n            if (response.ok) {\n                alert(editingDiario ? '✅ Diario actualizado' : '✅ Diario creado');\n                await loadDiarios();\n                handleCancel();\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al guardar:', error);\n            alert('Error al guardar el diario');\n        } finally{\n            setSaving(false);\n        }\n    };\n    const testPrompt = async (diario)=>{\n        setTestingPrompt(diario.id);\n        try {\n            // Simular test del prompt (aquí podrías hacer una llamada real a OpenAI)\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            alert(\"✅ Prompt de \".concat(diario.nombre, \" probado exitosamente\"));\n        } catch (error) {\n            alert(\"❌ Error al probar prompt de \".concat(diario.nombre));\n        } finally{\n            setTestingPrompt(null);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando configuraci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/admin'),\n                                        className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Volver al Admin\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-6 w-6 mr-2 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Configuraci\\xf3n de Diarios IA\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Gestiona los prompts y configuraci\\xf3n para la reescritura autom\\xe1tica\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleNew,\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Nuevo Diario\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    (editingDiario || showNewForm) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 bg-white shadow-lg rounded-lg p-6 border-l-4 border-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: editingDiario ? \"Editar \".concat(editingDiario.nombre) : 'Crear Nuevo Diario'\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCancel,\n                                        className: \"text-gray-500 hover:text-gray-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: [\n                                                    \"Nombre del Diario \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.nombre,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        nombre: e.target.value\n                                                    }),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                                                placeholder: \"Ej: Telesoldiario\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Descripci\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.descripcion,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        descripcion: e.target.value\n                                                    }),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                                                placeholder: \"Descripci\\xf3n breve del diario\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: [\n                                                    \"Prompt de Reescritura \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.prompt,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        prompt: e.target.value\n                                                    }),\n                                                rows: 6,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm\",\n                                                placeholder: \"Instrucciones para la IA sobre c\\xf3mo reescribir las noticias para este diario...\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-xs text-gray-500\",\n                                                children: \"Este prompt se usar\\xe1 para instruir a la IA sobre c\\xf3mo adaptar las noticias al estilo de este diario.\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"isActive\",\n                                                checked: formData.isActive,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        isActive: e.target.checked\n                                                    }),\n                                                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"isActive\",\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Diario activo (disponible para generar versiones)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-end space-x-4 pt-4 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancel,\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSave,\n                                                disabled: saving,\n                                                className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                                children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Guardando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: editingDiario ? 'Actualizar' : 'Crear'\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: [\n                                        \"Diarios Configurados (\",\n                                        diarios.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            diarios.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No hay diarios configurados\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Crea tu primer diario para comenzar a generar versiones con IA.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNew,\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Crear Primer Diario\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y divide-gray-200\",\n                                children: diarios.map((diario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                    children: diario.nombre\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(diario.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                    children: diario.isActive ? 'Activo' : 'Inactivo'\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        diario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-3\",\n                                                            children: diario.descripcion\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Prompt de Reescritura\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1 p-3 bg-gray-50 rounded text-sm text-gray-700 font-mono max-h-32 overflow-y-auto\",\n                                                                    children: diario.prompt\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Creado: \",\n                                                                new Date(diario.createdAt).toLocaleDateString(),\n                                                                \" | Actualizado: \",\n                                                                new Date(diario.updatedAt).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 ml-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>testPrompt(diario),\n                                                            disabled: testingPrompt === diario.id,\n                                                            className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                                            children: testingPrompt === diario.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Probando...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                        lineNumber: 388,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Probar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleEdit(diario),\n                                                            className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Editar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, diario.id, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Bot_Edit_Plus_Save_TestTube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600 mt-0.5 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                                            children: \"Informaci\\xf3n sobre los Prompts\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-800 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Los prompts definen c\\xf3mo la IA reescribir\\xe1 las noticias para cada diario.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• S\\xe9 espec\\xedfico sobre el tono, estilo y formato deseado.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Puedes incluir instrucciones sobre la audiencia objetivo del diario.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Los cambios en los prompts afectar\\xe1n todas las futuras generaciones.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\diarios\\\\page.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDiariosPage, \"B3ncyc79CESnIAVt/VDpxR8teH0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminDiariosPage;\nvar _c;\n$RefreshReg$(_c, \"AdminDiariosPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/diarios/page.tsx\n"));

/***/ })

});