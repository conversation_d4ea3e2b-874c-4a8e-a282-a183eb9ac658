import OpenAI from 'openai';

// Configuración de OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'your-openai-api-key-here',
});

export interface RewriteRequest {
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  prompt: string;
  diarioNombre: string;
}

export interface RewriteResponse {
  titulo: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  metadatos: {
    modelo: string;
    tokens_usados: number;
    tiempo_generacion: number;
    diario: string;
  };
}

export async function rewriteNoticia(request: RewriteRequest): Promise<RewriteResponse> {
  const startTime = Date.now();

  try {
    // Construir el prompt completo
    const fullPrompt = `${request.prompt}

NOTICIA ORIGINAL:
Volanta: ${request.volanta || 'Sin volanta'}
Título: ${request.titulo}
Subtítulo: ${request.subtitulo || 'Sin subtítulo'}
Resumen: ${request.resumen || 'Sin resumen'}
Contenido: ${request.contenido}

INSTRUCCIONES:
- Reescribe completamente la noticia para ${request.diarioNombre}
- Mantén la información factual pero adapta el estilo y tono
- NO generes subtítulo, solo volanta, título, resumen y contenido
- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:
{
  "volanta": "nueva volanta",
  "titulo": "nuevo título",
  "resumen": "nuevo resumen",
  "contenido": "nuevo contenido completo"
}

No incluyas explicaciones adicionales, solo el JSON.`;

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional."
        },
        {
          role: "user",
          content: fullPrompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const responseText = completion.choices[0]?.message?.content;
    if (!responseText) {
      throw new Error('No se recibió respuesta de OpenAI');
    }

    // Intentar parsear la respuesta JSON
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(responseText);
    } catch (parseError) {
      // Si falla el parsing, intentar extraer JSON del texto
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        parsedResponse = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('La respuesta de OpenAI no está en formato JSON válido');
      }
    }

    const endTime = Date.now();
    const generationTime = endTime - startTime;

    return {
      titulo: parsedResponse.titulo || request.titulo,
      volanta: parsedResponse.volanta,
      contenido: parsedResponse.contenido || request.contenido,
      resumen: parsedResponse.resumen,
      metadatos: {
        modelo: completion.model,
        tokens_usados: completion.usage?.total_tokens || 0,
        tiempo_generacion: generationTime,
        diario: request.diarioNombre,
      }
    };

  } catch (error) {
    console.error('Error en rewriteNoticia:', error);
    throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);
  }
}

export async function testOpenAIConnection(): Promise<boolean> {
  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: "Responde solo con 'OK'" }],
      max_tokens: 5,
    });

    return completion.choices[0]?.message?.content?.includes('OK') || false;
  } catch (error) {
    console.error('Error testing OpenAI connection:', error);
    return false;
  }
}