import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface RewriteRequest {
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  prompt: string;
  diarioNombre: string;
}

export interface RewriteResponse {
  titulo: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  metadatos: {
    modelo: string;
    tokens_usados: number;
    tiempo_generacion: number;
    diario: string;
    proveedor: string;
  };
}

export interface AIConfig {
  // OpenAI
  openaiApiKey?: string;
  openaiModel: string;
  openaiMaxTokens: number;
  openaiTemperature: number;
  
  // Gemini
  geminiApiKey?: string;
  geminiModel: string;
  geminiMaxTokens: number;
  geminiTemperature: number;
  
  // Global
  defaultProvider: 'OPENAI' | 'GEMINI';
}

// Obtener configuración de IA
export async function getAIConfig(): Promise<AIConfig> {
  let config = await prisma.aIConfig.findFirst({
    where: { isActive: true },
    orderBy: { createdAt: 'desc' }
  });

  // Migrar modelos obsoletos
  if (config && (config.geminiModel === 'gemini-pro' || config.geminiModel === 'gemini-1.5-flash')) {
    const newModel = 'gemini-2.5-flash';
    console.log(`🔄 Migrando modelo Gemini obsoleto de ${config.geminiModel} a ${newModel}`);
    config = await prisma.aIConfig.update({
      where: { id: config.id },
      data: { geminiModel: newModel }
    });
  }

  if (!config) {
    // Configuración por defecto
    return {
      openaiApiKey: process.env.OPENAI_API_KEY,
      openaiModel: 'gpt-3.5-turbo',
      openaiMaxTokens: 2000,
      openaiTemperature: 0.7,
      geminiApiKey: process.env.GEMINI_API_KEY,
      geminiModel: 'gemini-2.5-flash',
      geminiMaxTokens: 2000,
      geminiTemperature: 0.7,
      defaultProvider: 'OPENAI'
    };
  }

  return {
    openaiApiKey: config.openaiApiKey || process.env.OPENAI_API_KEY,
    openaiModel: config.openaiModel,
    openaiMaxTokens: config.openaiMaxTokens,
    openaiTemperature: config.openaiTemperature,
    geminiApiKey: config.geminiApiKey || process.env.GEMINI_API_KEY,
    geminiModel: config.geminiModel,
    geminiMaxTokens: config.geminiMaxTokens,
    geminiTemperature: config.geminiTemperature,
    defaultProvider: config.defaultProvider as 'OPENAI' | 'GEMINI'
  };
}

// Reescribir noticia usando OpenAI
async function rewriteWithOpenAI(request: RewriteRequest, config: AIConfig): Promise<RewriteResponse> {
  const startTime = Date.now();

  if (!config.openaiApiKey) {
    throw new Error('OpenAI API key no configurada');
  }

  const openai = new OpenAI({
    apiKey: config.openaiApiKey,
  });

  const fullPrompt = `${request.prompt}

NOTICIA ORIGINAL:
Volanta: ${request.volanta || 'Sin volanta'}
Título: ${request.titulo}
Subtítulo: ${request.subtitulo || 'Sin subtítulo'}
Resumen: ${request.resumen || 'Sin resumen'}
Contenido: ${request.contenido}

INSTRUCCIONES:
- Reescribe completamente la noticia para ${request.diarioNombre}
- Mantén la información factual pero adapta el estilo y tono
- NO generes subtítulo, solo volanta, título, resumen y contenido
- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:
{
  "volanta": "nueva volanta",
  "titulo": "nuevo título",
  "resumen": "nuevo resumen",
  "contenido": "nuevo contenido completo"
}

No incluyas explicaciones adicionales, solo el JSON.`;

  const completion = await openai.chat.completions.create({
    model: config.openaiModel,
    messages: [
      {
        role: "system",
        content: "Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional."
      },
      {
        role: "user",
        content: fullPrompt
      }
    ],
    temperature: config.openaiTemperature,
    max_tokens: config.openaiMaxTokens,
  });

  const responseText = completion.choices[0]?.message?.content;
  if (!responseText) {
    throw new Error('No se recibió respuesta de OpenAI');
  }

  // Intentar parsear la respuesta JSON
  let parsedResponse;
  try {
    parsedResponse = JSON.parse(responseText);
  } catch (parseError) {
    // Si falla el parsing, intentar extraer JSON del texto
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      parsedResponse = JSON.parse(jsonMatch[0]);
    } else {
      throw new Error('La respuesta de OpenAI no está en formato JSON válido');
    }
  }

  const endTime = Date.now();
  const generationTime = endTime - startTime;

  return {
    titulo: parsedResponse.titulo || request.titulo,
    volanta: parsedResponse.volanta,
    contenido: parsedResponse.contenido || request.contenido,
    resumen: parsedResponse.resumen,
    metadatos: {
      modelo: completion.model,
      tokens_usados: completion.usage?.total_tokens || 0,
      tiempo_generacion: generationTime,
      diario: request.diarioNombre,
      proveedor: 'OpenAI'
    }
  };
}

// Reescribir noticia usando Google Gemini
async function rewriteWithGemini(request: RewriteRequest, config: AIConfig): Promise<RewriteResponse> {
  const startTime = Date.now();

  if (!config.geminiApiKey) {
    throw new Error('Google Gemini API key no configurada');
  }

  const genAI = new GoogleGenerativeAI(config.geminiApiKey);
  const model = genAI.getGenerativeModel({ 
    model: config.geminiModel,
    generationConfig: {
      temperature: config.geminiTemperature,
      maxOutputTokens: config.geminiMaxTokens,
    }
  });

  const fullPrompt = `${request.prompt}

NOTICIA ORIGINAL:
Volanta: ${request.volanta || 'Sin volanta'}
Título: ${request.titulo}
Subtítulo: ${request.subtitulo || 'Sin subtítulo'}
Resumen: ${request.resumen || 'Sin resumen'}
Contenido: ${request.contenido}

INSTRUCCIONES:
- Reescribe completamente la noticia para ${request.diarioNombre}
- Mantén la información factual pero adapta el estilo y tono
- NO generes subtítulo, solo volanta, título, resumen y contenido
- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:
{
  "volanta": "nueva volanta",
  "titulo": "nuevo título",
  "resumen": "nuevo resumen",
  "contenido": "nuevo contenido completo"
}

No incluyas explicaciones adicionales, solo el JSON.`;

  const result = await model.generateContent(fullPrompt);
  const response = await result.response;
  const responseText = response.text();

  if (!responseText) {
    throw new Error('No se recibió respuesta de Google Gemini');
  }

  // Intentar parsear la respuesta JSON
  let parsedResponse;
  try {
    parsedResponse = JSON.parse(responseText);
  } catch (parseError) {
    // Si falla el parsing, intentar extraer JSON del texto
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      parsedResponse = JSON.parse(jsonMatch[0]);
    } else {
      throw new Error('La respuesta de Google Gemini no está en formato JSON válido');
    }
  }

  const endTime = Date.now();
  const generationTime = endTime - startTime;

  return {
    titulo: parsedResponse.titulo || request.titulo,
    volanta: parsedResponse.volanta,
    contenido: parsedResponse.contenido || request.contenido,
    resumen: parsedResponse.resumen,
    metadatos: {
      modelo: config.geminiModel,
      tokens_usados: 0, // Gemini no proporciona conteo de tokens en la respuesta
      tiempo_generacion: generationTime,
      diario: request.diarioNombre,
      proveedor: 'Google Gemini'
    }
  };
}

// Función principal para reescribir noticias
export async function rewriteNoticia(request: RewriteRequest, diarioId?: number): Promise<RewriteResponse> {
  try {
    const config = await getAIConfig();
    let provider = config.defaultProvider;

    // Si se especifica un diario, verificar su configuración específica
    if (diarioId) {
      const diario = await prisma.diario.findUnique({
        where: { id: diarioId }
      });

      if (diario && !diario.useGlobalConfig && diario.aiProvider) {
        provider = diario.aiProvider as 'OPENAI' | 'GEMINI';
        
        // Si el diario tiene un modelo específico, usarlo
        if (diario.aiModel) {
          if (provider === 'OPENAI') {
            config.openaiModel = diario.aiModel;
          } else {
            config.geminiModel = diario.aiModel;
          }
        }
      }
    }

    // Ejecutar reescritura según el proveedor
    if (provider === 'GEMINI') {
      return await rewriteWithGemini(request, config);
    } else {
      return await rewriteWithOpenAI(request, config);
    }

  } catch (error) {
    console.error('Error en rewriteNoticia:', error);
    throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);
  }
}

// Función para probar conexión con OpenAI
export async function testOpenAIConnection(): Promise<boolean> {
  try {
    console.log('🔍 Probando conexión con OpenAI...');

    const config = await getAIConfig();
    const keyToUse = config.openaiApiKey || process.env.OPENAI_API_KEY;

    console.log('🔑 API Key disponible:', !!keyToUse);
    console.log('📝 Modelo a usar:', config.openaiModel);

    if (!keyToUse) {
      console.error('❌ No hay API key disponible');
      return false;
    }

    const openai = new OpenAI({
      apiKey: keyToUse,
    });

    console.log('📡 Enviando petición de prueba...');

    const completion = await openai.chat.completions.create({
      model: config.openaiModel,
      messages: [{ role: "user", content: "Responde solo con 'OK'" }],
      max_tokens: 5,
      temperature: 0,
    });

    console.log('📥 Respuesta recibida:', completion.choices[0]?.message?.content);

    const isOk = completion.choices[0]?.message?.content?.includes('OK') || false;
    console.log('✅ Conexión exitosa:', isOk);

    return isOk;
  } catch (error) {
    console.error('❌ Error testing OpenAI connection:', error);
    if (error instanceof Error) {
      console.error('❌ Error message:', error.message);
      console.error('❌ Error stack:', error.stack);
    }
    return false;
  }
}

// Función para probar conexión con Google Gemini
export async function testGeminiConnection(): Promise<boolean> {
  try {
    console.log('🔍 Probando conexión con Google Gemini...');

    const config = await getAIConfig();
    const keyToUse = config.geminiApiKey || process.env.GEMINI_API_KEY;

    console.log('🔑 API Key disponible:', !!keyToUse);
    console.log('📝 Modelo a usar:', config.geminiModel);

    if (!keyToUse) {
      console.error('❌ No hay API key disponible para Gemini');
      return false;
    }

    const genAI = new GoogleGenerativeAI(keyToUse);
    const model = genAI.getGenerativeModel({
      model: config.geminiModel,
      generationConfig: {
        temperature: 0,
        maxOutputTokens: 10,
      }
    });

    console.log('📡 Enviando petición de prueba a Gemini...');

    const result = await model.generateContent("Responde solo con 'OK'");
    const response = await result.response;
    const text = response.text();

    console.log('📥 Respuesta recibida de Gemini:', text);

    const isOk = text.includes('OK');
    console.log('✅ Conexión Gemini exitosa:', isOk);

    return isOk;
  } catch (error) {
    console.error('❌ Error testing Gemini connection:', error);
    if (error instanceof Error) {
      console.error('❌ Error message:', error.message);
      console.error('❌ Error stack:', error.stack);
    }
    return false;
  }
}
