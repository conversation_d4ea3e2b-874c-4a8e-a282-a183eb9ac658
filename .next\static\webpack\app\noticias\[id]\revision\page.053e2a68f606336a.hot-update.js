"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/noticias/[id]/revision/page",{

/***/ "(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/noticias/[id]/revision/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RevisionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RevisionPage() {\n    var _session_user, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = Array.isArray(params.id) ? params.id[0] : params.id;\n    const [noticia, setNoticia] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [versiones, setVersiones] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [expandedVersions, setExpandedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Set());\n    // Estados para generación incremental\n    const [diarios, setDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [showGenerateMore, setShowGenerateMore] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedNewDiarios, setSelectedNewDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        status,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (session && id) {\n                loadData();\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        session,\n        id\n    ]);\n    // Debug: monitorear cambios en selectedNewDiarios\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            console.log('selectedNewDiarios changed:', selectedNewDiarios);\n        }\n    }[\"RevisionPage.useEffect\"], [\n        selectedNewDiarios\n    ]);\n    const loadData = async ()=>{\n        try {\n            // Cargar noticia\n            const noticiaResponse = await fetch(\"/api/noticias/\".concat(id));\n            if (noticiaResponse.ok) {\n                const noticiaData = await noticiaResponse.json();\n                setNoticia(noticiaData);\n            }\n            // Cargar versiones\n            const versionesResponse = await fetch(\"/api/noticias/\".concat(id, \"/versions\"));\n            if (versionesResponse.ok) {\n                const versionesData = await versionesResponse.json();\n                setVersiones(versionesData.versiones);\n                // Expandir todas las versiones por defecto\n                setExpandedVersions(new Set(versionesData.versiones.map((v)=>v.id)));\n            }\n            // Cargar diarios disponibles\n            const diariosResponse = await fetch('/api/diarios');\n            if (diariosResponse.ok) {\n                const diariosData = await diariosResponse.json();\n                setDiarios(diariosData.diarios);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos:', error);\n            alert('Error al cargar los datos');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVersionStateChange = async (versionId, estado)=>{\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    versionId,\n                    estado\n                })\n            });\n            if (response.ok) {\n                await loadData(); // Recargar datos\n                alert(\"✅ Estado actualizado a: \".concat(estado));\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al actualizar estado:', error);\n            alert('Error al actualizar el estado de la versión');\n        }\n    };\n    const toggleVersionExpansion = (versionId)=>{\n        const newExpanded = new Set(expandedVersions);\n        if (newExpanded.has(versionId)) {\n            newExpanded.delete(versionId);\n        } else {\n            newExpanded.add(versionId);\n        }\n        setExpandedVersions(newExpanded);\n    };\n    const getEstadoBadge = (estado)=>{\n        const badges = {\n            'GENERADA': 'bg-blue-100 text-blue-800',\n            'APROBADA': 'bg-green-100 text-green-800',\n            'RECHAZADA': 'bg-red-100 text-red-800',\n            'EN_REVISION': 'bg-yellow-100 text-yellow-800'\n        };\n        return badges[estado] || 'bg-gray-100 text-gray-800';\n    };\n    const getEstadoIcon = (estado)=>{\n        switch(estado){\n            case 'APROBADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 16\n                }, this);\n            case 'RECHAZADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 16\n                }, this);\n            case 'EN_REVISION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // Obtener diarios que ya tienen versiones generadas\n    const getDiariosConVersiones = ()=>{\n        return versiones.map((v)=>v.diario.id);\n    };\n    // Obtener diarios disponibles para generar (que no tienen versiones)\n    const getDiariosDisponibles = ()=>{\n        const diariosConVersiones = getDiariosConVersiones();\n        return diarios.filter((d)=>!diariosConVersiones.includes(d.id));\n    };\n    // Manejar generación incremental\n    const handleGenerateMore = async ()=>{\n        console.log('selectedNewDiarios:', selectedNewDiarios);\n        if (selectedNewDiarios.length === 0) {\n            alert('Selecciona al menos un diario para generar versiones');\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/generate-versions\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    diarioIds: selectedNewDiarios\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                alert(\"✅ \".concat(data.generadas, \" versiones generadas exitosamente\"));\n                // Recargar datos para mostrar las nuevas versiones\n                await loadData();\n                // Limpiar selección y cerrar modal\n                setSelectedNewDiarios([]);\n                setShowGenerateMore(false);\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al generar versiones:', error);\n            alert('Error al generar las versiones');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando revisi\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, this);\n    }\n    if (!noticia) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Noticia no encontrada\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this);\n    }\n    // Verificar permisos\n    const canReview = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'ADMIN' || (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role) === 'EDITOR';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.back(),\n                                        className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Volver\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Revisi\\xf3n de Noticia\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Estado: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: noticia.estado.replace('_', ' ')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 text-sm font-medium rounded-full \".concat(noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800' : noticia.estado === 'APROBADA' ? 'bg-green-100 text-green-800' : noticia.estado === 'PUBLICADA' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'),\n                                    children: noticia.estado.replace('_', ' ')\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-12 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-7 xl:col-span-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 bg-blue-50 border-b border-blue-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-blue-900 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-6 w-6 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Noticia Original\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-sm text-blue-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                noticia.user.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                new Date(noticia.createdAt).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"Categor\\xeda\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                style: {\n                                                                    color: noticia.categoria.color\n                                                                },\n                                                                children: noticia.categoria.nombre\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"Estado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: noticia.estado.replace('_', ' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"Destacada\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: noticia.destacada ? 'Sí' : 'No'\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    noticia.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"Volanta\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-blue-600 font-medium uppercase tracking-wide\",\n                                                                children: noticia.volanta\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"T\\xedtulo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-gray-900 leading-tight\",\n                                                                children: noticia.titulo\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    noticia.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"Subt\\xedtulo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg text-gray-700 font-medium\",\n                                                                children: noticia.subtitulo\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    noticia.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"Resumen\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-base text-gray-700 leading-relaxed\",\n                                                                children: noticia.resumen\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    noticia.imagenUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"Imagen\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: noticia.imagenUrl,\n                                                                alt: noticia.imagenAlt || noticia.titulo,\n                                                                className: \"w-full max-w-2xl h-64 object-cover rounded-lg mt-2\",\n                                                                onError: (e)=>{\n                                                                    e.currentTarget.style.display = 'none';\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                children: \"Contenido\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none\",\n                                                                children: noticia.contenido\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 border-t border-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                            children: [\n                                                                noticia.autor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"Autor:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \" \",\n                                                                        noticia.autor\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                noticia.fuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"Fuente:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \" \",\n                                                                        noticia.fuente\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                noticia.urlFuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"md:col-span-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"URL Fuente:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 427,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        ' ',\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: noticia.urlFuente,\n                                                                            target: \"_blank\",\n                                                                            rel: \"noopener noreferrer\",\n                                                                            className: \"text-blue-600 hover:text-blue-800 underline\",\n                                                                            children: noticia.urlFuente\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-5 xl:col-span-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-bold text-gray-900 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Versiones IA (\",\n                                                versiones.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 mt-1\",\n                                            children: [\n                                                \"Versiones reescritas autom\\xe1ticamente.\",\n                                                canReview && ' Puedes aprobar o rechazar cada una.'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 11\n                                }, this),\n                                diarios.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 bg-white shadow rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Estado de Generaci\\xf3n por Diario\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, this),\n                                                getDiariosDisponibles().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowGenerateMore(!showGenerateMore),\n                                                    className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Generar M\\xe1s Versiones\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4\",\n                                            children: diarios.map((diario)=>{\n                                                const tieneVersion = getDiariosConVersiones().includes(diario.id);\n                                                const version = versiones.find((v)=>v.diario.id === diario.id);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-lg border-2 \".concat(tieneVersion ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900\",\n                                                                    children: diario.nombre\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                tieneVersion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        tieneVersion && version ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getEstadoBadge(version.estado)),\n                                                                    children: version.estado\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: [\n                                                                        \"Generado: \",\n                                                                        new Date(version.createdAt).toLocaleDateString('es-ES', {\n                                                                            day: '2-digit',\n                                                                            month: 'short',\n                                                                            hour: '2-digit',\n                                                                            minute: '2-digit'\n                                                                        })\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Sin versi\\xf3n generada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, diario.id, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 15\n                                        }, this),\n                                        showGenerateMore && getDiariosDisponibles().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-gray-200 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 mb-3\",\n                                                    children: \"Seleccionar Diarios para Generar Versiones\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 mb-4\",\n                                                    children: getDiariosDisponibles().map((diario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: selectedNewDiarios.includes(diario.id),\n                                                                    onChange: (e)=>{\n                                                                        console.log('Checkbox changed:', diario.nombre, e.target.checked);\n                                                                        if (e.target.checked) {\n                                                                            const newSelection = [\n                                                                                ...selectedNewDiarios,\n                                                                                diario.id\n                                                                            ];\n                                                                            console.log('Adding diario:', diario.id, 'New selection:', newSelection);\n                                                                            setSelectedNewDiarios(newSelection);\n                                                                        } else {\n                                                                            const newSelection = selectedNewDiarios.filter((id)=>id !== diario.id);\n                                                                            console.log('Removing diario:', diario.id, 'New selection:', newSelection);\n                                                                            setSelectedNewDiarios(newSelection);\n                                                                        }\n                                                                    },\n                                                                    className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: diario.nombre\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        diario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: diario.descripcion\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, diario.id, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                selectedNewDiarios.length,\n                                                                \" de \",\n                                                                getDiariosDisponibles().length,\n                                                                \" diarios seleccionados\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowGenerateMore(false),\n                                                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                                                    children: \"Cancelar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: handleGenerateMore,\n                                                                    disabled: isGenerating || selectedNewDiarios.length === 0,\n                                                                    className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                                                    children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 577,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Generando...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 578,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 582,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Generar Versiones\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 583,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this),\n                                        getDiariosDisponibles().length === 0 && versiones.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-4 bg-green-50 rounded-lg border border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-8 w-8 text-green-600 mx-auto mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"\\xa1Todas las versiones han sido generadas!\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-600\",\n                                                    children: \"Se han creado versiones para todos los diarios disponibles.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this),\n                                versiones.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white shadow rounded-lg p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No hay versiones generadas\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Las versiones generadas por IA aparecer\\xe1n aqu\\xed una vez que se active el proceso de reescritura.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: versiones.map((version, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-green-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-6 py-4 bg-green-50 border-b border-green-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>toggleVersionExpansion(version.id),\n                                                                            className: \"flex items-center space-x-2 text-green-900 hover:text-green-700 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        getEstadoIcon(version.estado),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                            className: \"text-lg font-bold\",\n                                                                                            children: version.diario.nombre\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                            lineNumber: 629,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 627,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                expandedVersions.has(version.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-5 w-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 634,\n                                                                                    columnNumber: 29\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"h-5 w-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 636,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-3 py-1 text-xs font-medium rounded-full \".concat(getEstadoBadge(version.estado)),\n                                                                            children: version.estado\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                canReview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleVersionStateChange(version.id, 'APROBADA'),\n                                                                            className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 652,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Aprobar\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 653,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 648,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleVersionStateChange(version.id, 'RECHAZADA'),\n                                                                            className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 659,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Rechazar\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 660,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleVersionStateChange(version.id, 'EN_REVISION'),\n                                                                            className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 rounded-md transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 666,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"En Revisi\\xf3n\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 667,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 662,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 flex items-center space-x-4 text-sm text-green-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 676,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Generado por \",\n                                                                        version.usuario.name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        new Date(version.createdAt).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                version.metadatos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        version.metadatos.tokens_usados,\n                                                                        \" tokens\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 19\n                                                }, this),\n                                                expandedVersions.has(version.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            version.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Volanta\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 698,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-600 font-medium uppercase tracking-wide\",\n                                                                        children: version.volanta\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 699,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"T\\xedtulo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 706,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-xl font-bold text-gray-900 leading-tight\",\n                                                                        children: version.titulo\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            version.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Subt\\xedtulo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 714,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-lg text-gray-700 font-medium\",\n                                                                        children: version.subtitulo\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 715,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 713,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            version.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Resumen\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 723,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-base text-gray-700 leading-relaxed\",\n                                                                        children: version.resumen\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 724,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Contenido\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none\",\n                                                                        children: version.contenido\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            version.metadatos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"pt-4 border-t border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                        children: \"Informaci\\xf3n T\\xe9cnica\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 740,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Modelo:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 743,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    version.metadatos.modelo\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 742,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Tokens:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 746,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    version.metadatos.tokens_usados\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 745,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Tiempo:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 749,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    version.metadatos.tiempo_generacion,\n                                                                                    \"ms\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 748,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 741,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            version.promptUsado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"pt-4 border-t border-gray-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                                    className: \"group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                                            className: \"cursor-pointer text-xs font-medium text-gray-500 uppercase tracking-wide hover:text-gray-700\",\n                                                                            children: \"Ver Prompt Utilizado\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 759,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-2 p-3 bg-gray-50 rounded text-sm text-gray-700 font-mono whitespace-pre-wrap\",\n                                                                            children: version.promptUsado\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 762,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, version.id, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, this);\n}\n_s(RevisionPage, \"RoxOeDmqFYne6sGVAGdOGde1QMU=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = RevisionPage;\nvar _c;\n$RefreshReg$(_c, \"RevisionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx\n"));

/***/ })

});