import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { rewriteNoticia } from '@/lib/openai';

// POST /api/noticias/[id]/generate-versions - Generar versiones con IA
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Obtener datos del request
    const body = await request.json();
    console.log('Body recibido:', body);
    const { diarioIds } = body; // Array de IDs de diarios seleccionados
    console.log('diarioIds extraídos:', diarioIds);

    if (!diarioIds || !Array.isArray(diarioIds) || diarioIds.length === 0) {
      return NextResponse.json(
        { error: 'Debe seleccionar al menos un diario' },
        { status: 400 }
      );
    }

    // Verificar que la noticia existe
    const noticia = await prisma.noticia.findUnique({
      where: { id },
      include: {
        categoria: true,
        user: true,
      },
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Verificar permisos (solo el autor o admin/editor pueden generar versiones)
    const userRole = session.user.role;
    const isAuthor = noticia.userId === parseInt(session.user.id);
    const canGenerate = isAuthor || userRole === 'ADMIN' || userRole === 'EDITOR';

    if (!canGenerate) {
      return NextResponse.json(
        { error: 'No tienes permisos para generar versiones de esta noticia' },
        { status: 403 }
      );
    }

    // Obtener los diarios seleccionados
    const diarios = await prisma.diario.findMany({
      where: {
        id: { in: diarioIds },
        isActive: true,
      },
    });

    if (diarios.length === 0) {
      return NextResponse.json(
        { error: 'No se encontraron diarios válidos' },
        { status: 400 }
      );
    }

    const generatedVersions = [];
    const errors = [];

    // Generar versión para cada diario
    for (const diario of diarios) {
      try {
        console.log(`Generando versión para ${diario.nombre}...`);

        const rewriteResponse = await rewriteNoticia({
          titulo: noticia.titulo,
          subtitulo: noticia.subtitulo || undefined,
          volanta: noticia.volanta || undefined,
          contenido: noticia.contenido,
          resumen: noticia.resumen || undefined,
          prompt: diario.prompt,
          diarioNombre: diario.nombre,
        });

        // Guardar la versión generada en la base de datos
        const versionGuardada = await prisma.versionNoticia.create({
          data: {
            titulo: rewriteResponse.titulo,
            subtitulo: rewriteResponse.subtitulo,
            volanta: rewriteResponse.volanta,
            contenido: rewriteResponse.contenido,
            resumen: rewriteResponse.resumen,
            promptUsado: diario.prompt,
            metadatos: JSON.stringify(rewriteResponse.metadatos),
            noticiaId: noticia.id,
            diarioId: diario.id,
            generadaPor: parseInt(session.user.id),
            estado: 'GENERADA',
          },
          include: {
            diario: true,
            usuario: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        generatedVersions.push(versionGuardada);
        console.log(`✅ Versión generada para ${diario.nombre}`);

      } catch (error) {
        console.error(`Error generando versión para ${diario.nombre}:`, error);
        errors.push({
          diario: diario.nombre,
          error: error instanceof Error ? error.message : 'Error desconocido',
        });
      }
    }

    // Cambiar el estado de la noticia a EN_REVISION si se generaron versiones
    if (generatedVersions.length > 0 && noticia.estado === 'BORRADOR') {
      await prisma.noticia.update({
        where: { id },
        data: { estado: 'EN_REVISION' },
      });
    }

    return NextResponse.json({
      success: true,
      message: `Se generaron ${generatedVersions.length} versiones exitosamente`,
      versiones: generatedVersions,
      errores: errors,
    });

  } catch (error) {
    console.error('Error en generate-versions:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}