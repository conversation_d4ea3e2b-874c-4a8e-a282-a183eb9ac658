{"c": ["app/layout", "app/noticias/[id]/revision/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js", "(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CDEL%20SUR%20FINAL%5C%5Cpanel%20unificado%20version%202%5C%5Csrc%5C%5Capp%5C%5Cnoticias%5C%5C%5Bid%5D%5C%5Crevision%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx"]}