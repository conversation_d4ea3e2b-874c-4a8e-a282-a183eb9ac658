'use client';

import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ArrowLeft, Bot, CheckCircle, XCircle, Clock, ChevronDown, ChevronUp, FileText, User, Calendar, Zap, Plus, Send, ExternalLink, Edit3, RotateCcw, Save, X } from 'lucide-react';

interface Noticia {
  id: number;
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  imagenUrl?: string;
  imagenAlt?: string;
  autor?: string;
  fuente?: string;
  urlFuente?: string;
  estado: string;
  destacada: boolean;
  publicada: boolean;
  fechaPublicacion?: string;
  createdAt: string;
  updatedAt: string;
  categoria?: {
    id: number;
    nombre: string;
    color: string;
  };
  user: {
    id: number;
    name: string;
    email: string;
  };
}

interface Version {
  id: number;
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  estado: string;
  promptUsado?: string;
  metadatos?: any;
  createdAt: string;
  updatedAt: string;
  diario: {
    id: number;
    nombre: string;
    descripcion?: string;
  };
  usuario: {
    id: number;
    name: string;
    email: string;
  };
}

interface Diario {
  id: number;
  nombre: string;
  descripcion?: string;
}

export default function RevisionPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const id = Array.isArray(params.id) ? params.id[0] : params.id;

  const [noticia, setNoticia] = useState<Noticia | null>(null);
  const [versiones, setVersiones] = useState<Version[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedVersions, setExpandedVersions] = useState<Set<number>>(new Set());

  // Estados para generación incremental
  const [diarios, setDiarios] = useState<Diario[]>([]);
  const [showGenerateMore, setShowGenerateMore] = useState(false);
  const [selectedNewDiarios, setSelectedNewDiarios] = useState<number[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para edición de versiones
  const [editingVersion, setEditingVersion] = useState<number | null>(null);
  const [editForm, setEditForm] = useState({
    titulo: '',
    volanta: '',
    resumen: '',
    contenido: ''
  });
  const [isRegenerating, setIsRegenerating] = useState<number | null>(null);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session && id) {
      loadData();
    }
  }, [session, id]);

  // Debug: monitorear cambios en selectedNewDiarios
  useEffect(() => {
    console.log('selectedNewDiarios changed:', selectedNewDiarios);
  }, [selectedNewDiarios]);

  // Función para iniciar edición de versión
  const startEditVersion = (version: Version) => {
    setEditingVersion(version.id);
    setEditForm({
      titulo: version.titulo,
      volanta: version.volanta || '',
      resumen: version.resumen || '',
      contenido: version.contenido
    });
  };

  // Función para cancelar edición
  const cancelEdit = () => {
    setEditingVersion(null);
    setEditForm({
      titulo: '',
      volanta: '',
      resumen: '',
      contenido: ''
    });
  };

  // Función para guardar cambios de edición
  const saveEditVersion = async (versionId: number) => {
    try {
      const response = await fetch(`/api/noticias/${id}/versions/${versionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (!response.ok) {
        throw new Error('Error al actualizar la versión');
      }

      const updatedVersion = await response.json();

      // Actualizar la versión en el estado
      setVersiones(prev => prev.map(v =>
        v.id === versionId ? updatedVersion : v
      ));

      // Limpiar estado de edición
      cancelEdit();

    } catch (error) {
      console.error('Error saving version:', error);
      alert('Error al guardar los cambios');
    }
  };

  // Función para regenerar versión
  const regenerateVersion = async (versionId: number) => {
    try {
      setIsRegenerating(versionId);

      const response = await fetch(`/api/noticias/${id}/versions/${versionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Error al regenerar la versión');
      }

      const regeneratedVersion = await response.json();

      // Actualizar la versión en el estado
      setVersiones(prev => prev.map(v =>
        v.id === versionId ? regeneratedVersion : v
      ));

    } catch (error) {
      console.error('Error regenerating version:', error);
      alert('Error al regenerar la versión');
    } finally {
      setIsRegenerating(null);
    }
  };

  // TODO: Add edit functions back

  const loadData = async () => {
    try {
      // Cargar noticia
      const noticiaResponse = await fetch(`/api/noticias/${id}`);
      if (noticiaResponse.ok) {
        const noticiaData = await noticiaResponse.json();
        setNoticia(noticiaData);
      }

      // Cargar versiones
      const versionesResponse = await fetch(`/api/noticias/${id}/versions`);
      if (versionesResponse.ok) {
        const versionesData = await versionesResponse.json();
        setVersiones(versionesData.versiones);
        // Expandir todas las versiones por defecto
        setExpandedVersions(new Set(versionesData.versiones.map((v: Version) => v.id)));
      }

      // Cargar diarios disponibles
      const diariosResponse = await fetch('/api/diarios');
      if (diariosResponse.ok) {
        const diariosData = await diariosResponse.json();
        setDiarios(diariosData.diarios);
      }
    } catch (error) {
      console.error('Error al cargar datos:', error);
      alert('Error al cargar los datos');
    } finally {
      setLoading(false);
    }
  };

  const handleVersionStateChange = async (versionId: number, estado: string) => {
    try {
      const response = await fetch(`/api/noticias/${id}/versions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          versionId,
          estado,
        }),
      });

      if (response.ok) {
        await loadData(); // Recargar datos
        alert(`✅ Estado actualizado a: ${estado}`);
      } else {
        const data = await response.json();
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error al actualizar estado:', error);
      alert('Error al actualizar el estado de la versión');
    }
  };

  const toggleVersionExpansion = (versionId: number) => {
    const newExpanded = new Set(expandedVersions);
    if (newExpanded.has(versionId)) {
      newExpanded.delete(versionId);
    } else {
      newExpanded.add(versionId);
    }
    setExpandedVersions(newExpanded);
  };

  const getEstadoBadge = (estado: string) => {
    const badges = {
      'GENERADA': 'bg-blue-100 text-blue-800',
      'APROBADA': 'bg-green-100 text-green-800',
      'RECHAZADA': 'bg-red-100 text-red-800',
      'EN_REVISION': 'bg-yellow-100 text-yellow-800',
    };
    return badges[estado as keyof typeof badges] || 'bg-gray-100 text-gray-800';
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'APROBADA':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'RECHAZADA':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'EN_REVISION':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Bot className="h-4 w-4 text-blue-600" />;
    }
  };

  // Obtener diarios que ya tienen versiones generadas
  const getDiariosConVersiones = () => {
    return versiones.map(v => v.diario.id);
  };

  // Obtener diarios disponibles para generar (que no tienen versiones)
  const getDiariosDisponibles = () => {
    const diariosConVersiones = getDiariosConVersiones();
    return diarios.filter(d => !diariosConVersiones.includes(d.id));
  };

  // Manejar generación incremental
  const handleGenerateMore = async () => {
    console.log('selectedNewDiarios:', selectedNewDiarios);

    if (selectedNewDiarios.length === 0) {
      alert('Selecciona al menos un diario para generar versiones');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch(`/api/noticias/${id}/generate-versions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          diarioIds: selectedNewDiarios,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        alert(`✅ ${data.generadas} versiones generadas exitosamente`);

        // Recargar datos para mostrar las nuevas versiones
        await loadData();

        // Limpiar selección y cerrar modal
        setSelectedNewDiarios([]);
        setShowGenerateMore(false);
      } else {
        const data = await response.json();
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error al generar versiones:', error);
      alert('Error al generar las versiones');
    } finally {
      setIsGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando revisión...</p>
        </div>
      </div>
    );
  }

  if (!noticia) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Noticia no encontrada</p>
        </div>
      </div>
    );
  }

  // Verificar permisos
  const canReview = session?.user?.role === 'ADMIN' || session?.user?.role === 'EDITOR';

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Volver
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Revisión de Noticia
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  Revisa y gestiona las versiones generadas por IA
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                noticia.estado === 'BORRADOR' ? 'bg-gray-100 text-gray-800' :
                noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800' :
                noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800' :
                'bg-red-100 text-red-800'
              }`}>
                {noticia.estado}
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Layout Responsive: Vertical en móvil, Horizontal en desktop */}
        <div className="flex flex-col lg:grid lg:grid-cols-12 gap-6 lg:gap-8">

          {/* Noticia Original - Izquierda en desktop, arriba en móvil */}
          <div className="lg:col-span-7 xl:col-span-8 order-1">
            <div className="bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-blue-500">
              <div className="px-6 py-4 bg-blue-50 border-b border-blue-100">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-blue-900 flex items-center">
                    <FileText className="h-6 w-6 mr-2" />
                    Noticia Original
                  </h2>
                  <div className="flex items-center space-x-4 text-sm text-blue-700">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      {noticia.user.name}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {new Date(noticia.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {/* Metadatos */}
                <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                  {noticia.categoria && (
                    <div>
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Categoría</span>
                      <p className="text-sm font-medium" style={{ color: noticia.categoria.color }}>
                        {noticia.categoria.nombre}
                      </p>
                    </div>
                  )}
                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Estado</span>
                    <p className="text-sm font-medium text-gray-900">{noticia.estado.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Destacada</span>
                    <p className="text-sm font-medium text-gray-900">{noticia.destacada ? 'Sí' : 'No'}</p>
                  </div>
                </div>

                {/* Contenido de la noticia */}
                <div className="space-y-4">
                  {noticia.volanta && (
                    <div>
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Volanta</span>
                      <p className="text-sm text-blue-600 font-medium uppercase tracking-wide">
                        {noticia.volanta}
                      </p>
                    </div>
                  )}

                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Título</span>
                    <h3 className="text-2xl font-bold text-gray-900 leading-tight">
                      {noticia.titulo}
                    </h3>
                  </div>

                  {noticia.subtitulo && (
                    <div>
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Subtítulo</span>
                      <p className="text-lg text-gray-700 font-medium">
                        {noticia.subtitulo}
                      </p>
                    </div>
                  )}

                  {noticia.resumen && (
                    <div>
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Resumen</span>
                      <p className="text-base text-gray-700 leading-relaxed">
                        {noticia.resumen}
                      </p>
                    </div>
                  )}

                  {noticia.imagenUrl && (
                    <div>
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Imagen</span>
                      <img
                        src={noticia.imagenUrl}
                        alt={noticia.imagenAlt || noticia.titulo}
                        className="w-full max-w-2xl h-64 object-cover rounded-lg mt-2"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  )}

                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Contenido</span>
                    <div className="text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none">
                      {noticia.contenido}
                    </div>
                  </div>

                  {/* Información adicional */}
                  <div className="pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                      {noticia.autor && (
                        <div>
                          <span className="font-medium">Autor:</span> {noticia.autor}
                        </div>
                      )}
                      {noticia.fuente && (
                        <div>
                          <span className="font-medium">Fuente:</span> {noticia.fuente}
                        </div>
                      )}
                      {noticia.urlFuente && (
                        <div className="md:col-span-2">
                          <span className="font-medium">URL Fuente:</span>{' '}
                          <a
                            href={noticia.urlFuente}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 underline"
                          >
                            {noticia.urlFuente}
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Versiones Generadas por IA - Derecha en desktop, abajo en móvil */}
          <div className="lg:col-span-5 xl:col-span-4 order-2">
            <div className="mb-6">
              <h2 className="text-lg font-bold text-gray-900 flex items-center">
                <Bot className="h-5 w-5 mr-2 text-green-600" />
                Versiones IA ({versiones.length})
              </h2>
              <p className="text-xs text-gray-600 mt-1">
                Versiones reescritas automáticamente.
                {canReview && ' Puedes aprobar o rechazar cada una.'}
              </p>
            </div>

            {versiones.length === 0 && (
              <div className="bg-white shadow rounded-lg p-6 text-center">
                <Bot className="h-8 w-8 text-gray-400 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-2">No hay versiones</h3>
                <p className="text-xs text-gray-600">
                  Las versiones aparecerán aquí una vez generadas.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Versiones Individuales - Debajo del grid principal */}
        {versiones.length > 0 && (
          <div className="mt-8">
            <div className="mb-6">
              <h2 className="text-xl font-bold text-gray-900 flex items-center">
                <Bot className="h-6 w-6 mr-2 text-green-600" />
                Versiones Generadas por IA ({versiones.length})
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Versiones reescritas automáticamente para diferentes diarios.
                {canReview && ' Puedes aprobar o rechazar cada versión individualmente.'}
              </p>
            </div>

            <div className="space-y-6">
              {versiones.map((version, index) => (
                <div key={version.id} className="bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-green-500">
                  {/* Header de la versión */}
                  <div className="px-6 py-4 bg-green-50 border-b border-green-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => toggleVersionExpansion(version.id)}
                          className="flex items-center space-x-2 text-green-900 hover:text-green-700 transition-colors"
                        >
                          <div className="flex items-center space-x-2">
                            {getEstadoIcon(version.estado)}
                            <h3 className="text-lg font-bold">
                              {version.diario.nombre}
                            </h3>
                          </div>
                          {expandedVersions.has(version.id) ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </button>

                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${getEstadoBadge(version.estado)}`}>
                          {version.estado}
                        </span>
                      </div>

                      {/* Botones de acción */}
                      {canReview && (
                        <div className="flex items-center space-x-2">
                          {/* Botones de edición */}
                          <button
                            onClick={() => startEditVersion(version)}
                            className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors"
                            title="Editar versión"
                          >
                            <Edit3 className="h-4 w-4" />
                            <span>Editar</span>
                          </button>
                          <button
                            onClick={() => regenerateVersion(version.id)}
                            disabled={isRegenerating === version.id}
                            className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-purple-700 bg-purple-100 hover:bg-purple-200 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
                            title="Regenerar con IA"
                          >
                            {isRegenerating === version.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-700"></div>
                            ) : (
                              <RotateCcw className="h-4 w-4" />
                            )}
                            <span>{isRegenerating === version.id ? 'Regenerando...' : 'Regenerar'}</span>
                          </button>

                          {/* Botones de estado */}
                          <button
                            onClick={() => handleVersionStateChange(version.id, 'APROBADA')}
                            className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors"
                          >
                            <CheckCircle className="h-4 w-4" />
                            <span>Aprobar</span>
                          </button>
                          <button
                            onClick={() => handleVersionStateChange(version.id, 'RECHAZADA')}
                            className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors"
                          >
                            <XCircle className="h-4 w-4" />
                            <span>Rechazar</span>
                          </button>
                          <button
                            onClick={() => handleVersionStateChange(version.id, 'EN_REVISION')}
                            className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 rounded-md transition-colors"
                          >
                            <Clock className="h-4 w-4" />
                            <span>En Revisión</span>
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Información de generación */}
                    <div className="mt-2 flex items-center space-x-4 text-sm text-green-700">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        Generado por {version.usuario.name}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(version.createdAt).toLocaleString()}
                      </div>
                      {version.metadatos && (
                        <div className="flex items-center">
                          <Zap className="h-4 w-4 mr-1" />
                          {version.metadatos.tokens_usados} tokens
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Contenido expandible */}
                  {expandedVersions.has(version.id) && (
                    <div className="p-6">
                      {editingVersion === version.id ? (
                        /* Formulario de edición */
                        <div className="space-y-4">
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="text-lg font-semibold text-gray-900">Editando versión</h4>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => saveEditVersion(version.id)}
                                className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors"
                              >
                                <Save className="h-4 w-4" />
                                <span>Guardar</span>
                              </button>
                              <button
                                onClick={cancelEdit}
                                className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                              >
                                <X className="h-4 w-4" />
                                <span>Cancelar</span>
                              </button>
                            </div>
                          </div>

                          {/* Campo Volanta */}
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                              Volanta
                            </label>
                            <input
                              type="text"
                              value={editForm.volanta}
                              onChange={(e) => setEditForm(prev => ({ ...prev, volanta: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Volanta (opcional)"
                            />
                          </div>

                          {/* Campo Título */}
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                              Título *
                            </label>
                            <input
                              type="text"
                              value={editForm.titulo}
                              onChange={(e) => setEditForm(prev => ({ ...prev, titulo: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg font-semibold"
                              placeholder="Título de la noticia"
                              required
                            />
                          </div>

                          {/* Campo Resumen */}
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                              Resumen
                            </label>
                            <textarea
                              value={editForm.resumen}
                              onChange={(e) => setEditForm(prev => ({ ...prev, resumen: e.target.value }))}
                              rows={3}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Resumen de la noticia (opcional)"
                            />
                          </div>

                          {/* Campo Contenido */}
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                              Contenido *
                            </label>
                            <textarea
                              value={editForm.contenido}
                              onChange={(e) => setEditForm(prev => ({ ...prev, contenido: e.target.value }))}
                              rows={12}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Contenido completo de la noticia"
                              required
                            />
                          </div>
                        </div>
                      ) : (
                        /* Vista normal */
                        <div className="space-y-4">
                          {version.volanta && (
                            <div>
                              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Volanta</span>
                              <p className="text-sm text-green-600 font-medium uppercase tracking-wide">
                                {version.volanta}
                              </p>
                            </div>
                          )}

                          <div>
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Título</span>
                            <h4 className="text-xl font-bold text-gray-900 leading-tight">
                              {version.titulo}
                            </h4>
                          </div>

                          {version.resumen && (
                            <div>
                              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Resumen</span>
                              <p className="text-base text-gray-700 leading-relaxed">
                                {version.resumen}
                              </p>
                            </div>
                          )}

                          <div>
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Contenido</span>
                            <div className="text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none">
                              {version.contenido}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Metadatos técnicos - Solo para ADMIN */}
                      {version.metadatos && session?.user?.role === 'ADMIN' && (
                        <div className="pt-4 border-t border-gray-200">
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Información Técnica</span>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div>
                              <span className="font-medium">Modelo:</span> {version.metadatos.modelo}
                            </div>
                            <div>
                              <span className="font-medium">Tokens:</span> {version.metadatos.tokens_usados}
                            </div>
                            <div>
                              <span className="font-medium">Tiempo:</span> {version.metadatos.tiempo_generacion}ms
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Prompt usado - Solo para ADMIN */}
                      {version.promptUsado && session?.user?.role === 'ADMIN' && (
                        <div className="pt-4 border-t border-gray-200">
                          <details className="group">
                            <summary className="cursor-pointer text-xs font-medium text-gray-500 uppercase tracking-wide hover:text-gray-700">
                              Ver Prompt Utilizado
                            </summary>
                            <div className="mt-2 p-3 bg-gray-50 rounded text-sm text-gray-700 font-mono whitespace-pre-wrap">
                              {version.promptUsado}
                            </div>
                          </details>
                        </div>
                      )}

                      {/* Enlace al diario */}
                      {version.diario.url && (
                        <div className="pt-4 border-t border-gray-200">
                          <a
                            href={version.diario.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 transition-colors"
                          >
                            <ExternalLink className="h-4 w-4" />
                            <span>Ver en {version.diario.nombre}</span>
                          </a>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
