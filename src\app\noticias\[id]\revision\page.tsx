'use client';

import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ArrowLeft, Bot, CheckCircle, XCircle, Clock, ChevronDown, ChevronUp, FileText, User, Calendar, Zap, Plus, Send, ExternalLink, Edit3, RotateCcw, Save, X } from 'lucide-react';

interface Noticia {
  id: number;
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  imagenUrl?: string;
  imagenAlt?: string;
  autor?: string;
  fuente?: string;
  urlFuente?: string;
  estado: string;
  destacada: boolean;
  publicada: boolean;
  fechaPublicacion?: string;
  createdAt: string;
  updatedAt: string;
  categoria?: {
    id: number;
    nombre: string;
    color: string;
  };
  user: {
    id: number;
    name: string;
    email: string;
  };
}

interface Version {
  id: number;
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  estado: string;
  promptUsado?: string;
  metadatos?: any;
  createdAt: string;
  updatedAt: string;
  diario: {
    id: number;
    nombre: string;
    descripcion?: string;
  };
  usuario: {
    id: number;
    name: string;
    email: string;
  };
}

interface Diario {
  id: number;
  nombre: string;
  descripcion?: string;
}

export default function RevisionPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const id = Array.isArray(params.id) ? params.id[0] : params.id;

  const [noticia, setNoticia] = useState<Noticia | null>(null);
  const [versiones, setVersiones] = useState<Version[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedVersions, setExpandedVersions] = useState<Set<number>>(new Set());

  // Estados para generación incremental
  const [diarios, setDiarios] = useState<Diario[]>([]);
  const [showGenerateMore, setShowGenerateMore] = useState(false);
  const [selectedNewDiarios, setSelectedNewDiarios] = useState<number[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para edición de versiones
  const [editingVersion, setEditingVersion] = useState<number | null>(null);
  const [editForm, setEditForm] = useState({
    titulo: '',
    volanta: '',
    resumen: '',
    contenido: ''
  });
  const [isRegenerating, setIsRegenerating] = useState<number | null>(null);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session && id) {
      loadData();
    }
  }, [session, id]);

  // Debug: monitorear cambios en selectedNewDiarios
  useEffect(() => {
    console.log('selectedNewDiarios changed:', selectedNewDiarios);
  }, [selectedNewDiarios]);

  // Función para iniciar edición de versión
  const startEditVersion = (version: Version) => {
    setEditingVersion(version.id);
    setEditForm({
      titulo: version.titulo,
      volanta: version.volanta || '',
      resumen: version.resumen || '',
      contenido: version.contenido
    });
  };

  // Función para cancelar edición
  const cancelEdit = () => {
    setEditingVersion(null);
    setEditForm({
      titulo: '',
      volanta: '',
      resumen: '',
      contenido: ''
    });
  };

  // Función para guardar cambios de edición
  const saveEditVersion = async (versionId: number) => {
    try {
      const response = await fetch(`/api/noticias/${id}/versions/${versionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (!response.ok) {
        throw new Error('Error al actualizar la versión');
      }

      const updatedVersion = await response.json();

      // Actualizar la versión en el estado
      setVersiones(prev => prev.map(v =>
        v.id === versionId ? updatedVersion : v
      ));

      // Limpiar estado de edición
      cancelEdit();

    } catch (error) {
      console.error('Error saving version:', error);
      alert('Error al guardar los cambios');
    }
  };

  // Función para regenerar versión
  const regenerateVersion = async (versionId: number) => {
    try {
      setIsRegenerating(versionId);

      const response = await fetch(`/api/noticias/${id}/versions/${versionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Error al regenerar la versión');
      }

      const regeneratedVersion = await response.json();

      // Actualizar la versión en el estado
      setVersiones(prev => prev.map(v =>
        v.id === versionId ? regeneratedVersion : v
      ));

    } catch (error) {
      console.error('Error regenerating version:', error);
      alert('Error al regenerar la versión');
    } finally {
      setIsRegenerating(null);
    }
  };

  // TODO: Add edit functions back

  const loadData = async () => {
    try {
      // Cargar noticia
      const noticiaResponse = await fetch(`/api/noticias/${id}`);
      if (noticiaResponse.ok) {
        const noticiaData = await noticiaResponse.json();
        setNoticia(noticiaData);
      }

      // Cargar versiones
      const versionesResponse = await fetch(`/api/noticias/${id}/versions`);
      if (versionesResponse.ok) {
        const versionesData = await versionesResponse.json();
        setVersiones(versionesData.versiones);
        // Expandir todas las versiones por defecto
        setExpandedVersions(new Set(versionesData.versiones.map((v: Version) => v.id)));
      }

      // Cargar diarios disponibles
      const diariosResponse = await fetch('/api/diarios');
      if (diariosResponse.ok) {
        const diariosData = await diariosResponse.json();
        setDiarios(diariosData.diarios);
      }
    } catch (error) {
      console.error('Error al cargar datos:', error);
      alert('Error al cargar los datos');
    } finally {
      setLoading(false);
    }
  };

  const handleVersionStateChange = async (versionId: number, estado: string) => {
    try {
      const response = await fetch(`/api/noticias/${id}/versions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          versionId,
          estado,
        }),
      });

      if (response.ok) {
        await loadData(); // Recargar datos
        alert(`✅ Estado actualizado a: ${estado}`);
      } else {
        const data = await response.json();
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error al actualizar estado:', error);
      alert('Error al actualizar el estado de la versión');
    }
  };

  const toggleVersionExpansion = (versionId: number) => {
    const newExpanded = new Set(expandedVersions);
    if (newExpanded.has(versionId)) {
      newExpanded.delete(versionId);
    } else {
      newExpanded.add(versionId);
    }
    setExpandedVersions(newExpanded);
  };

  const getEstadoBadge = (estado: string) => {
    const badges = {
      'GENERADA': 'bg-blue-100 text-blue-800',
      'APROBADA': 'bg-green-100 text-green-800',
      'RECHAZADA': 'bg-red-100 text-red-800',
      'EN_REVISION': 'bg-yellow-100 text-yellow-800',
    };
    return badges[estado as keyof typeof badges] || 'bg-gray-100 text-gray-800';
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'APROBADA':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'RECHAZADA':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'EN_REVISION':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Bot className="h-4 w-4 text-blue-600" />;
    }
  };

  // Obtener diarios que ya tienen versiones generadas
  const getDiariosConVersiones = () => {
    return versiones.map(v => v.diario.id);
  };

  // Obtener diarios disponibles para generar (que no tienen versiones)
  const getDiariosDisponibles = () => {
    const diariosConVersiones = getDiariosConVersiones();
    return diarios.filter(d => !diariosConVersiones.includes(d.id));
  };

  // Manejar generación incremental
  const handleGenerateMore = async () => {
    console.log('selectedNewDiarios:', selectedNewDiarios);

    if (selectedNewDiarios.length === 0) {
      alert('Selecciona al menos un diario para generar versiones');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch(`/api/noticias/${id}/generate-versions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          diarioIds: selectedNewDiarios,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        alert(`✅ ${data.generadas} versiones generadas exitosamente`);

        // Recargar datos para mostrar las nuevas versiones
        await loadData();

        // Limpiar selección y cerrar modal
        setSelectedNewDiarios([]);
        setShowGenerateMore(false);
      } else {
        const data = await response.json();
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error al generar versiones:', error);
      alert('Error al generar las versiones');
    } finally {
      setIsGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando revisión...</p>
        </div>
      </div>
    );
  }

  if (!noticia) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Noticia no encontrada</p>
        </div>
      </div>
    );
  }

  // Verificar permisos
  const canReview = session?.user?.role === 'ADMIN' || session?.user?.role === 'EDITOR';

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Volver
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Revisión de Noticia
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  Revisa y gestiona las versiones generadas por IA
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                noticia.estado === 'BORRADOR' ? 'bg-gray-100 text-gray-800' :
                noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800' :
                noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800' :
                'bg-red-100 text-red-800'
              }`}>
                {noticia.estado}
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h2 className="text-xl font-bold text-gray-900">Página en construcción</h2>
          <p className="text-gray-600 mt-2">Las versiones aparecerán aquí pronto.</p>
        </div>
      </main>
    </div>
  );
}
