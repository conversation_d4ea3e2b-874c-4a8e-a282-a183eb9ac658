'use client';

import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ArrowLeft, Bot, CheckCircle, XCircle, Clock, ChevronDown, ChevronUp, FileText, User, Calendar, Zap, Plus, Send } from 'lucide-react';

interface Noticia {
  id: number;
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  imagenUrl?: string;
  imagenAlt?: string;
  autor?: string;
  fuente?: string;
  urlFuente?: string;
  estado: string;
  destacada: boolean;
  publicada: boolean;
  fechaPublicacion?: string;
  createdAt: string;
  updatedAt: string;
  categoria?: {
    id: number;
    nombre: string;
    color: string;
  };
  user: {
    id: number;
    name: string;
    email: string;
  };
}

interface Version {
  id: number;
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  estado: string;
  promptUsado?: string;
  metadatos?: any;
  createdAt: string;
  updatedAt: string;
  diario: {
    id: number;
    nombre: string;
    descripcion?: string;
  };
  usuario: {
    id: number;
    name: string;
    email: string;
  };
}

interface Diario {
  id: number;
  nombre: string;
  descripcion?: string;
}

export default function RevisionPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const id = Array.isArray(params.id) ? params.id[0] : params.id;

  const [noticia, setNoticia] = useState<Noticia | null>(null);
  const [versiones, setVersiones] = useState<Version[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedVersions, setExpandedVersions] = useState<Set<number>>(new Set());

  // Estados para generación incremental
  const [diarios, setDiarios] = useState<Diario[]>([]);
  const [showGenerateMore, setShowGenerateMore] = useState(false);
  const [selectedNewDiarios, setSelectedNewDiarios] = useState<number[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session && id) {
      loadData();
    }
  }, [session, id]);

  // Debug: monitorear cambios en selectedNewDiarios
  useEffect(() => {
    console.log('selectedNewDiarios changed:', selectedNewDiarios);
  }, [selectedNewDiarios]);

  const loadData = async () => {
    try {
      // Cargar noticia
      const noticiaResponse = await fetch(`/api/noticias/${id}`);
      if (noticiaResponse.ok) {
        const noticiaData = await noticiaResponse.json();
        setNoticia(noticiaData);
      }

      // Cargar versiones
      const versionesResponse = await fetch(`/api/noticias/${id}/versions`);
      if (versionesResponse.ok) {
        const versionesData = await versionesResponse.json();
        setVersiones(versionesData.versiones);
        // Expandir todas las versiones por defecto
        setExpandedVersions(new Set(versionesData.versiones.map((v: Version) => v.id)));
      }

      // Cargar diarios disponibles
      const diariosResponse = await fetch('/api/diarios');
      if (diariosResponse.ok) {
        const diariosData = await diariosResponse.json();
        setDiarios(diariosData.diarios);
      }
    } catch (error) {
      console.error('Error al cargar datos:', error);
      alert('Error al cargar los datos');
    } finally {
      setLoading(false);
    }
  };

  const handleVersionStateChange = async (versionId: number, estado: string) => {
    try {
      const response = await fetch(`/api/noticias/${id}/versions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          versionId,
          estado,
        }),
      });

      if (response.ok) {
        await loadData(); // Recargar datos
        alert(`✅ Estado actualizado a: ${estado}`);
      } else {
        const data = await response.json();
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error al actualizar estado:', error);
      alert('Error al actualizar el estado de la versión');
    }
  };

  const toggleVersionExpansion = (versionId: number) => {
    const newExpanded = new Set(expandedVersions);
    if (newExpanded.has(versionId)) {
      newExpanded.delete(versionId);
    } else {
      newExpanded.add(versionId);
    }
    setExpandedVersions(newExpanded);
  };

  const getEstadoBadge = (estado: string) => {
    const badges = {
      'GENERADA': 'bg-blue-100 text-blue-800',
      'APROBADA': 'bg-green-100 text-green-800',
      'RECHAZADA': 'bg-red-100 text-red-800',
      'EN_REVISION': 'bg-yellow-100 text-yellow-800',
    };
    return badges[estado as keyof typeof badges] || 'bg-gray-100 text-gray-800';
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'APROBADA':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'RECHAZADA':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'EN_REVISION':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Bot className="h-4 w-4 text-blue-600" />;
    }
  };

  // Obtener diarios que ya tienen versiones generadas
  const getDiariosConVersiones = () => {
    return versiones.map(v => v.diario.id);
  };

  // Obtener diarios disponibles para generar (que no tienen versiones)
  const getDiariosDisponibles = () => {
    const diariosConVersiones = getDiariosConVersiones();
    return diarios.filter(d => !diariosConVersiones.includes(d.id));
  };

  // Manejar generación incremental
  const handleGenerateMore = async () => {
    console.log('selectedNewDiarios:', selectedNewDiarios);

    if (selectedNewDiarios.length === 0) {
      alert('Selecciona al menos un diario para generar versiones');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch(`/api/noticias/${id}/generate-versions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          diarioIds: selectedNewDiarios,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        alert(`✅ ${data.generadas} versiones generadas exitosamente`);

        // Recargar datos para mostrar las nuevas versiones
        await loadData();

        // Limpiar selección y cerrar modal
        setSelectedNewDiarios([]);
        setShowGenerateMore(false);
      } else {
        const data = await response.json();
        alert(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error al generar versiones:', error);
      alert('Error al generar las versiones');
    } finally {
      setIsGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando revisión...</p>
        </div>
      </div>
    );
  }

  if (!noticia) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Noticia no encontrada</p>
        </div>
      </div>
    );
  }

  // Verificar permisos
  const canReview = session?.user?.role === 'ADMIN' || session?.user?.role === 'EDITOR';

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Volver
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Revisión de Noticia</h1>
                <p className="text-sm text-gray-600">
                  Estado: <span className="font-medium">{noticia.estado.replace('_', ' ')}</span>
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800' :
                noticia.estado === 'APROBADA' ? 'bg-green-100 text-green-800' :
                noticia.estado === 'PUBLICADA' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {noticia.estado.replace('_', ' ')}
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Noticia Original - Arriba */}
        <div className="mb-8">
          <div className="bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-blue-500">
            <div className="px-6 py-4 bg-blue-50 border-b border-blue-100">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-blue-900 flex items-center">
                  <FileText className="h-6 w-6 mr-2" />
                  Noticia Original
                </h2>
                <div className="flex items-center space-x-4 text-sm text-blue-700">
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    {noticia.user.name}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {new Date(noticia.createdAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6">
              {/* Metadatos */}
              <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                {noticia.categoria && (
                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Categoría</span>
                    <p className="text-sm font-medium" style={{ color: noticia.categoria.color }}>
                      {noticia.categoria.nombre}
                    </p>
                  </div>
                )}
                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Estado</span>
                  <p className="text-sm font-medium text-gray-900">{noticia.estado.replace('_', ' ')}</p>
                </div>
                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Destacada</span>
                  <p className="text-sm font-medium text-gray-900">{noticia.destacada ? 'Sí' : 'No'}</p>
                </div>
              </div>

              {/* Contenido de la noticia */}
              <div className="space-y-4">
                {noticia.volanta && (
                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Volanta</span>
                    <p className="text-sm text-blue-600 font-medium uppercase tracking-wide">
                      {noticia.volanta}
                    </p>
                  </div>
                )}

                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Título</span>
                  <h3 className="text-2xl font-bold text-gray-900 leading-tight">
                    {noticia.titulo}
                  </h3>
                </div>

                {noticia.subtitulo && (
                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Subtítulo</span>
                    <p className="text-lg text-gray-700 font-medium">
                      {noticia.subtitulo}
                    </p>
                  </div>
                )}

                {noticia.resumen && (
                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Resumen</span>
                    <p className="text-base text-gray-700 leading-relaxed">
                      {noticia.resumen}
                    </p>
                  </div>
                )}

                {noticia.imagenUrl && (
                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Imagen</span>
                    <img
                      src={noticia.imagenUrl}
                      alt={noticia.imagenAlt || noticia.titulo}
                      className="w-full max-w-2xl h-64 object-cover rounded-lg mt-2"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                )}

                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Contenido</span>
                  <div className="text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none">
                    {noticia.contenido}
                  </div>
                </div>

                {/* Información adicional */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                    {noticia.autor && (
                      <div>
                        <span className="font-medium">Autor:</span> {noticia.autor}
                      </div>
                    )}
                    {noticia.fuente && (
                      <div>
                        <span className="font-medium">Fuente:</span> {noticia.fuente}
                      </div>
                    )}
                    {noticia.urlFuente && (
                      <div className="md:col-span-2">
                        <span className="font-medium">URL Fuente:</span>{' '}
                        <a
                          href={noticia.urlFuente}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          {noticia.urlFuente}
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Versiones Generadas por IA - Abajo */}
        <div>
          <div className="mb-6">
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <Bot className="h-6 w-6 mr-2 text-green-600" />
              Versiones Generadas por IA ({versiones.length})
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Versiones reescritas automáticamente para diferentes diarios.
              {canReview && ' Puedes aprobar o rechazar cada versión individualmente.'}
            </p>
          </div>

          {/* Estado de Diarios y Generación Incremental */}
          {diarios.length > 0 && (
            <div className="mb-6 bg-white shadow rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Estado de Generación por Diario
                </h3>
                {getDiariosDisponibles().length > 0 && (
                  <button
                    onClick={() => setShowGenerateMore(!showGenerateMore)}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors"
                  >
                    <Plus className="h-4 w-4" />
                    <span>Generar Más Versiones</span>
                  </button>
                )}
              </div>

              {/* Grid de estado de diarios */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                {diarios.map((diario) => {
                  const tieneVersion = getDiariosConVersiones().includes(diario.id);
                  const version = versiones.find(v => v.diario.id === diario.id);

                  return (
                    <div
                      key={diario.id}
                      className={`p-4 rounded-lg border-2 ${
                        tieneVersion
                          ? 'border-green-200 bg-green-50'
                          : 'border-gray-200 bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900">{diario.nombre}</h4>
                        {tieneVersion ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <Clock className="h-5 w-5 text-gray-400" />
                        )}
                      </div>

                      {tieneVersion && version ? (
                        <div className="space-y-1">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getEstadoBadge(version.estado)}`}>
                            {version.estado}
                          </span>
                          <p className="text-xs text-gray-600">
                            Generado: {new Date(version.createdAt).toLocaleDateString('es-ES', {
                              day: '2-digit',
                              month: 'short',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                      ) : (
                        <p className="text-xs text-gray-500">Sin versión generada</p>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Formulario de generación incremental */}
              {showGenerateMore && getDiariosDisponibles().length > 0 && (
                <div className="border-t border-gray-200 pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">
                    Seleccionar Diarios para Generar Versiones
                  </h4>

                  <div className="space-y-3 mb-4">
                    {getDiariosDisponibles().map((diario) => (
                      <label key={diario.id} className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedNewDiarios.includes(diario.id)}
                          onChange={(e) => {
                            console.log('Checkbox changed:', diario.nombre, e.target.checked);
                            if (e.target.checked) {
                              const newSelection = [...selectedNewDiarios, diario.id];
                              console.log('Adding diario:', diario.id, 'New selection:', newSelection);
                              setSelectedNewDiarios(newSelection);
                            } else {
                              const newSelection = selectedNewDiarios.filter(id => id !== diario.id);
                              console.log('Removing diario:', diario.id, 'New selection:', newSelection);
                              setSelectedNewDiarios(newSelection);
                            }
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <div>
                          <span className="text-sm font-medium text-gray-900">{diario.nombre}</span>
                          {diario.descripcion && (
                            <p className="text-xs text-gray-500">{diario.descripcion}</p>
                          )}
                        </div>
                      </label>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      {selectedNewDiarios.length} de {getDiariosDisponibles().length} diarios seleccionados
                    </div>
                    <div className="space-x-2">
                      <button
                        onClick={() => setShowGenerateMore(false)}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                      >
                        Cancelar
                      </button>
                      <button
                        onClick={handleGenerateMore}
                        disabled={isGenerating || selectedNewDiarios.length === 0}
                        className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
                      >
                        {isGenerating ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Generando...</span>
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4" />
                            <span>Generar Versiones</span>
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Mensaje cuando todos los diarios tienen versiones */}
              {getDiariosDisponibles().length === 0 && versiones.length > 0 && (
                <div className="text-center py-4 bg-green-50 rounded-lg border border-green-200">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <p className="text-sm font-medium text-green-800">
                    ¡Todas las versiones han sido generadas!
                  </p>
                  <p className="text-xs text-green-600">
                    Se han creado versiones para todos los diarios disponibles.
                  </p>
                </div>
              )}
            </div>
          )}

          {versiones.length === 0 ? (
            <div className="bg-white shadow rounded-lg p-8 text-center">
              <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No hay versiones generadas</h3>
              <p className="text-gray-600">
                Las versiones generadas por IA aparecerán aquí una vez que se active el proceso de reescritura.
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {versiones.map((version, index) => (
                <div key={version.id} className="bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-green-500">
                  {/* Header de la versión */}
                  <div className="px-6 py-4 bg-green-50 border-b border-green-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => toggleVersionExpansion(version.id)}
                          className="flex items-center space-x-2 text-green-900 hover:text-green-700 transition-colors"
                        >
                          <div className="flex items-center space-x-2">
                            {getEstadoIcon(version.estado)}
                            <h3 className="text-lg font-bold">
                              {version.diario.nombre}
                            </h3>
                          </div>
                          {expandedVersions.has(version.id) ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </button>

                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${getEstadoBadge(version.estado)}`}>
                          {version.estado}
                        </span>
                      </div>

                      {/* Botones de acción */}
                      {canReview && (
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleVersionStateChange(version.id, 'APROBADA')}
                            className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors"
                          >
                            <CheckCircle className="h-4 w-4" />
                            <span>Aprobar</span>
                          </button>
                          <button
                            onClick={() => handleVersionStateChange(version.id, 'RECHAZADA')}
                            className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors"
                          >
                            <XCircle className="h-4 w-4" />
                            <span>Rechazar</span>
                          </button>
                          <button
                            onClick={() => handleVersionStateChange(version.id, 'EN_REVISION')}
                            className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 rounded-md transition-colors"
                          >
                            <Clock className="h-4 w-4" />
                            <span>En Revisión</span>
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Información de generación */}
                    <div className="mt-2 flex items-center space-x-4 text-sm text-green-700">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        Generado por {version.usuario.name}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(version.createdAt).toLocaleString()}
                      </div>
                      {version.metadatos && (
                        <div className="flex items-center">
                          <Zap className="h-4 w-4 mr-1" />
                          {version.metadatos.tokens_usados} tokens
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Contenido expandible */}
                  {expandedVersions.has(version.id) && (
                    <div className="p-6">
                      <div className="space-y-4">
                        {version.volanta && (
                          <div>
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Volanta</span>
                            <p className="text-sm text-green-600 font-medium uppercase tracking-wide">
                              {version.volanta}
                            </p>
                          </div>
                        )}

                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Título</span>
                          <h4 className="text-xl font-bold text-gray-900 leading-tight">
                            {version.titulo}
                          </h4>
                        </div>

                        {version.subtitulo && (
                          <div>
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Subtítulo</span>
                            <p className="text-lg text-gray-700 font-medium">
                              {version.subtitulo}
                            </p>
                          </div>
                        )}

                        {version.resumen && (
                          <div>
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Resumen</span>
                            <p className="text-base text-gray-700 leading-relaxed">
                              {version.resumen}
                            </p>
                          </div>
                        )}

                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Contenido</span>
                          <div className="text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none">
                            {version.contenido}
                          </div>
                        </div>

                        {/* Metadatos técnicos */}
                        {version.metadatos && (
                          <div className="pt-4 border-t border-gray-200">
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Información Técnica</span>
                            <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                              <div>
                                <span className="font-medium">Modelo:</span> {version.metadatos.modelo}
                              </div>
                              <div>
                                <span className="font-medium">Tokens:</span> {version.metadatos.tokens_usados}
                              </div>
                              <div>
                                <span className="font-medium">Tiempo:</span> {version.metadatos.tiempo_generacion}ms
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Prompt usado */}
                        {version.promptUsado && (
                          <div className="pt-4 border-t border-gray-200">
                            <details className="group">
                              <summary className="cursor-pointer text-xs font-medium text-gray-500 uppercase tracking-wide hover:text-gray-700">
                                Ver Prompt Utilizado
                              </summary>
                              <div className="mt-2 p-3 bg-gray-50 rounded text-sm text-gray-700 font-mono whitespace-pre-wrap">
                                {version.promptUsado}
                              </div>
                            </details>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}