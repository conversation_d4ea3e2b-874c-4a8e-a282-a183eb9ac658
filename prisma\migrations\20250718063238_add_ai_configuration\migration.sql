-- CreateTable
CREATE TABLE "boards" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "createdById" INTEGER NOT NULL,
    CONSTRAINT "boards_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "board_members" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "role" TEXT NOT NULL DEFAULT 'MEMBER',
    "joinedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "userId" INTEGER NOT NULL,
    "boardId" INTEGER NOT NULL,
    CONSTRAINT "board_members_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "board_members_boardId_fkey" FOREIGN KEY ("boardId") REFERENCES "boards" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "diarios" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "nombre" TEXT NOT NULL,
    "descripcion" TEXT,
    "prompt" TEXT NOT NULL DEFAULT 'Reescribe la siguiente noticia manteniendo la información principal pero adaptando el estilo y tono:',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "aiProvider" TEXT NOT NULL DEFAULT 'OPENAI',
    "aiModel" TEXT,
    "useGlobalConfig" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "versiones_noticias" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "titulo" TEXT NOT NULL,
    "subtitulo" TEXT,
    "volanta" TEXT,
    "contenido" TEXT NOT NULL,
    "resumen" TEXT,
    "estado" TEXT NOT NULL DEFAULT 'GENERADA',
    "promptUsado" TEXT,
    "metadatos" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "noticiaId" INTEGER NOT NULL,
    "diarioId" INTEGER NOT NULL,
    "generadaPor" INTEGER NOT NULL,
    CONSTRAINT "versiones_noticias_noticiaId_fkey" FOREIGN KEY ("noticiaId") REFERENCES "noticias" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "versiones_noticias_diarioId_fkey" FOREIGN KEY ("diarioId") REFERENCES "diarios" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "versiones_noticias_generadaPor_fkey" FOREIGN KEY ("generadaPor") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ai_config" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "openaiApiKey" TEXT,
    "openaiModel" TEXT NOT NULL DEFAULT 'gpt-3.5-turbo',
    "openaiMaxTokens" INTEGER NOT NULL DEFAULT 2000,
    "openaiTemperature" REAL NOT NULL DEFAULT 0.7,
    "geminiApiKey" TEXT,
    "geminiModel" TEXT NOT NULL DEFAULT 'gemini-pro',
    "geminiMaxTokens" INTEGER NOT NULL DEFAULT 2000,
    "geminiTemperature" REAL NOT NULL DEFAULT 0.7,
    "defaultProvider" TEXT NOT NULL DEFAULT 'OPENAI',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "board_members_userId_boardId_key" ON "board_members"("userId", "boardId");

-- CreateIndex
CREATE UNIQUE INDEX "diarios_nombre_key" ON "diarios"("nombre");
