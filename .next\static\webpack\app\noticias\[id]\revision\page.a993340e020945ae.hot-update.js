"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/noticias/[id]/revision/page",{

/***/ "(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/noticias/[id]/revision/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RevisionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,FileText,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RevisionPage() {\n    var _session_user, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = Array.isArray(params.id) ? params.id[0] : params.id;\n    const [noticia, setNoticia] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [versiones, setVersiones] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [expandedVersions, setExpandedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Set());\n    // Estados para generación incremental\n    const [diarios, setDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [showGenerateMore, setShowGenerateMore] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedNewDiarios, setSelectedNewDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        status,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (session && id) {\n                loadData();\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        session,\n        id\n    ]);\n    const loadData = async ()=>{\n        try {\n            // Cargar noticia\n            const noticiaResponse = await fetch(\"/api/noticias/\".concat(id));\n            if (noticiaResponse.ok) {\n                const noticiaData = await noticiaResponse.json();\n                setNoticia(noticiaData);\n            }\n            // Cargar versiones\n            const versionesResponse = await fetch(\"/api/noticias/\".concat(id, \"/versions\"));\n            if (versionesResponse.ok) {\n                const versionesData = await versionesResponse.json();\n                setVersiones(versionesData.versiones);\n                // Expandir todas las versiones por defecto\n                setExpandedVersions(new Set(versionesData.versiones.map((v)=>v.id)));\n            }\n            // Cargar diarios disponibles\n            const diariosResponse = await fetch('/api/diarios');\n            if (diariosResponse.ok) {\n                const diariosData = await diariosResponse.json();\n                setDiarios(diariosData.diarios);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos:', error);\n            alert('Error al cargar los datos');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVersionStateChange = async (versionId, estado)=>{\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    versionId,\n                    estado\n                })\n            });\n            if (response.ok) {\n                await loadData(); // Recargar datos\n                alert(\"✅ Estado actualizado a: \".concat(estado));\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al actualizar estado:', error);\n            alert('Error al actualizar el estado de la versión');\n        }\n    };\n    const toggleVersionExpansion = (versionId)=>{\n        const newExpanded = new Set(expandedVersions);\n        if (newExpanded.has(versionId)) {\n            newExpanded.delete(versionId);\n        } else {\n            newExpanded.add(versionId);\n        }\n        setExpandedVersions(newExpanded);\n    };\n    const getEstadoBadge = (estado)=>{\n        const badges = {\n            'GENERADA': 'bg-blue-100 text-blue-800',\n            'APROBADA': 'bg-green-100 text-green-800',\n            'RECHAZADA': 'bg-red-100 text-red-800',\n            'EN_REVISION': 'bg-yellow-100 text-yellow-800'\n        };\n        return badges[estado] || 'bg-gray-100 text-gray-800';\n    };\n    const getEstadoIcon = (estado)=>{\n        switch(estado){\n            case 'APROBADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 16\n                }, this);\n            case 'RECHAZADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 16\n                }, this);\n            case 'EN_REVISION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // Obtener diarios que ya tienen versiones generadas\n    const getDiariosConVersiones = ()=>{\n        return versiones.map((v)=>v.diario.id);\n    };\n    // Obtener diarios disponibles para generar (que no tienen versiones)\n    const getDiariosDisponibles = ()=>{\n        const diariosConVersiones = getDiariosConVersiones();\n        return diarios.filter((d)=>!diariosConVersiones.includes(d.id));\n    };\n    // Manejar generación incremental\n    const handleGenerateMore = async ()=>{\n        if (selectedNewDiarios.length === 0) {\n            alert('Selecciona al menos un diario para generar versiones');\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/generate-versions\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    diariosIds: selectedNewDiarios\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                alert(\"✅ \".concat(data.generadas, \" versiones generadas exitosamente\"));\n                // Recargar datos para mostrar las nuevas versiones\n                await loadData();\n                // Limpiar selección y cerrar modal\n                setSelectedNewDiarios([]);\n                setShowGenerateMore(false);\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al generar versiones:', error);\n            alert('Error al generar las versiones');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando revisi\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this);\n    }\n    if (!noticia) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Noticia no encontrada\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this);\n    }\n    // Verificar permisos\n    const canReview = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'ADMIN' || (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role) === 'EDITOR';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.back(),\n                                        className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Volver\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Revisi\\xf3n de Noticia\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Estado: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: noticia.estado.replace('_', ' ')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 text-sm font-medium rounded-full \".concat(noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800' : noticia.estado === 'APROBADA' ? 'bg-green-100 text-green-800' : noticia.estado === 'PUBLICADA' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'),\n                                    children: noticia.estado.replace('_', ' ')\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 bg-blue-50 border-b border-blue-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-blue-900 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-6 w-6 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Noticia Original\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            noticia.user.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            new Date(noticia.createdAt).toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                            children: \"Categor\\xeda\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            style: {\n                                                                color: noticia.categoria.color\n                                                            },\n                                                            children: noticia.categoria.nombre\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                            children: \"Estado\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: noticia.estado.replace('_', ' ')\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                            children: \"Destacada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: noticia.destacada ? 'Sí' : 'No'\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                noticia.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                            children: \"Volanta\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-600 font-medium uppercase tracking-wide\",\n                                                            children: noticia.volanta\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                            children: \"T\\xedtulo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-gray-900 leading-tight\",\n                                                            children: noticia.titulo\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this),\n                                                noticia.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                            children: \"Subt\\xedtulo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg text-gray-700 font-medium\",\n                                                            children: noticia.subtitulo\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this),\n                                                noticia.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                            children: \"Resumen\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base text-gray-700 leading-relaxed\",\n                                                            children: noticia.resumen\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this),\n                                                noticia.imagenUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                            children: \"Imagen\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: noticia.imagenUrl,\n                                                            alt: noticia.imagenAlt || noticia.titulo,\n                                                            className: \"w-full max-w-2xl h-64 object-cover rounded-lg mt-2\",\n                                                            onError: (e)=>{\n                                                                e.currentTarget.style.display = 'none';\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                            children: \"Contenido\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none\",\n                                                            children: noticia.contenido\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4 border-t border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                        children: [\n                                                            noticia.autor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Autor:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    noticia.autor\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            noticia.fuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Fuente:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    noticia.fuente\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            noticia.urlFuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"md:col-span-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"URL Fuente:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    ' ',\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: noticia.urlFuente,\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                                                                        children: noticia.urlFuente\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-6 w-6 mr-2 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Versiones Generadas por IA (\",\n                                            versiones.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: [\n                                            \"Versiones reescritas autom\\xe1ticamente para diferentes diarios.\",\n                                            canReview && ' Puedes aprobar o rechazar cada versión individualmente.'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, this),\n                            versiones.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No hay versiones generadas\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Las versiones generadas por IA aparecer\\xe1n aqu\\xed una vez que se active el proceso de reescritura.\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: versiones.map((version, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-green-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-4 bg-green-50 border-b border-green-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>toggleVersionExpansion(version.id),\n                                                                        className: \"flex items-center space-x-2 text-green-900 hover:text-green-700 transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    getEstadoIcon(version.estado),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"text-lg font-bold\",\n                                                                                        children: version.diario.nombre\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 470,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            expandedVersions.has(version.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 text-xs font-medium rounded-full \".concat(getEstadoBadge(version.estado)),\n                                                                        children: version.estado\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            canReview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'APROBADA'),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 493,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Aprobar\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'RECHAZADA'),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Rechazar\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 501,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'EN_REVISION'),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 rounded-md transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 507,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"En Revisi\\xf3n\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center space-x-4 text-sm text-green-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Generado por \",\n                                                                    version.usuario.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    new Date(version.createdAt).toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            version.metadatos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_FileText_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    version.metadatos.tokens_usados,\n                                                                    \" tokens\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this),\n                                            expandedVersions.has(version.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        version.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Volanta\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-green-600 font-medium uppercase tracking-wide\",\n                                                                    children: version.volanta\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"T\\xedtulo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-xl font-bold text-gray-900 leading-tight\",\n                                                                    children: version.titulo\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        version.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Subt\\xedtulo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg text-gray-700 font-medium\",\n                                                                    children: version.subtitulo\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        version.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Resumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-base text-gray-700 leading-relaxed\",\n                                                                    children: version.resumen\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Contenido\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none\",\n                                                                    children: version.contenido\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        version.metadatos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-4 border-t border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Informaci\\xf3n T\\xe9cnica\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Modelo:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 584,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \" \",\n                                                                                version.metadatos.modelo\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Tokens:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 587,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \" \",\n                                                                                version.metadatos.tokens_usados\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Tiempo:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 590,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \" \",\n                                                                                version.metadatos.tiempo_generacion,\n                                                                                \"ms\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        version.promptUsado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-4 border-t border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                                className: \"group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                                        className: \"cursor-pointer text-xs font-medium text-gray-500 uppercase tracking-wide hover:text-gray-700\",\n                                                                        children: \"Ver Prompt Utilizado\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2 p-3 bg-gray-50 rounded text-sm text-gray-700 font-mono whitespace-pre-wrap\",\n                                                                        children: version.promptUsado\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, version.id, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n_s(RevisionPage, \"O9TAX5FK5jhmI4Jb9wAwr9mL12M=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = RevisionPage;\nvar _c;\n$RefreshReg$(_c, \"RevisionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx\n"));

/***/ })

});