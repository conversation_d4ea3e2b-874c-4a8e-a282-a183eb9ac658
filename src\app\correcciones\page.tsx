'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { ArrowLeft } from 'lucide-react';
import CorreccionesList from '@/components/CorreccionesList';
import CorreccionForm from '@/components/CorreccionForm';
import CorreccionDetail from '@/components/CorreccionDetail';
import { Correccion } from '@/types/correccion';

export default function CorreccionesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [showForm, setShowForm] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [selectedCorreccion, setSelectedCorreccion] = useState<Correccion | null>(null);
  const [editingCorreccion, setEditingCorreccion] = useState<Correccion | null>(null);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const handleCreate = () => {
    setShowForm(true);
    setEditingCorreccion(null);
  };

  const handleEdit = (correccion: Correccion) => {
    setEditingCorreccion(correccion);
    setShowForm(true);
  };

  const handleView = (correccion: Correccion) => {
    setSelectedCorreccion(correccion);
    setShowDetail(true);
  };

  const handleSave = (correccion: Correccion) => {
    setShowForm(false);
    setEditingCorreccion(null);
    // Recargar la lista
    window.location.reload();
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setEditingCorreccion(null);
  };

  const handleCloseDetail = () => {
    setShowDetail(false);
    setSelectedCorreccion(null);
  };

  const handleEditFromDetail = () => {
    if (selectedCorreccion) {
      setEditingCorreccion(selectedCorreccion);
      setShowDetail(false);
      setShowForm(true);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/dashboard')}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h1 className="ml-3 text-xl font-semibold text-gray-900">
                Gestión de Correcciones
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">{session.user.name}</span>
                {session.user.role === 'ADMIN' && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Admin
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <CorreccionesList
            onEdit={handleEdit}
            onView={handleView}
            onCreate={handleCreate}
          />
        </div>
      </main>

      {/* Modals */}
      {showForm && (
        <CorreccionForm
          correccion={editingCorreccion || undefined}
          onSave={handleSave}
          onCancel={handleCancelForm}
        />
      )}

      {showDetail && selectedCorreccion && (
        <CorreccionDetail
          correccion={selectedCorreccion}
          onClose={handleCloseDetail}
          onEdit={handleEditFromDetail}
        />
      )}
    </div>
  );
} 