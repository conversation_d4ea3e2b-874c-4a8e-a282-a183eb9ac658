'use client';

import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Eye, Clock, CheckCircle, FileText, ExternalLink, Download, Image as ImageIcon, File, Mic, LogOut, User, Newspaper, Bot, ChevronDown, ChevronUp } from 'lucide-react';
import { ThemeToggle } from '@/components/theme-toggle';

interface VersionNoticia {
  id: number;
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  estado: string;
  createdAt: string;
  diario: {
    id: number;
    nombre: string;
    descripcion?: string;
  };
  usuario: {
    id: number;
    name: string;
    email: string;
  };
}

interface Noticia {
  id: number;
  titulo: string;
  subtitulo?: string;
  estado: string;
  destacada: boolean;
  publicada: boolean;
  createdAt: string;
  imagenUrl?: string | null;
  categoria?: {
    id: number;
    nombre: string;
    color: string;
  };
  user?: {
    name: string;
  };
  versiones?: VersionNoticia[];
}

interface Categoria {
  id: number;
  nombre: string;
  color: string;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export default function NoticiasPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [noticias, setNoticias] = useState<Noticia[]>([]);
  const [categorias, setCategorias] = useState<Categoria[]>([]);
  const [pagination, setPagination] = useState<Pagination | null>(null);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [selectedCategoria, setSelectedCategoria] = useState('');
  const [selectedEstado, setSelectedEstado] = useState('');
  const [expandedNoticias, setExpandedNoticias] = useState<Set<number>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session) {
      loadCategorias();
      loadNoticias();
    }
  }, [session, currentPage, search, selectedCategoria, selectedEstado]);

  const loadCategorias = async () => {
    try {
      const response = await fetch('/api/categorias');
      if (response.ok) {
        const data = await response.json();
        setCategorias(data);
      }
    } catch (error) {
      console.error('Error al cargar categorías:', error);
    }
  };

  const loadNoticias = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
      });

      if (search) params.append('search', search);
      if (selectedCategoria) params.append('categoria', selectedCategoria);
      if (selectedEstado) params.append('estado', selectedEstado);

      const response = await fetch(`/api/noticias?${params}`);
      if (response.ok) {
        const data = await response.json();
        setNoticias(data.noticias);
        setPagination(data.pagination);
      }
    } catch (error) {
      console.error('Error al cargar noticias:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta noticia?')) {
      return;
    }

    try {
      const response = await fetch(`/api/noticias/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        loadNoticias();
      } else {
        alert('Error al eliminar la noticia');
      }
    } catch (error) {
      console.error('Error al eliminar noticia:', error);
      alert('Error al eliminar la noticia');
    }
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/auth/signin' });
  };

  const toggleVersiones = (noticiaId: number) => {
    const newExpanded = new Set(expandedNoticias);
    if (newExpanded.has(noticiaId)) {
      newExpanded.delete(noticiaId);
    } else {
      newExpanded.add(noticiaId);
    }
    setExpandedNoticias(newExpanded);
  };

  const getEstadoVersionBadge = (estado: string) => {
    const badges = {
      'GENERADA': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      'APROBADA': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'RECHAZADA': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      'EN_REVISION': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    };
    return badges[estado as keyof typeof badges] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'PUBLICADA':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'APROBADA':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'EN_REVISION':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'BORRADOR':
        return 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'PUBLICADA':
        return <CheckCircle className="h-4 w-4" />;
      case 'APROBADA':
        return <CheckCircle className="h-4 w-4" />;
      case 'EN_REVISION':
        return <Clock className="h-4 w-4" />;
      case 'BORRADOR':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 dark:border-blue-400"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 sticky top-0 z-10">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center cursor-pointer" onClick={() => router.push('/dashboard')}>
                <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Newspaper className="h-5 w-5 text-white" />
                </div>
                <h1 className="ml-3 text-xl font-semibold text-gray-900 dark:text-gray-100">
                  Panel de Noticias
                </h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-gray-400" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {session.user?.name || 'Usuario'}
                </span>
              </div>
              <button
                onClick={handleSignOut}
                className="p-1 rounded-full text-gray-400 hover:text-gray-500 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
              >
                <LogOut className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Lista de Noticias</h1>
          <button
            onClick={() => router.push('/noticias/nueva')}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus className="-ml-1 mr-2 h-5 w-5" />
            Nueva Noticia
          </button>
        </div>

        {/* Filters */}
        <div className="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="Buscar por título..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <select
                value={selectedCategoria}
                onChange={(e) => setSelectedCategoria(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Todas las categorías</option>
                {categorias.map((cat) => (
                  <option key={cat.id} value={cat.id.toString()}>
                    {cat.nombre}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <select
                value={selectedEstado}
                onChange={(e) => setSelectedEstado(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Todos los estados</option>
                <option value="BORRADOR">Borrador</option>
                <option value="EN_REVISION">En Revisión</option>
                <option value="APROBADA">Aprobada</option>
                <option value="PUBLICADA">Publicada</option>
              </select>
            </div>
            <button className="w-full p-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-600">
              <Filter className="mr-2 h-5 w-5" />
              Filtros Avanzados
            </button>
          </div>
        </div>

        {/* News List */}
        <div className="space-y-3 md:space-y-4">
          {noticias.length > 0 ? (
            noticias.map((noticia) => (
              <div key={noticia.id} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-200">
                {/* Mobile Layout (< 768px) */}
                <div className="block md:hidden p-4">
                  <div className="flex items-start space-x-3 mb-3">
                    {/* Imagen en móvil */}
                    <div className="w-20 h-16 flex-shrink-0">
                      {noticia.imagenUrl ? (
                        <img
                          src={noticia.imagenUrl}
                          alt={noticia.titulo}
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                          <ImageIcon className="h-6 w-6 text-gray-400 dark:text-gray-500" />
                        </div>
                      )}
                    </div>

                    {/* Contenido principal en móvil */}
                    <div className="flex-1 min-w-0">
                      {/* Fecha y estado en móvil */}
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(noticia.createdAt).toLocaleDateString('es-ES', {
                            day: '2-digit',
                            month: 'short'
                          })}
                        </span>
                        <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                          noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          noticia.estado === 'APROBADA' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        }`}>
                          {noticia.estado === 'PUBLICADA' ? 'Publicado' :
                           noticia.estado === 'APROBADA' ? 'Aprobada' :
                           noticia.estado === 'EN_REVISION' ? 'En Revisión' :
                           'Borrador'}
                        </span>
                      </div>

                      {/* Título en móvil */}
                      <h3
                        className="text-sm font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer line-clamp-2 mb-2"
                        onClick={() => router.push(`/noticias/${noticia.id}`)}
                      >
                        {noticia.titulo}
                      </h3>

                      {/* Autor en móvil */}
                      <div className="text-xs text-gray-600 dark:text-gray-300">
                        Por: {noticia.user?.name || 'N/A'}
                      </div>
                    </div>
                  </div>

                  {/* Categoría en móvil */}
                  {noticia.categoria && (
                    <div className="flex items-center space-x-2 mb-3">
                      <div
                        className="w-3 h-3 rounded-sm"
                        style={{ backgroundColor: noticia.categoria.color }}
                      ></div>
                      <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                        {noticia.categoria.nombre}
                      </span>
                    </div>
                  )}

                  {/* Botones en móvil - más grandes para touch */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => router.push(`/noticias/${noticia.id}`)}
                        className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors duration-200"
                        title="Ver noticia"
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span>Ver</span>
                      </button>
                      <button
                        onClick={() => router.push(`/noticias/${noticia.id}/editar`)}
                        className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors duration-200"
                        title="Editar noticia"
                      >
                        <Edit className="h-4 w-4" />
                        <span>Editar</span>
                      </button>
                      <button
                        onClick={() => handleDelete(noticia.id)}
                        className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200"
                        title="Eliminar noticia"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Estadísticas en móvil */}
                    <div className="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <ImageIcon className="h-3 w-3" />
                        <span>{noticia.imagenUrl ? 1 : 0}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <File className="h-3 w-3" />
                        <span>0</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Mic className="h-3 w-3" />
                        <span>0</span>
                      </div>
                    </div>
                  </div>

                  {/* Versiones de IA en móvil - Solo mostrar si tiene versiones */}
                  {noticia.versiones && noticia.versiones.length > 0 && (
                    <div className="mt-3 border-t border-gray-200 dark:border-gray-600 pt-3">
                      {/* Header de versiones - clickeable para expandir/colapsar */}
                      <div
                        className="flex items-center justify-between cursor-pointer"
                        onClick={() => toggleVersiones(noticia.id)}
                      >
                        <div className="flex items-center space-x-2">
                          <Bot className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Versiones IA ({noticia.versiones.length})
                          </span>
                        </div>
                        {expandedNoticias.has(noticia.id) ? (
                          <ChevronUp className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-gray-500" />
                        )}
                      </div>

                      {/* Contenido expandible de versiones en móvil */}
                      {expandedNoticias.has(noticia.id) && (
                        <div className="mt-3 space-y-2">
                          {noticia.versiones.map((version) => (
                            <div
                              key={version.id}
                              className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border-l-4 border-green-500"
                            >
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                                  {version.diario.nombre}
                                </span>
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getEstadoVersionBadge(version.estado)}`}>
                                  {version.estado}
                                </span>
                              </div>

                              <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2 mb-2">
                                {version.titulo}
                              </p>

                              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                                <span>Por: {version.usuario.name}</span>
                                <button
                                  onClick={() => router.push(`/noticias/${noticia.id}/revision`)}
                                  className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                                >
                                  Ver →
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Tablet & Desktop Layout (>= 768px) */}
                <div className="hidden md:flex items-center p-4 lg:p-6">
                  {/* Columna izquierda - Información temporal y autoría */}
                  <div className="flex flex-col items-start space-y-2 w-28 lg:w-32 xl:w-36 flex-shrink-0">
                    {/* Día y hora de creación */}
                    <div className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                      {new Date(noticia.createdAt).toLocaleDateString('es-ES', {
                        day: '2-digit',
                        month: 'short'
                      })}, {new Date(noticia.createdAt).toLocaleTimeString('es-ES', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })} hs
                    </div>

                    {/* Estado */}
                    <span className={`px-2 lg:px-3 py-1 rounded-full text-xs font-medium ${
                      noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      noticia.estado === 'APROBADA' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                    }`}>
                      {noticia.estado === 'PUBLICADA' ? 'Publicado' :
                       noticia.estado === 'APROBADA' ? 'Aprobada' :
                       noticia.estado === 'EN_REVISION' ? 'En Revisión' :
                       'Borrador'}
                    </span>

                    {/* Nombre del autor */}
                    <div className="text-xs text-gray-600 dark:text-gray-300 font-medium">
                      Por: {noticia.user?.name || 'N/A'}
                    </div>
                  </div>

                  {/* Contenido principal */}
                  <div className="flex-1 px-3 lg:px-4 xl:px-6 min-w-0">
                    {/* Título */}
                    <h3
                      className="text-sm lg:text-base xl:text-lg font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer line-clamp-2 mb-2"
                      onClick={() => router.push(`/noticias/${noticia.id}`)}
                    >
                      {noticia.titulo}
                    </h3>

                    {/* Categoría */}
                    {noticia.categoria && (
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-sm"
                          style={{ backgroundColor: noticia.categoria.color }}
                        ></div>
                        <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                          {noticia.categoria.nombre}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Imagen */}
                  <div className="w-20 h-16 lg:w-24 lg:h-20 xl:w-28 xl:h-22 flex-shrink-0 ml-3 lg:ml-4">
                    {noticia.imagenUrl ? (
                      <img
                        src={noticia.imagenUrl}
                        alt={noticia.titulo}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                        <ImageIcon className="h-6 w-6 lg:h-8 lg:w-8 text-gray-400 dark:text-gray-500" />
                      </div>
                    )}
                  </div>

                  {/* Columna derecha - Botones y estadísticas */}
                  <div className="flex flex-col items-center space-y-3 ml-3 lg:ml-4 xl:ml-6">
                    {/* Botones de acción horizontales - solo iconos */}
                    <div className="flex items-center space-x-1 lg:space-x-2">
                      <button
                        onClick={() => router.push(`/noticias/${noticia.id}`)}
                        className="p-2 lg:p-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                        title="Ver noticia"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => router.push(`/noticias/${noticia.id}/editar`)}
                        className="p-2 lg:p-2.5 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200"
                        title="Editar noticia"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(noticia.id)}
                        className="p-2 lg:p-2.5 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200"
                        title="Eliminar noticia"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Estadísticas debajo de los botones */}
                    <div className="flex flex-col items-center space-y-1 text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-200 dark:border-gray-600">
                      <div className="flex items-center space-x-1">
                        <ImageIcon className="h-3 w-3" />
                        <span>{noticia.imagenUrl ? 1 : 0}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <File className="h-3 w-3" />
                        <span>0</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Mic className="h-3 w-3" />
                        <span>0</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Versiones de IA - Solo mostrar si tiene versiones */}
                {noticia.versiones && noticia.versiones.length > 0 && (
                  <div className="border-t border-gray-200 dark:border-gray-600">
                    {/* Header de versiones - clickeable para expandir/colapsar */}
                    <div
                      className="px-4 py-3 bg-gray-50 dark:bg-gray-700 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => toggleVersiones(noticia.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Bot className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Versiones IA ({noticia.versiones.length})
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="flex space-x-1">
                            {noticia.versiones.map((version) => (
                              <span
                                key={version.id}
                                className={`px-2 py-1 text-xs font-medium rounded-full ${getEstadoVersionBadge(version.estado)}`}
                              >
                                {version.diario.nombre}
                              </span>
                            ))}
                          </div>
                          {expandedNoticias.has(noticia.id) ? (
                            <ChevronUp className="h-4 w-4 text-gray-500" />
                          ) : (
                            <ChevronDown className="h-4 w-4 text-gray-500" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Contenido expandible de versiones */}
                    {expandedNoticias.has(noticia.id) && (
                      <div className="px-4 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600">
                        <div className="space-y-3">
                          {noticia.versiones.map((version) => (
                            <div
                              key={version.id}
                              className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border-l-4 border-green-500"
                            >
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                                    {version.diario.nombre}
                                  </span>
                                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getEstadoVersionBadge(version.estado)}`}>
                                    {version.estado}
                                  </span>
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {new Date(version.createdAt).toLocaleDateString('es-ES', {
                                    day: '2-digit',
                                    month: 'short',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </div>
                              </div>

                              <div className="space-y-2">
                                {version.volanta && (
                                  <div>
                                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Volanta</span>
                                    <p className="text-xs text-green-600 dark:text-green-400 font-medium uppercase">
                                      {version.volanta}
                                    </p>
                                  </div>
                                )}

                                <div>
                                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Título</span>
                                  <p className="text-sm font-semibold text-gray-900 dark:text-gray-100 line-clamp-2">
                                    {version.titulo}
                                  </p>
                                </div>

                                {version.subtitulo && (
                                  <div>
                                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Subtítulo</span>
                                    <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">
                                      {version.subtitulo}
                                    </p>
                                  </div>
                                )}

                                <div>
                                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Contenido</span>
                                  <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
                                    {version.contenido}
                                  </p>
                                </div>

                                <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-600">
                                  <div className="text-xs text-gray-500 dark:text-gray-400">
                                    Generado por: {version.usuario.name}
                                  </div>
                                  <button
                                    onClick={() => router.push(`/noticias/${noticia.id}/revision`)}
                                    className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                                  >
                                    Ver detalles →
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
              No se encontraron noticias
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination && pagination.pages > 1 && (
          <div className="mt-4 md:mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 px-4 md:px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  Mostrando página {pagination.page} de {pagination.pages}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Anterior
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(pagination.pages, currentPage + 1))}
                    disabled={currentPage === pagination.pages}
                    className="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Siguiente
                  </button>
                </div>
              </div>
            </div>
          )}
      </main>
    </div>
  );
}