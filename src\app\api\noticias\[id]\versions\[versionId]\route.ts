import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { rewriteNoticia } from '@/lib/ai-service';

const prisma = new PrismaClient();

// PUT - Update AI-generated version
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; versionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(params.id);
    const versionId = parseInt(params.versionId);

    if (isNaN(id) || isNaN(versionId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const body = await request.json();
    const { titulo, volanta, resumen, contenido } = body;

    // Verificar que la versión existe y pertenece a la noticia
    const existingVersion = await prisma.versionNoticia.findFirst({
      where: {
        id: versionId,
        noticiaId: id,
      },
    });

    if (!existingVersion) {
      return NextResponse.json({ error: 'Versión no encontrada' }, { status: 404 });
    }

    // Actualizar la versión
    const updatedVersion = await prisma.versionNoticia.update({
      where: { id: versionId },
      data: {
        titulo: titulo || existingVersion.titulo,
        volanta: volanta !== undefined ? volanta : existingVersion.volanta,
        resumen: resumen !== undefined ? resumen : existingVersion.resumen,
        contenido: contenido || existingVersion.contenido,
        updatedAt: new Date(),
      },
      include: {
        diario: true,
        usuario: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(updatedVersion);

  } catch (error) {
    console.error('Error updating version:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Regenerate AI version
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string; versionId: string } }
) {
  try {
    console.log('🔄 Iniciando regeneración de versión:', { id: params.id, versionId: params.versionId });

    const session = await getServerSession(authOptions);

    if (!session?.user) {
      console.log('❌ Usuario no autorizado');
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(params.id);
    const versionId = parseInt(params.versionId);

    if (isNaN(id) || isNaN(versionId)) {
      console.log('❌ IDs inválidos:', { id, versionId });
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    console.log('📖 Obteniendo noticia y versión...');

    // Obtener la noticia original y la versión existente
    const [noticia, existingVersion] = await Promise.all([
      prisma.noticia.findUnique({
        where: { id },
        include: { categoria: true },
      }),
      prisma.versionNoticia.findFirst({
        where: {
          id: versionId,
          noticiaId: id,
        },
        include: { diario: true },
      }),
    ]);

    if (!noticia || !existingVersion) {
      console.log('❌ Noticia o versión no encontrada:', { noticia: !!noticia, existingVersion: !!existingVersion });
      return NextResponse.json({ error: 'Noticia o versión no encontrada' }, { status: 404 });
    }

    console.log('🤖 Llamando a OpenAI para regenerar...');
    console.log('Prompt del diario:', existingVersion.diario.prompt);

    // Regenerar usando el prompt del diario
    const rewriteResponse = await rewriteNoticia({
      titulo: noticia.titulo,
      subtitulo: noticia.subtitulo,
      volanta: noticia.volanta,
      contenido: noticia.contenido,
      resumen: noticia.resumen,
      prompt: existingVersion.diario.prompt,
      diarioNombre: existingVersion.diario.nombre,
    }, existingVersion.diario.id);

    console.log('✅ Respuesta de OpenAI recibida');

    // Actualizar la versión con el contenido regenerado
    const updatedVersion = await prisma.versionNoticia.update({
      where: { id: versionId },
      data: {
        titulo: rewriteResponse.titulo,
        volanta: rewriteResponse.volanta,
        resumen: rewriteResponse.resumen,
        contenido: rewriteResponse.contenido,
        metadatos: JSON.stringify(rewriteResponse.metadatos),
        promptUsado: existingVersion.diario.prompt,
        updatedAt: new Date(),
      },
      include: {
        diario: true,
        usuario: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('✅ Versión actualizada en base de datos');
    return NextResponse.json(updatedVersion);

  } catch (error) {
    console.error('❌ Error regenerating version:', error);
    return NextResponse.json(
      { error: `Error al regenerar la versión: ${error instanceof Error ? error.message : 'Error desconocido'}` },
      { status: 500 }
    );
  }
}
