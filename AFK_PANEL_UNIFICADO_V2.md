# ANÁLISIS FUNCIONAL COMPLETO (AFK)
## PANEL UNIFICADO V2

---

## 📋 INFORMACIÓN GENERAL

**Nombre del Proyecto:** Panel Unificado V2
**Versión:** 0.1.0
**Tipo:** Aplicación Web de Gestión de Noticias y Correcciones
**Framework Principal:** Next.js 15.3.4 con TypeScript
**Base de Datos:** SQLite con Prisma ORM
**Puerto de Desarrollo:** 3010

---

## 🏗️ ARQUITECTURA TÉCNICA

### **Stack Tecnológico**

#### **Frontend**
- **Framework:** Next.js 15.3.4 (App Router)
- **Lenguaje:** TypeScript 5.8.3
- **UI Framework:** React 19.0.0
- **Estilos:** Tailwind CSS 3.4.0 + Tailwind Animate
- **Componentes UI:** Radix UI + shadcn/ui
- **Iconos:** Lucide React
- **Te<PERSON>:** next-themes (modo claro/oscuro)

#### **Backend**
- **API:** Next.js API Routes
- **ORM:** Prisma 6.10.1
- **Base de Datos:** SQLite (desarrollo)
- **Autenticación:** NextAuth.js 4.24.11
- **Validación:** Zod 3.25.67
- **Encriptación:** bcrypt 6.0.0

#### **Estado y Gestión**
- **Estado Global:** Zustand 5.0.5
- **Sesiones:** NextAuth con JWT
- **Gestión de Archivos:** Cloudinary 2.7.0

#### **Desarrollo**
- **Linter:** ESLint 9
- **Bundler:** Next.js (Webpack interno)
- **Package Manager:** pnpm
- **Tipado:** TypeScript estricto

---

## 🗄️ MODELO DE DATOS

### **Entidades Principales**

#### **1. User (Usuarios)**
```typescript
{
  id: number (PK)
  email: string (unique)
  name: string
  password: string (hashed)
  role: Role (ADMIN | EDITOR | USER)
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
}
```

#### **2. Categoria (Categorías de Noticias)**
```typescript
{
  id: number (PK)
  nombre: string (unique)
  descripcion: string?
  color: string (hex color)
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
}
```

#### **3. Noticia (Noticias)**
```typescript
{
  id: number (PK)
  titulo: string
  subtitulo: string?
  volanta: string?
  contenido: string
  resumen: string?
  imagenUrl: string?
  imagenAlt: string?
  autor: string?
  fuente: string?
  urlFuente: string?
  estado: EstadoNoticia
  destacada: boolean
  publicada: boolean
  fechaPublicacion: DateTime?
  categoriaId: number? (FK)
  userId: number (FK)
  createdAt: DateTime
  updatedAt: DateTime
}
```

#### **4. Correccion (Sistema Legacy)**
```typescript
{
  id: number (PK)
  titulo: string
  contenido: string
  medio: string
  fechaPublicacion: DateTime
  fechaCorreccion: DateTime
  estado: Estado
  prioridad: Prioridad
  imagenUrl: string?
  observaciones: string?
  userId: number (FK)
  createdAt: DateTime
  updatedAt: DateTime
}
```

### **Enumeraciones**

#### **Roles de Usuario**
- `ADMIN`: Acceso completo al sistema
- `EDITOR`: Gestión de noticias y contenido
- `USER`: Creación y edición de noticias propias

#### **Estados de Noticia**
- `BORRADOR`: Noticia en creación
- `EN_REVISION`: Pendiente de revisión
- `APROBADA`: Aprobada para publicación
- `PUBLICADA`: Publicada y visible
- `ARCHIVADA`: Archivada

#### **Estados de Corrección**
- `PENDIENTE`: Corrección pendiente
- `EN_REVISION`: En proceso de revisión
- `COMPLETADA`: Corrección completada
- `RECHAZADA`: Corrección rechazada

#### **Prioridades**
- `BAJA`: Prioridad baja
- `MEDIA`: Prioridad media
- `ALTA`: Prioridad alta
- `URGENTE`: Prioridad urgente

---

## 🔐 SISTEMA DE AUTENTICACIÓN

### **Configuración NextAuth**
- **Estrategia:** Credentials Provider
- **Sesiones:** JWT (JSON Web Tokens)
- **Encriptación:** bcrypt para contraseñas
- **Página de Login:** `/auth/signin`

### **Flujo de Autenticación**
1. Usuario ingresa credenciales
2. Verificación contra base de datos
3. Generación de JWT con rol y ID
4. Redirección a dashboard
5. Middleware de protección de rutas

### **Usuarios de Prueba**
- **Admin:** <EMAIL> / admin123
- **Editor:** <EMAIL> / editor123
- **Usuario:** <EMAIL> / user123