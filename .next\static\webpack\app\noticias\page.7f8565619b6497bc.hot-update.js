"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/noticias/page",{

/***/ "(app-pages-browser)/./src/app/noticias/page.tsx":
/*!***********************************!*\
  !*** ./src/app/noticias/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NoticiasPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,ChevronDown,ChevronUp,Clock,Edit,ExternalLink,File,FileText,Filter,Image,LogOut,Mic,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _components_theme_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-toggle */ \"(app-pages-browser)/./src/components/theme-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NoticiasPage() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [noticias, setNoticias] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [categorias, setCategorias] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [selectedCategoria, setSelectedCategoria] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [selectedEstado, setSelectedEstado] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [expandedNoticias, setExpandedNoticias] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Set());\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(1);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NoticiasPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"NoticiasPage.useEffect\"], [\n        status,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NoticiasPage.useEffect\": ()=>{\n            if (session) {\n                loadCategorias();\n                loadNoticias();\n            }\n        }\n    }[\"NoticiasPage.useEffect\"], [\n        session,\n        currentPage,\n        search,\n        selectedCategoria,\n        selectedEstado\n    ]);\n    const loadCategorias = async ()=>{\n        try {\n            const response = await fetch('/api/categorias');\n            if (response.ok) {\n                const data = await response.json();\n                setCategorias(data);\n            }\n        } catch (error) {\n            console.error('Error al cargar categorías:', error);\n        }\n    };\n    const loadNoticias = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: '10'\n            });\n            if (search) params.append('search', search);\n            if (selectedCategoria) params.append('categoria', selectedCategoria);\n            if (selectedEstado) params.append('estado', selectedEstado);\n            const response = await fetch(\"/api/noticias?\".concat(params));\n            if (response.ok) {\n                const data = await response.json();\n                setNoticias(data.noticias);\n                setPagination(data.pagination);\n            }\n        } catch (error) {\n            console.error('Error al cargar noticias:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta noticia?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                loadNoticias();\n            } else {\n                alert('Error al eliminar la noticia');\n            }\n        } catch (error) {\n            console.error('Error al eliminar noticia:', error);\n            alert('Error al eliminar la noticia');\n        }\n    };\n    const handleSignOut = async ()=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n            callbackUrl: '/auth/signin'\n        });\n    };\n    const toggleVersiones = (noticiaId)=>{\n        const newExpanded = new Set(expandedNoticias);\n        if (newExpanded.has(noticiaId)) {\n            newExpanded.delete(noticiaId);\n        } else {\n            newExpanded.add(noticiaId);\n        }\n        setExpandedNoticias(newExpanded);\n    };\n    const getEstadoVersionBadge = (estado)=>{\n        const badges = {\n            'GENERADA': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n            'APROBADA': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n            'RECHAZADA': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n            'EN_REVISION': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n        };\n        return badges[estado] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n    };\n    const getEstadoColor = (estado)=>{\n        switch(estado){\n            case 'PUBLICADA':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'APROBADA':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n            case 'EN_REVISION':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'BORRADOR':\n                return 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n            default:\n                return 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n        }\n    };\n    const getEstadoIcon = (estado)=>{\n        switch(estado){\n            case 'PUBLICADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 16\n                }, this);\n            case 'APROBADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 16\n                }, this);\n            case 'EN_REVISION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 16\n                }, this);\n            case 'BORRADOR':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (status === 'loading' || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 dark:border-blue-400\"\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 sticky top-0 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center cursor-pointer\",\n                                    onClick: ()=>router.push('/dashboard'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"ml-3 text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: \"Panel de Noticias\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                children: ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || 'Usuario'\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSignOut,\n                                        className: \"p-1 rounded-full text-gray-400 hover:text-gray-500 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-gray-100\",\n                                children: \"Lista de Noticias\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/noticias/nueva'),\n                                className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"-ml-1 mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Nueva Noticia\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Buscar por t\\xedtulo...\",\n                                            value: search,\n                                            onChange: (e)=>setSearch(e.target.value),\n                                            className: \"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200 focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategoria,\n                                        onChange: (e)=>setSelectedCategoria(e.target.value),\n                                        className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200 focus:ring-blue-500 focus:border-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Todas las categor\\xedas\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            categorias.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: cat.id.toString(),\n                                                    children: cat.nombre\n                                                }, cat.id, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedEstado,\n                                        onChange: (e)=>setSelectedEstado(e.target.value),\n                                        className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200 focus:ring-blue-500 focus:border-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Todos los estados\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"BORRADOR\",\n                                                children: \"Borrador\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"EN_REVISION\",\n                                                children: \"En Revisi\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"APROBADA\",\n                                                children: \"Aprobada\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PUBLICADA\",\n                                                children: \"Publicada\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full p-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mr-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Filtros Avanzados\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 md:space-y-4\",\n                        children: noticias.length > 0 ? noticias.map((noticia)=>{\n                            var _noticia_user, _noticia_user1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"block md:hidden p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-20 h-16 flex-shrink-0\",\n                                                        children: noticia.imagenUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: noticia.imagenUrl,\n                                                            alt: noticia.titulo,\n                                                            className: \"w-full h-full object-cover rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-6 w-6 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: new Date(noticia.createdAt).toLocaleDateString('es-ES', {\n                                                                            day: '2-digit',\n                                                                            month: 'short'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-0.5 rounded-full text-xs font-medium \".concat(noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : noticia.estado === 'APROBADA' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'),\n                                                                        children: noticia.estado === 'PUBLICADA' ? 'Publicado' : noticia.estado === 'APROBADA' ? 'Aprobada' : noticia.estado === 'EN_REVISION' ? 'En Revisión' : 'Borrador'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer line-clamp-2 mb-2\",\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id)),\n                                                                children: noticia.titulo\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 dark:text-gray-300\",\n                                                                children: [\n                                                                    \"Por: \",\n                                                                    ((_noticia_user = noticia.user) === null || _noticia_user === void 0 ? void 0 : _noticia_user.name) || 'N/A'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-sm\",\n                                                        style: {\n                                                            backgroundColor: noticia.categoria.color\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-blue-600 dark:text-blue-400 font-medium\",\n                                                        children: noticia.categoria.nombre\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id)),\n                                                                className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors duration-200\",\n                                                                title: \"Ver noticia\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Ver\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id, \"/editar\")),\n                                                                className: \"flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors duration-200\",\n                                                                title: \"Editar noticia\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Editar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDelete(noticia.id),\n                                                                className: \"p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200\",\n                                                                title: \"Eliminar noticia\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: noticia.imagenUrl ? 1 : 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this),\n                                            noticia.versiones && noticia.versiones.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 border-t border-gray-200 dark:border-gray-600 pt-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between cursor-pointer\",\n                                                        onClick: ()=>toggleVersiones(noticia.id),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                                        children: [\n                                                                            \"Versiones IA (\",\n                                                                            noticia.versiones.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            expandedNoticias.has(noticia.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 27\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    expandedNoticias.has(noticia.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-3 space-y-2\",\n                                                        children: noticia.versiones.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border-l-4 border-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-semibold text-gray-900 dark:text-gray-100\",\n                                                                                children: version.diario.nombre\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                lineNumber: 456,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getEstadoVersionBadge(version.estado)),\n                                                                                children: version.estado\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                lineNumber: 459,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-700 dark:text-gray-300 line-clamp-2 mb-2\",\n                                                                        children: version.titulo\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"Por: \",\n                                                                                    version.usuario.name\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                lineNumber: 469,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id, \"/revision\")),\n                                                                                className: \"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium\",\n                                                                                children: \"Ver →\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, version.id, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center p-4 lg:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-start space-y-2 w-28 lg:w-32 xl:w-36 flex-shrink-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400 font-medium\",\n                                                        children: [\n                                                            new Date(noticia.createdAt).toLocaleDateString('es-ES', {\n                                                                day: '2-digit',\n                                                                month: 'short'\n                                                            }),\n                                                            \", \",\n                                                            new Date(noticia.createdAt).toLocaleTimeString('es-ES', {\n                                                                hour: '2-digit',\n                                                                minute: '2-digit'\n                                                            }),\n                                                            \" hs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 lg:px-3 py-1 rounded-full text-xs font-medium \".concat(noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : noticia.estado === 'APROBADA' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'),\n                                                        children: noticia.estado === 'PUBLICADA' ? 'Publicado' : noticia.estado === 'APROBADA' ? 'Aprobada' : noticia.estado === 'EN_REVISION' ? 'En Revisión' : 'Borrador'\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-600 dark:text-gray-300 font-medium\",\n                                                        children: [\n                                                            \"Por: \",\n                                                            ((_noticia_user1 = noticia.user) === null || _noticia_user1 === void 0 ? void 0 : _noticia_user1.name) || 'N/A'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 px-3 lg:px-4 xl:px-6 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm lg:text-base xl:text-lg font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer line-clamp-2 mb-2\",\n                                                        onClick: ()=>router.push(\"/noticias/\".concat(noticia.id)),\n                                                        children: noticia.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-sm\",\n                                                                style: {\n                                                                    backgroundColor: noticia.categoria.color\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-blue-600 dark:text-blue-400 font-medium\",\n                                                                children: noticia.categoria.nombre\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-16 lg:w-24 lg:h-20 xl:w-28 xl:h-22 flex-shrink-0 ml-3 lg:ml-4\",\n                                                children: noticia.imagenUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: noticia.imagenUrl,\n                                                    alt: noticia.titulo,\n                                                    className: \"w-full h-full object-cover rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 lg:h-8 lg:w-8 text-gray-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center space-y-3 ml-3 lg:ml-4 xl:ml-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 lg:space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id)),\n                                                                className: \"p-2 lg:p-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200\",\n                                                                title: \"Ver noticia\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/noticias/\".concat(noticia.id, \"/editar\")),\n                                                                className: \"p-2 lg:p-2.5 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200\",\n                                                                title: \"Editar noticia\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDelete(noticia.id),\n                                                                className: \"p-2 lg:p-2.5 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200\",\n                                                                title: \"Eliminar noticia\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center space-y-1 text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-200 dark:border-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: noticia.imagenUrl ? 1 : 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 589,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                        lineNumber: 597,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 17\n                                    }, this),\n                                    noticia.versiones && noticia.versiones.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-200 dark:border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-3 bg-gray-50 dark:bg-gray-700 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\",\n                                                onClick: ()=>toggleVersiones(noticia.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                                    children: [\n                                                                        \"Versiones IA (\",\n                                                                        noticia.versiones.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-1\",\n                                                                    children: noticia.versiones.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getEstadoVersionBadge(version.estado)),\n                                                                            children: version.diario.nombre\n                                                                        }, version.id, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedNoticias.has(noticia.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_ChevronDown_ChevronUp_Clock_Edit_ExternalLink_File_FileText_Filter_Image_LogOut_Mic_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 632,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 21\n                                            }, this),\n                                            expandedNoticias.has(noticia.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: noticia.versiones.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border-l-4 border-green-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-semibold text-gray-900 dark:text-gray-100\",\n                                                                                    children: version.diario.nombre\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 649,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getEstadoVersionBadge(version.estado)),\n                                                                                    children: version.estado\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 652,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                            lineNumber: 648,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: new Date(version.createdAt).toLocaleDateString('es-ES', {\n                                                                                day: '2-digit',\n                                                                                month: 'short',\n                                                                                hour: '2-digit',\n                                                                                minute: '2-digit'\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                            lineNumber: 656,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        version.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\",\n                                                                                    children: \"Volanta\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 669,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-green-600 dark:text-green-400 font-medium uppercase\",\n                                                                                    children: version.volanta\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 670,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\",\n                                                                                    children: \"T\\xedtulo\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 677,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-semibold text-gray-900 dark:text-gray-100 line-clamp-2\",\n                                                                                    children: version.titulo\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 678,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                            lineNumber: 676,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        version.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\",\n                                                                                    children: \"Subt\\xedtulo\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 685,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-700 dark:text-gray-300 line-clamp-2\",\n                                                                                    children: version.subtitulo\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 686,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\",\n                                                                                    children: \"Contenido\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 693,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-700 dark:text-gray-300 line-clamp-3\",\n                                                                                    children: version.contenido\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 694,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                                    children: [\n                                                                                        \"Generado por: \",\n                                                                                        version.usuario.name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 700,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>router.push(\"/noticias/\".concat(noticia.id, \"/revision\")),\n                                                                                    className: \"text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium\",\n                                                                                    children: \"Ver detalles →\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                                    lineNumber: 703,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, version.id, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 29\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, noticia.id, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-8 text-center text-gray-500 dark:text-gray-400\",\n                            children: \"No se encontraron noticias\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this),\n                    pagination && pagination.pages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 md:mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 px-4 md:px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                    children: [\n                                        \"Mostrando p\\xe1gina \",\n                                        pagination.page,\n                                        \" de \",\n                                        pagination.pages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                            disabled: currentPage === 1,\n                                            className: \"px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Anterior\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(Math.min(pagination.pages, currentPage + 1)),\n                                            disabled: currentPage === pagination.pages,\n                                            className: \"px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Siguiente\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\page.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(NoticiasPage, \"fzv/lW3rAvi46OM1ozYjgnW1OQU=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NoticiasPage;\nvar _c;\n$RefreshReg$(_c, \"NoticiasPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbm90aWNpYXMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVzRDtBQUNWO0FBQ0E7QUFDc0s7QUFDMUo7QUF3RHpDLFNBQVN5QjtRQTJLTEM7O0lBMUtqQixNQUFNLEVBQUVDLE1BQU1ELE9BQU8sRUFBRUUsTUFBTSxFQUFFLEdBQUc1QiwyREFBVUE7SUFDNUMsTUFBTTZCLFNBQVMzQiwwREFBU0E7SUFDeEIsTUFBTSxDQUFDNEIsVUFBVUMsWUFBWSxHQUFHM0IsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUM0QixZQUFZQyxjQUFjLEdBQUc3QiwrQ0FBUUEsQ0FBYyxFQUFFO0lBQzVELE1BQU0sQ0FBQzhCLFlBQVlDLGNBQWMsR0FBRy9CLCtDQUFRQSxDQUFvQjtJQUNoRSxNQUFNLENBQUNnQyxTQUFTQyxXQUFXLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNrQyxRQUFRQyxVQUFVLEdBQUduQywrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUNvQyxtQkFBbUJDLHFCQUFxQixHQUFHckMsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDc0MsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZDLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3dDLGtCQUFrQkMsb0JBQW9CLEdBQUd6QywrQ0FBUUEsQ0FBYyxJQUFJMEM7SUFDMUUsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUUvQ0QsZ0RBQVNBO2tDQUFDO1lBQ1IsSUFBSXlCLFdBQVcsbUJBQW1CO2dCQUNoQ0MsT0FBT29CLElBQUksQ0FBQztZQUNkO1FBQ0Y7aUNBQUc7UUFBQ3JCO1FBQVFDO0tBQU87SUFFbkIxQixnREFBU0E7a0NBQUM7WUFDUixJQUFJdUIsU0FBUztnQkFDWHdCO2dCQUNBQztZQUNGO1FBQ0Y7aUNBQUc7UUFBQ3pCO1FBQVNxQjtRQUFhVDtRQUFRRTtRQUFtQkU7S0FBZTtJQUVwRSxNQUFNUSxpQkFBaUI7UUFDckIsSUFBSTtZQUNGLE1BQU1FLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixJQUFJRCxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTTNCLE9BQU8sTUFBTXlCLFNBQVNHLElBQUk7Z0JBQ2hDdEIsY0FBY047WUFDaEI7UUFDRixFQUFFLE9BQU82QixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1FBQy9DO0lBQ0Y7SUFFQSxNQUFNTCxlQUFlO1FBQ25CLElBQUk7WUFDRmQsV0FBVztZQUNYLE1BQU1xQixTQUFTLElBQUlDLGdCQUFnQjtnQkFDakNDLE1BQU1iLFlBQVljLFFBQVE7Z0JBQzFCQyxPQUFPO1lBQ1Q7WUFFQSxJQUFJeEIsUUFBUW9CLE9BQU9LLE1BQU0sQ0FBQyxVQUFVekI7WUFDcEMsSUFBSUUsbUJBQW1Ca0IsT0FBT0ssTUFBTSxDQUFDLGFBQWF2QjtZQUNsRCxJQUFJRSxnQkFBZ0JnQixPQUFPSyxNQUFNLENBQUMsVUFBVXJCO1lBRTVDLE1BQU1VLFdBQVcsTUFBTUMsTUFBTSxpQkFBd0IsT0FBUEs7WUFDOUMsSUFBSU4sU0FBU0UsRUFBRSxFQUFFO2dCQUNmLE1BQU0zQixPQUFPLE1BQU15QixTQUFTRyxJQUFJO2dCQUNoQ3hCLFlBQVlKLEtBQUtHLFFBQVE7Z0JBQ3pCSyxjQUFjUixLQUFLTyxVQUFVO1lBQy9CO1FBQ0YsRUFBRSxPQUFPc0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtRQUM3QyxTQUFVO1lBQ1JuQixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU0yQixlQUFlLE9BQU9DO1FBQzFCLElBQUksQ0FBQ0MsUUFBUSx3REFBd0Q7WUFDbkU7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNZCxXQUFXLE1BQU1DLE1BQU0saUJBQW9CLE9BQUhZLEtBQU07Z0JBQ2xERSxRQUFRO1lBQ1Y7WUFFQSxJQUFJZixTQUFTRSxFQUFFLEVBQUU7Z0JBQ2ZIO1lBQ0YsT0FBTztnQkFDTGlCLE1BQU07WUFDUjtRQUNGLEVBQUUsT0FBT1osT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtZQUM1Q1ksTUFBTTtRQUNSO0lBQ0Y7SUFFQSxNQUFNQyxnQkFBZ0I7UUFDcEIsTUFBTXBFLHdEQUFPQSxDQUFDO1lBQUVxRSxhQUFhO1FBQWU7SUFDOUM7SUFFQSxNQUFNQyxrQkFBa0IsQ0FBQ0M7UUFDdkIsTUFBTUMsY0FBYyxJQUFJM0IsSUFBSUY7UUFDNUIsSUFBSTZCLFlBQVlDLEdBQUcsQ0FBQ0YsWUFBWTtZQUM5QkMsWUFBWUUsTUFBTSxDQUFDSDtRQUNyQixPQUFPO1lBQ0xDLFlBQVlHLEdBQUcsQ0FBQ0o7UUFDbEI7UUFDQTNCLG9CQUFvQjRCO0lBQ3RCO0lBRUEsTUFBTUksd0JBQXdCLENBQUNDO1FBQzdCLE1BQU1DLFNBQVM7WUFDYixZQUFZO1lBQ1osWUFBWTtZQUNaLGFBQWE7WUFDYixlQUFlO1FBQ2pCO1FBQ0EsT0FBT0EsTUFBTSxDQUFDRCxPQUE4QixJQUFJO0lBQ2xEO0lBRUEsTUFBTUUsaUJBQWlCLENBQUNGO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxNQUFNRyxnQkFBZ0IsQ0FBQ0g7UUFDckIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDbkUsaU5BQVdBO29CQUFDdUUsV0FBVTs7Ozs7O1lBQ2hDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUN2RSxpTkFBV0E7b0JBQUN1RSxXQUFVOzs7Ozs7WUFDaEMsS0FBSztnQkFDSCxxQkFBTyw4REFBQ3hFLGlOQUFLQTtvQkFBQ3dFLFdBQVU7Ozs7OztZQUMxQixLQUFLO2dCQUNILHFCQUFPLDhEQUFDdEUsaU5BQVFBO29CQUFDc0UsV0FBVTs7Ozs7O1lBQzdCO2dCQUNFLHFCQUFPLDhEQUFDdEUsaU5BQVFBO29CQUFDc0UsV0FBVTs7Ozs7O1FBQy9CO0lBQ0Y7SUFFQSxJQUFJdEQsV0FBVyxhQUFhUSxTQUFTO1FBQ25DLHFCQUNFLDhEQUFDK0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7Ozs7Ozs7Ozs7O0lBR3JCO0lBRUEsSUFBSSxDQUFDeEQsU0FBUztRQUNaLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDeUQ7UUFBSUQsV0FBVTs7MEJBQ2IsOERBQUNFO2dCQUFPRixXQUFVOzBCQUNoQiw0RUFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUlELFdBQVU7MENBQ2IsNEVBQUNDO29DQUFJRCxXQUFVO29DQUFtQ0csU0FBUyxJQUFNeEQsT0FBT29CLElBQUksQ0FBQzs7c0RBQzNFLDhEQUFDa0M7NENBQUlELFdBQVU7c0RBQ2IsNEVBQUM5RCxpTkFBU0E7Z0RBQUM4RCxXQUFVOzs7Ozs7Ozs7OztzREFFdkIsOERBQUNJOzRDQUFHSixXQUFVO3NEQUE4RDs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTWhGLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUMxRCxpRUFBV0E7Ozs7O2tEQUNaLDhEQUFDMkQ7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDL0QsaU5BQUlBO2dEQUFDK0QsV0FBVTs7Ozs7OzBEQUNoQiw4REFBQ0s7Z0RBQUtMLFdBQVU7MERBQ2J4RCxFQUFBQSxnQkFBQUEsUUFBUThELElBQUksY0FBWjlELG9DQUFBQSxjQUFjK0QsSUFBSSxLQUFJOzs7Ozs7Ozs7Ozs7a0RBRzNCLDhEQUFDQzt3Q0FDQ0wsU0FBU2hCO3dDQUNUYSxXQUFVO2tEQUVWLDRFQUFDaEUsa05BQU1BOzRDQUFDZ0UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU81Qiw4REFBQ1M7Z0JBQUtULFdBQVU7O2tDQUNkLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNJO2dDQUFHSixXQUFVOzBDQUFzRDs7Ozs7OzBDQUNwRSw4REFBQ1E7Z0NBQ0NMLFNBQVMsSUFBTXhELE9BQU9vQixJQUFJLENBQUM7Z0NBQzNCaUMsV0FBVTs7a0RBRVYsOERBQUM3RSxrTkFBSUE7d0NBQUM2RSxXQUFVOzs7Ozs7b0NBQXVCOzs7Ozs7Ozs7Ozs7O2tDQU0zQyw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDNUUsa05BQU1BOzRDQUFDNEUsV0FBVTs7Ozs7O3NEQUNsQiw4REFBQ1U7NENBQ0NDLE1BQUs7NENBQ0xDLGFBQVk7NENBQ1pDLE9BQU96RDs0Q0FDUDBELFVBQVUsQ0FBQ0MsSUFBTTFELFVBQVUwRCxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQ3pDYixXQUFVOzs7Ozs7Ozs7Ozs7OENBR2QsOERBQUNDOzhDQUNDLDRFQUFDZ0I7d0NBQ0NKLE9BQU92RDt3Q0FDUHdELFVBQVUsQ0FBQ0MsSUFBTXhELHFCQUFxQndELEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDcERiLFdBQVU7OzBEQUVWLDhEQUFDa0I7Z0RBQU9MLE9BQU07MERBQUc7Ozs7Ozs0Q0FDaEIvRCxXQUFXcUUsR0FBRyxDQUFDLENBQUNDLG9CQUNmLDhEQUFDRjtvREFBb0JMLE9BQU9PLElBQUlyQyxFQUFFLENBQUNKLFFBQVE7OERBQ3hDeUMsSUFBSUMsTUFBTTttREFEQUQsSUFBSXJDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTXpCLDhEQUFDa0I7OENBQ0MsNEVBQUNnQjt3Q0FDQ0osT0FBT3JEO3dDQUNQc0QsVUFBVSxDQUFDQyxJQUFNdEQsa0JBQWtCc0QsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dDQUNqRGIsV0FBVTs7MERBRVYsOERBQUNrQjtnREFBT0wsT0FBTTswREFBRzs7Ozs7OzBEQUNqQiw4REFBQ0s7Z0RBQU9MLE9BQU07MERBQVc7Ozs7OzswREFDekIsOERBQUNLO2dEQUFPTCxPQUFNOzBEQUFjOzs7Ozs7MERBQzVCLDhEQUFDSztnREFBT0wsT0FBTTswREFBVzs7Ozs7OzBEQUN6Qiw4REFBQ0s7Z0RBQU9MLE9BQU07MERBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUc5Qiw4REFBQ0w7b0NBQU9SLFdBQVU7O3NEQUNoQiw4REFBQzNFLGtOQUFNQTs0Q0FBQzJFLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPekMsOERBQUNDO3dCQUFJRCxXQUFVO2tDQUNacEQsU0FBUzBFLE1BQU0sR0FBRyxJQUNqQjFFLFNBQVN1RSxHQUFHLENBQUMsQ0FBQ0k7Z0NBcURJQSxlQXdKRkE7aURBNU1kLDhEQUFDdEI7Z0NBQXFCRCxXQUFVOztrREFFOUIsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUViLDhEQUFDQzt3REFBSUQsV0FBVTtrRUFDWnVCLFFBQVFDLFNBQVMsaUJBQ2hCLDhEQUFDQzs0REFDQ0MsS0FBS0gsUUFBUUMsU0FBUzs0REFDdEJHLEtBQUtKLFFBQVFLLE1BQU07NERBQ25CNUIsV0FBVTs7Ozs7aUZBR1osOERBQUNDOzREQUFJRCxXQUFVO3NFQUNiLDRFQUFDbkUsa05BQVNBO2dFQUFDbUUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztrRUFNM0IsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFFYiw4REFBQ0M7Z0VBQUlELFdBQVU7O2tGQUNiLDhEQUFDSzt3RUFBS0wsV0FBVTtrRkFDYixJQUFJNkIsS0FBS04sUUFBUU8sU0FBUyxFQUFFQyxrQkFBa0IsQ0FBQyxTQUFTOzRFQUN2REMsS0FBSzs0RUFDTEMsT0FBTzt3RUFDVDs7Ozs7O2tGQUVGLDhEQUFDNUI7d0VBQUtMLFdBQVcsZ0RBS2hCLE9BSkN1QixRQUFRM0IsTUFBTSxLQUFLLGNBQWMsc0VBQ2pDMkIsUUFBUTNCLE1BQU0sS0FBSyxhQUFhLGtFQUNoQzJCLFFBQVEzQixNQUFNLEtBQUssZ0JBQWdCLDBFQUNuQztrRkFFQzJCLFFBQVEzQixNQUFNLEtBQUssY0FBYyxjQUNqQzJCLFFBQVEzQixNQUFNLEtBQUssYUFBYSxhQUNoQzJCLFFBQVEzQixNQUFNLEtBQUssZ0JBQWdCLGdCQUNuQzs7Ozs7Ozs7Ozs7OzBFQUtMLDhEQUFDc0M7Z0VBQ0NsQyxXQUFVO2dFQUNWRyxTQUFTLElBQU14RCxPQUFPb0IsSUFBSSxDQUFDLGFBQXdCLE9BQVh3RCxRQUFReEMsRUFBRTswRUFFakR3QyxRQUFRSyxNQUFNOzs7Ozs7MEVBSWpCLDhEQUFDM0I7Z0VBQUlELFdBQVU7O29FQUEyQztvRUFDbER1QixFQUFBQSxnQkFBQUEsUUFBUWpCLElBQUksY0FBWmlCLG9DQUFBQSxjQUFjaEIsSUFBSSxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRDQU1qQ2dCLFFBQVFZLFNBQVMsa0JBQ2hCLDhEQUFDbEM7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDQzt3REFDQ0QsV0FBVTt3REFDVm9DLE9BQU87NERBQUVDLGlCQUFpQmQsUUFBUVksU0FBUyxDQUFDRyxLQUFLO3dEQUFDOzs7Ozs7a0VBRXBELDhEQUFDakM7d0RBQUtMLFdBQVU7a0VBQ2J1QixRQUFRWSxTQUFTLENBQUNkLE1BQU07Ozs7Ozs7Ozs7OzswREFNL0IsOERBQUNwQjtnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ1E7Z0VBQ0NMLFNBQVMsSUFBTXhELE9BQU9vQixJQUFJLENBQUMsYUFBd0IsT0FBWHdELFFBQVF4QyxFQUFFO2dFQUNsRGlCLFdBQVU7Z0VBQ1Z1QyxPQUFNOztrRkFFTiw4REFBQzVHLGtOQUFZQTt3RUFBQ3FFLFdBQVU7Ozs7OztrRkFDeEIsOERBQUNLO2tGQUFLOzs7Ozs7Ozs7Ozs7MEVBRVIsOERBQUNHO2dFQUNDTCxTQUFTLElBQU14RCxPQUFPb0IsSUFBSSxDQUFDLGFBQXdCLE9BQVh3RCxRQUFReEMsRUFBRSxFQUFDO2dFQUNuRGlCLFdBQVU7Z0VBQ1Z1QyxPQUFNOztrRkFFTiw4REFBQ2pILGtOQUFJQTt3RUFBQzBFLFdBQVU7Ozs7OztrRkFDaEIsOERBQUNLO2tGQUFLOzs7Ozs7Ozs7Ozs7MEVBRVIsOERBQUNHO2dFQUNDTCxTQUFTLElBQU1yQixhQUFheUMsUUFBUXhDLEVBQUU7Z0VBQ3RDaUIsV0FBVTtnRUFDVnVDLE9BQU07MEVBRU4sNEVBQUNoSCxrTkFBTUE7b0VBQUN5RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrRUFLdEIsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ0M7Z0VBQUlELFdBQVU7O2tGQUNiLDhEQUFDbkUsa05BQVNBO3dFQUFDbUUsV0FBVTs7Ozs7O2tGQUNyQiw4REFBQ0s7a0ZBQU1rQixRQUFRQyxTQUFTLEdBQUcsSUFBSTs7Ozs7Ozs7Ozs7OzBFQUVqQyw4REFBQ3ZCO2dFQUFJRCxXQUFVOztrRkFDYiw4REFBQ2xFLGtOQUFJQTt3RUFBQ2tFLFdBQVU7Ozs7OztrRkFDaEIsOERBQUNLO2tGQUFLOzs7Ozs7Ozs7Ozs7MEVBRVIsOERBQUNKO2dFQUFJRCxXQUFVOztrRkFDYiw4REFBQ2pFLGtOQUFHQTt3RUFBQ2lFLFdBQVU7Ozs7OztrRkFDZiw4REFBQ0s7a0ZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0Q0FNWGtCLFFBQVFpQixTQUFTLElBQUlqQixRQUFRaUIsU0FBUyxDQUFDbEIsTUFBTSxHQUFHLG1CQUMvQyw4REFBQ3JCO2dEQUFJRCxXQUFVOztrRUFFYiw4REFBQ0M7d0RBQ0NELFdBQVU7d0RBQ1ZHLFNBQVMsSUFBTWQsZ0JBQWdCa0MsUUFBUXhDLEVBQUU7OzBFQUV6Qyw4REFBQ2tCO2dFQUFJRCxXQUFVOztrRkFDYiw4REFBQzdELGtOQUFHQTt3RUFBQzZELFdBQVU7Ozs7OztrRkFDZiw4REFBQ0s7d0VBQUtMLFdBQVU7OzRFQUF1RDs0RUFDdER1QixRQUFRaUIsU0FBUyxDQUFDbEIsTUFBTTs0RUFBQzs7Ozs7Ozs7Ozs7Ozs0REFHM0M1RCxpQkFBaUI4QixHQUFHLENBQUMrQixRQUFReEMsRUFBRSxrQkFDOUIsOERBQUMxQyxrTkFBU0E7Z0VBQUMyRCxXQUFVOzs7OztxRkFFckIsOERBQUM1RCxrTkFBV0E7Z0VBQUM0RCxXQUFVOzs7Ozs7Ozs7Ozs7b0RBSzFCdEMsaUJBQWlCOEIsR0FBRyxDQUFDK0IsUUFBUXhDLEVBQUUsbUJBQzlCLDhEQUFDa0I7d0RBQUlELFdBQVU7a0VBQ1p1QixRQUFRaUIsU0FBUyxDQUFDckIsR0FBRyxDQUFDLENBQUNzQix3QkFDdEIsOERBQUN4QztnRUFFQ0QsV0FBVTs7a0ZBRVYsOERBQUNDO3dFQUFJRCxXQUFVOzswRkFDYiw4REFBQ0s7Z0ZBQUtMLFdBQVU7MEZBQ2J5QyxRQUFRQyxNQUFNLENBQUNyQixNQUFNOzs7Ozs7MEZBRXhCLDhEQUFDaEI7Z0ZBQUtMLFdBQVcsOENBQW9GLE9BQXRDTCxzQkFBc0I4QyxRQUFRN0MsTUFBTTswRkFDaEc2QyxRQUFRN0MsTUFBTTs7Ozs7Ozs7Ozs7O2tGQUluQiw4REFBQytDO3dFQUFFM0MsV0FBVTtrRkFDVnlDLFFBQVFiLE1BQU07Ozs7OztrRkFHakIsOERBQUMzQjt3RUFBSUQsV0FBVTs7MEZBQ2IsOERBQUNLOztvRkFBSztvRkFBTW9DLFFBQVFHLE9BQU8sQ0FBQ3JDLElBQUk7Ozs7Ozs7MEZBQ2hDLDhEQUFDQztnRkFDQ0wsU0FBUyxJQUFNeEQsT0FBT29CLElBQUksQ0FBQyxhQUF3QixPQUFYd0QsUUFBUXhDLEVBQUUsRUFBQztnRkFDbkRpQixXQUFVOzBGQUNYOzs7Ozs7Ozs7Ozs7OytEQXJCRXlDLFFBQVExRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQWtDN0IsOERBQUNrQjt3Q0FBSUQsV0FBVTs7MERBRWIsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFFYiw4REFBQ0M7d0RBQUlELFdBQVU7OzREQUNaLElBQUk2QixLQUFLTixRQUFRTyxTQUFTLEVBQUVDLGtCQUFrQixDQUFDLFNBQVM7Z0VBQ3ZEQyxLQUFLO2dFQUNMQyxPQUFPOzREQUNUOzREQUFHOzREQUFHLElBQUlKLEtBQUtOLFFBQVFPLFNBQVMsRUFBRWUsa0JBQWtCLENBQUMsU0FBUztnRUFDNURDLE1BQU07Z0VBQ05DLFFBQVE7NERBQ1Y7NERBQUc7Ozs7Ozs7a0VBSUwsOERBQUMxQzt3REFBS0wsV0FBVyxzREFLaEIsT0FKQ3VCLFFBQVEzQixNQUFNLEtBQUssY0FBYyxzRUFDakMyQixRQUFRM0IsTUFBTSxLQUFLLGFBQWEsa0VBQ2hDMkIsUUFBUTNCLE1BQU0sS0FBSyxnQkFBZ0IsMEVBQ25DO2tFQUVDMkIsUUFBUTNCLE1BQU0sS0FBSyxjQUFjLGNBQ2pDMkIsUUFBUTNCLE1BQU0sS0FBSyxhQUFhLGFBQ2hDMkIsUUFBUTNCLE1BQU0sS0FBSyxnQkFBZ0IsZ0JBQ25DOzs7Ozs7a0VBSUgsOERBQUNLO3dEQUFJRCxXQUFVOzs0REFBdUQ7NERBQzlEdUIsRUFBQUEsaUJBQUFBLFFBQVFqQixJQUFJLGNBQVppQixxQ0FBQUEsZUFBY2hCLElBQUksS0FBSTs7Ozs7Ozs7Ozs7OzswREFLaEMsOERBQUNOO2dEQUFJRCxXQUFVOztrRUFFYiw4REFBQ2tDO3dEQUNDbEMsV0FBVTt3REFDVkcsU0FBUyxJQUFNeEQsT0FBT29CLElBQUksQ0FBQyxhQUF3QixPQUFYd0QsUUFBUXhDLEVBQUU7a0VBRWpEd0MsUUFBUUssTUFBTTs7Ozs7O29EQUloQkwsUUFBUVksU0FBUyxrQkFDaEIsOERBQUNsQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNDO2dFQUNDRCxXQUFVO2dFQUNWb0MsT0FBTztvRUFBRUMsaUJBQWlCZCxRQUFRWSxTQUFTLENBQUNHLEtBQUs7Z0VBQUM7Ozs7OzswRUFFcEQsOERBQUNqQztnRUFBS0wsV0FBVTswRUFDYnVCLFFBQVFZLFNBQVMsQ0FBQ2QsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQU9qQyw4REFBQ3BCO2dEQUFJRCxXQUFVOzBEQUNadUIsUUFBUUMsU0FBUyxpQkFDaEIsOERBQUNDO29EQUNDQyxLQUFLSCxRQUFRQyxTQUFTO29EQUN0QkcsS0FBS0osUUFBUUssTUFBTTtvREFDbkI1QixXQUFVOzs7Ozt5RUFHWiw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ2IsNEVBQUNuRSxrTkFBU0E7d0RBQUNtRSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBEQU0zQiw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUViLDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNRO2dFQUNDTCxTQUFTLElBQU14RCxPQUFPb0IsSUFBSSxDQUFDLGFBQXdCLE9BQVh3RCxRQUFReEMsRUFBRTtnRUFDbERpQixXQUFVO2dFQUNWdUMsT0FBTTswRUFFTiw0RUFBQzVHLGtOQUFZQTtvRUFBQ3FFLFdBQVU7Ozs7Ozs7Ozs7OzBFQUUxQiw4REFBQ1E7Z0VBQ0NMLFNBQVMsSUFBTXhELE9BQU9vQixJQUFJLENBQUMsYUFBd0IsT0FBWHdELFFBQVF4QyxFQUFFLEVBQUM7Z0VBQ25EaUIsV0FBVTtnRUFDVnVDLE9BQU07MEVBRU4sNEVBQUNqSCxrTkFBSUE7b0VBQUMwRSxXQUFVOzs7Ozs7Ozs7OzswRUFFbEIsOERBQUNRO2dFQUNDTCxTQUFTLElBQU1yQixhQUFheUMsUUFBUXhDLEVBQUU7Z0VBQ3RDaUIsV0FBVTtnRUFDVnVDLE9BQU07MEVBRU4sNEVBQUNoSCxrTkFBTUE7b0VBQUN5RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrRUFLdEIsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ0M7Z0VBQUlELFdBQVU7O2tGQUNiLDhEQUFDbkUsa05BQVNBO3dFQUFDbUUsV0FBVTs7Ozs7O2tGQUNyQiw4REFBQ0s7a0ZBQU1rQixRQUFRQyxTQUFTLEdBQUcsSUFBSTs7Ozs7Ozs7Ozs7OzBFQUVqQyw4REFBQ3ZCO2dFQUFJRCxXQUFVOztrRkFDYiw4REFBQ2xFLGtOQUFJQTt3RUFBQ2tFLFdBQVU7Ozs7OztrRkFDaEIsOERBQUNLO2tGQUFLOzs7Ozs7Ozs7Ozs7MEVBRVIsOERBQUNKO2dFQUFJRCxXQUFVOztrRkFDYiw4REFBQ2pFLGtOQUFHQTt3RUFBQ2lFLFdBQVU7Ozs7OztrRkFDZiw4REFBQ0s7a0ZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FPYmtCLFFBQVFpQixTQUFTLElBQUlqQixRQUFRaUIsU0FBUyxDQUFDbEIsTUFBTSxHQUFHLG1CQUMvQyw4REFBQ3JCO3dDQUFJRCxXQUFVOzswREFFYiw4REFBQ0M7Z0RBQ0NELFdBQVU7Z0RBQ1ZHLFNBQVMsSUFBTWQsZ0JBQWdCa0MsUUFBUXhDLEVBQUU7MERBRXpDLDRFQUFDa0I7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDQzs0REFBSUQsV0FBVTs7OEVBQ2IsOERBQUM3RCxrTkFBR0E7b0VBQUM2RCxXQUFVOzs7Ozs7OEVBQ2YsOERBQUNLO29FQUFLTCxXQUFVOzt3RUFBdUQ7d0VBQ3REdUIsUUFBUWlCLFNBQVMsQ0FBQ2xCLE1BQU07d0VBQUM7Ozs7Ozs7Ozs7Ozs7c0VBRzVDLDhEQUFDckI7NERBQUlELFdBQVU7OzhFQUNiLDhEQUFDQztvRUFBSUQsV0FBVTs4RUFDWnVCLFFBQVFpQixTQUFTLENBQUNyQixHQUFHLENBQUMsQ0FBQ3NCLHdCQUN0Qiw4REFBQ3BDOzRFQUVDTCxXQUFXLDhDQUFvRixPQUF0Q0wsc0JBQXNCOEMsUUFBUTdDLE1BQU07c0ZBRTVGNkMsUUFBUUMsTUFBTSxDQUFDckIsTUFBTTsyRUFIakJvQixRQUFRMUQsRUFBRTs7Ozs7Ozs7OztnRUFPcEJyQixpQkFBaUI4QixHQUFHLENBQUMrQixRQUFReEMsRUFBRSxrQkFDOUIsOERBQUMxQyxrTkFBU0E7b0VBQUMyRCxXQUFVOzs7Ozt5RkFFckIsOERBQUM1RCxrTkFBV0E7b0VBQUM0RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0Q0FPOUJ0QyxpQkFBaUI4QixHQUFHLENBQUMrQixRQUFReEMsRUFBRSxtQkFDOUIsOERBQUNrQjtnREFBSUQsV0FBVTswREFDYiw0RUFBQ0M7b0RBQUlELFdBQVU7OERBQ1p1QixRQUFRaUIsU0FBUyxDQUFDckIsR0FBRyxDQUFDLENBQUNzQix3QkFDdEIsOERBQUN4Qzs0REFFQ0QsV0FBVTs7OEVBRVYsOERBQUNDO29FQUFJRCxXQUFVOztzRkFDYiw4REFBQ0M7NEVBQUlELFdBQVU7OzhGQUNiLDhEQUFDSztvRkFBS0wsV0FBVTs4RkFDYnlDLFFBQVFDLE1BQU0sQ0FBQ3JCLE1BQU07Ozs7Ozs4RkFFeEIsOERBQUNoQjtvRkFBS0wsV0FBVyw4Q0FBb0YsT0FBdENMLHNCQUFzQjhDLFFBQVE3QyxNQUFNOzhGQUNoRzZDLFFBQVE3QyxNQUFNOzs7Ozs7Ozs7Ozs7c0ZBR25CLDhEQUFDSzs0RUFBSUQsV0FBVTtzRkFDWixJQUFJNkIsS0FBS1ksUUFBUVgsU0FBUyxFQUFFQyxrQkFBa0IsQ0FBQyxTQUFTO2dGQUN2REMsS0FBSztnRkFDTEMsT0FBTztnRkFDUGEsTUFBTTtnRkFDTkMsUUFBUTs0RUFDVjs7Ozs7Ozs7Ozs7OzhFQUlKLDhEQUFDOUM7b0VBQUlELFdBQVU7O3dFQUNaeUMsUUFBUU8sT0FBTyxrQkFDZCw4REFBQy9DOzs4RkFDQyw4REFBQ0k7b0ZBQUtMLFdBQVU7OEZBQStFOzs7Ozs7OEZBQy9GLDhEQUFDMkM7b0ZBQUUzQyxXQUFVOzhGQUNWeUMsUUFBUU8sT0FBTzs7Ozs7Ozs7Ozs7O3NGQUt0Qiw4REFBQy9DOzs4RkFDQyw4REFBQ0k7b0ZBQUtMLFdBQVU7OEZBQStFOzs7Ozs7OEZBQy9GLDhEQUFDMkM7b0ZBQUUzQyxXQUFVOzhGQUNWeUMsUUFBUWIsTUFBTTs7Ozs7Ozs7Ozs7O3dFQUlsQmEsUUFBUVEsU0FBUyxrQkFDaEIsOERBQUNoRDs7OEZBQ0MsOERBQUNJO29GQUFLTCxXQUFVOzhGQUErRTs7Ozs7OzhGQUMvRiw4REFBQzJDO29GQUFFM0MsV0FBVTs4RkFDVnlDLFFBQVFRLFNBQVM7Ozs7Ozs7Ozs7OztzRkFLeEIsOERBQUNoRDs7OEZBQ0MsOERBQUNJO29GQUFLTCxXQUFVOzhGQUErRTs7Ozs7OzhGQUMvRiw4REFBQzJDO29GQUFFM0MsV0FBVTs4RkFDVnlDLFFBQVFTLFNBQVM7Ozs7Ozs7Ozs7OztzRkFJdEIsOERBQUNqRDs0RUFBSUQsV0FBVTs7OEZBQ2IsOERBQUNDO29GQUFJRCxXQUFVOzt3RkFBMkM7d0ZBQ3pDeUMsUUFBUUcsT0FBTyxDQUFDckMsSUFBSTs7Ozs7Ozs4RkFFckMsOERBQUNDO29GQUNDTCxTQUFTLElBQU14RCxPQUFPb0IsSUFBSSxDQUFDLGFBQXdCLE9BQVh3RCxRQUFReEMsRUFBRSxFQUFDO29GQUNuRGlCLFdBQVU7OEZBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkRBOURBeUMsUUFBUTFELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0JBN1VyQndDLFFBQVF4QyxFQUFFOzs7OzsyQ0EwWnRCLDhEQUFDa0I7NEJBQUlELFdBQVU7c0NBQXlEOzs7Ozs7Ozs7OztvQkFPM0VoRCxjQUFjQSxXQUFXbUcsS0FBSyxHQUFHLG1CQUNoQyw4REFBQ2xEO3dCQUFJRCxXQUFVO2tDQUNYLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOzt3Q0FBMkM7d0NBQ3RDaEQsV0FBVzBCLElBQUk7d0NBQUM7d0NBQUsxQixXQUFXbUcsS0FBSzs7Ozs7Ozs4Q0FFekQsOERBQUNsRDtvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNROzRDQUNDTCxTQUFTLElBQU1yQyxlQUFlc0YsS0FBS0MsR0FBRyxDQUFDLEdBQUd4RixjQUFjOzRDQUN4RHlGLFVBQVV6RixnQkFBZ0I7NENBQzFCbUMsV0FBVTtzREFDWDs7Ozs7O3NEQUdELDhEQUFDUTs0Q0FDQ0wsU0FBUyxJQUFNckMsZUFBZXNGLEtBQUtHLEdBQUcsQ0FBQ3ZHLFdBQVdtRyxLQUFLLEVBQUV0RixjQUFjOzRDQUN2RXlGLFVBQVV6RixnQkFBZ0JiLFdBQVdtRyxLQUFLOzRDQUMxQ25ELFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVW5CO0dBcnJCd0J6RDs7UUFDWXpCLHVEQUFVQTtRQUM3QkUsc0RBQVNBOzs7S0FGRnVCIiwic291cmNlcyI6WyJHOlxcREVMIFNVUiBGSU5BTFxccGFuZWwgdW5pZmljYWRvIHZlcnNpb24gMlxcc3JjXFxhcHBcXG5vdGljaWFzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVNlc3Npb24sIHNpZ25PdXQgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBQbHVzLCBTZWFyY2gsIEZpbHRlciwgRWRpdCwgVHJhc2gyLCBFeWUsIENsb2NrLCBDaGVja0NpcmNsZSwgRmlsZVRleHQsIEV4dGVybmFsTGluaywgRG93bmxvYWQsIEltYWdlIGFzIEltYWdlSWNvbiwgRmlsZSwgTWljLCBMb2dPdXQsIFVzZXIsIE5ld3NwYXBlciwgQm90LCBDaGV2cm9uRG93biwgQ2hldnJvblVwIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IFRoZW1lVG9nZ2xlIH0gZnJvbSAnQC9jb21wb25lbnRzL3RoZW1lLXRvZ2dsZSc7XG5cbmludGVyZmFjZSBWZXJzaW9uTm90aWNpYSB7XG4gIGlkOiBudW1iZXI7XG4gIHRpdHVsbzogc3RyaW5nO1xuICBzdWJ0aXR1bG8/OiBzdHJpbmc7XG4gIHZvbGFudGE/OiBzdHJpbmc7XG4gIGNvbnRlbmlkbzogc3RyaW5nO1xuICByZXN1bWVuPzogc3RyaW5nO1xuICBlc3RhZG86IHN0cmluZztcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIGRpYXJpbzoge1xuICAgIGlkOiBudW1iZXI7XG4gICAgbm9tYnJlOiBzdHJpbmc7XG4gICAgZGVzY3JpcGNpb24/OiBzdHJpbmc7XG4gIH07XG4gIHVzdWFyaW86IHtcbiAgICBpZDogbnVtYmVyO1xuICAgIG5hbWU6IHN0cmluZztcbiAgICBlbWFpbDogc3RyaW5nO1xuICB9O1xufVxuXG5pbnRlcmZhY2UgTm90aWNpYSB7XG4gIGlkOiBudW1iZXI7XG4gIHRpdHVsbzogc3RyaW5nO1xuICBzdWJ0aXR1bG8/OiBzdHJpbmc7XG4gIGVzdGFkbzogc3RyaW5nO1xuICBkZXN0YWNhZGE6IGJvb2xlYW47XG4gIHB1YmxpY2FkYTogYm9vbGVhbjtcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIGltYWdlblVybD86IHN0cmluZyB8IG51bGw7XG4gIGNhdGVnb3JpYT86IHtcbiAgICBpZDogbnVtYmVyO1xuICAgIG5vbWJyZTogc3RyaW5nO1xuICAgIGNvbG9yOiBzdHJpbmc7XG4gIH07XG4gIHVzZXI/OiB7XG4gICAgbmFtZTogc3RyaW5nO1xuICB9O1xuICB2ZXJzaW9uZXM/OiBWZXJzaW9uTm90aWNpYVtdO1xufVxuXG5pbnRlcmZhY2UgQ2F0ZWdvcmlhIHtcbiAgaWQ6IG51bWJlcjtcbiAgbm9tYnJlOiBzdHJpbmc7XG4gIGNvbG9yOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBQYWdpbmF0aW9uIHtcbiAgcGFnZTogbnVtYmVyO1xuICBsaW1pdDogbnVtYmVyO1xuICB0b3RhbDogbnVtYmVyO1xuICBwYWdlczogbnVtYmVyO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RpY2lhc1BhZ2UoKSB7XG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiwgc3RhdHVzIH0gPSB1c2VTZXNzaW9uKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBbbm90aWNpYXMsIHNldE5vdGljaWFzXSA9IHVzZVN0YXRlPE5vdGljaWFbXT4oW10pO1xuICBjb25zdCBbY2F0ZWdvcmlhcywgc2V0Q2F0ZWdvcmlhc10gPSB1c2VTdGF0ZTxDYXRlZ29yaWFbXT4oW10pO1xuICBjb25zdCBbcGFnaW5hdGlvbiwgc2V0UGFnaW5hdGlvbl0gPSB1c2VTdGF0ZTxQYWdpbmF0aW9uIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbc2VhcmNoLCBzZXRTZWFyY2hdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbc2VsZWN0ZWRDYXRlZ29yaWEsIHNldFNlbGVjdGVkQ2F0ZWdvcmlhXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkRXN0YWRvLCBzZXRTZWxlY3RlZEVzdGFkb10gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtleHBhbmRlZE5vdGljaWFzLCBzZXRFeHBhbmRlZE5vdGljaWFzXSA9IHVzZVN0YXRlPFNldDxudW1iZXI+PihuZXcgU2V0KCkpO1xuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDEpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHN0YXR1cyA9PT0gJ3VuYXV0aGVudGljYXRlZCcpIHtcbiAgICAgIHJvdXRlci5wdXNoKCcvYXV0aC9zaWduaW4nKTtcbiAgICB9XG4gIH0sIFtzdGF0dXMsIHJvdXRlcl0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNlc3Npb24pIHtcbiAgICAgIGxvYWRDYXRlZ29yaWFzKCk7XG4gICAgICBsb2FkTm90aWNpYXMoKTtcbiAgICB9XG4gIH0sIFtzZXNzaW9uLCBjdXJyZW50UGFnZSwgc2VhcmNoLCBzZWxlY3RlZENhdGVnb3JpYSwgc2VsZWN0ZWRFc3RhZG9dKTtcblxuICBjb25zdCBsb2FkQ2F0ZWdvcmlhcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9jYXRlZ29yaWFzJyk7XG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgc2V0Q2F0ZWdvcmlhcyhkYXRhKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgY2FyZ2FyIGNhdGVnb3LDrWFzOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9hZE5vdGljaWFzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgICAgIHBhZ2U6IGN1cnJlbnRQYWdlLnRvU3RyaW5nKCksXG4gICAgICAgIGxpbWl0OiAnMTAnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChzZWFyY2gpIHBhcmFtcy5hcHBlbmQoJ3NlYXJjaCcsIHNlYXJjaCk7XG4gICAgICBpZiAoc2VsZWN0ZWRDYXRlZ29yaWEpIHBhcmFtcy5hcHBlbmQoJ2NhdGVnb3JpYScsIHNlbGVjdGVkQ2F0ZWdvcmlhKTtcbiAgICAgIGlmIChzZWxlY3RlZEVzdGFkbykgcGFyYW1zLmFwcGVuZCgnZXN0YWRvJywgc2VsZWN0ZWRFc3RhZG8pO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL25vdGljaWFzPyR7cGFyYW1zfWApO1xuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHNldE5vdGljaWFzKGRhdGEubm90aWNpYXMpO1xuICAgICAgICBzZXRQYWdpbmF0aW9uKGRhdGEucGFnaW5hdGlvbik7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGNhcmdhciBub3RpY2lhczonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVEZWxldGUgPSBhc3luYyAoaWQ6IG51bWJlcikgPT4ge1xuICAgIGlmICghY29uZmlybSgnwr9Fc3TDoXMgc2VndXJvIGRlIHF1ZSBxdWllcmVzIGVsaW1pbmFyIGVzdGEgbm90aWNpYT8nKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL25vdGljaWFzLyR7aWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBsb2FkTm90aWNpYXMoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGFsZXJ0KCdFcnJvciBhbCBlbGltaW5hciBsYSBub3RpY2lhJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGVsaW1pbmFyIG5vdGljaWE6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ0Vycm9yIGFsIGVsaW1pbmFyIGxhIG5vdGljaWEnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2lnbk91dCA9IGFzeW5jICgpID0+IHtcbiAgICBhd2FpdCBzaWduT3V0KHsgY2FsbGJhY2tVcmw6ICcvYXV0aC9zaWduaW4nIH0pO1xuICB9O1xuXG4gIGNvbnN0IHRvZ2dsZVZlcnNpb25lcyA9IChub3RpY2lhSWQ6IG51bWJlcikgPT4ge1xuICAgIGNvbnN0IG5ld0V4cGFuZGVkID0gbmV3IFNldChleHBhbmRlZE5vdGljaWFzKTtcbiAgICBpZiAobmV3RXhwYW5kZWQuaGFzKG5vdGljaWFJZCkpIHtcbiAgICAgIG5ld0V4cGFuZGVkLmRlbGV0ZShub3RpY2lhSWQpO1xuICAgIH0gZWxzZSB7XG4gICAgICBuZXdFeHBhbmRlZC5hZGQobm90aWNpYUlkKTtcbiAgICB9XG4gICAgc2V0RXhwYW5kZWROb3RpY2lhcyhuZXdFeHBhbmRlZCk7XG4gIH07XG5cbiAgY29uc3QgZ2V0RXN0YWRvVmVyc2lvbkJhZGdlID0gKGVzdGFkbzogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgYmFkZ2VzID0ge1xuICAgICAgJ0dFTkVSQURBJzogJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgZGFyazpiZy1ibHVlLTkwMCBkYXJrOnRleHQtYmx1ZS0yMDAnLFxuICAgICAgJ0FQUk9CQURBJzogJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCBkYXJrOmJnLWdyZWVuLTkwMCBkYXJrOnRleHQtZ3JlZW4tMjAwJyxcbiAgICAgICdSRUNIQVpBREEnOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAgZGFyazpiZy1yZWQtOTAwIGRhcms6dGV4dC1yZWQtMjAwJyxcbiAgICAgICdFTl9SRVZJU0lPTic6ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCBkYXJrOmJnLXllbGxvdy05MDAgZGFyazp0ZXh0LXllbGxvdy0yMDAnLFxuICAgIH07XG4gICAgcmV0dXJuIGJhZGdlc1tlc3RhZG8gYXMga2V5b2YgdHlwZW9mIGJhZGdlc10gfHwgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAgZGFyazpiZy1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAnO1xuICB9O1xuXG4gIGNvbnN0IGdldEVzdGFkb0NvbG9yID0gKGVzdGFkbzogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChlc3RhZG8pIHtcbiAgICAgIGNhc2UgJ1BVQkxJQ0FEQSc6XG4gICAgICAgIHJldHVybiAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwIGRhcms6YmctZ3JlZW4tOTAwIGRhcms6dGV4dC1ncmVlbi0yMDAnO1xuICAgICAgY2FzZSAnQVBST0JBREEnOlxuICAgICAgICByZXR1cm4gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgZGFyazpiZy1ibHVlLTkwMCBkYXJrOnRleHQtYmx1ZS0yMDAnO1xuICAgICAgY2FzZSAnRU5fUkVWSVNJT04nOlxuICAgICAgICByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwIGRhcms6YmcteWVsbG93LTkwMCBkYXJrOnRleHQteWVsbG93LTIwMCc7XG4gICAgICBjYXNlICdCT1JSQURPUic6XG4gICAgICAgIHJldHVybiAnYmctZ3JheS0yMDAgdGV4dC1ncmF5LTgwMCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ2JnLWdyYXktMjAwIHRleHQtZ3JheS04MDAgZGFyazpiZy1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRFc3RhZG9JY29uID0gKGVzdGFkbzogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChlc3RhZG8pIHtcbiAgICAgIGNhc2UgJ1BVQkxJQ0FEQSc6XG4gICAgICAgIHJldHVybiA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+O1xuICAgICAgY2FzZSAnQVBST0JBREEnOlxuICAgICAgICByZXR1cm4gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPjtcbiAgICAgIGNhc2UgJ0VOX1JFVklTSU9OJzpcbiAgICAgICAgcmV0dXJuIDxDbG9jayBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz47XG4gICAgICBjYXNlICdCT1JSQURPUic6XG4gICAgICAgIHJldHVybiA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+O1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz47XG4gICAgfVxuICB9O1xuXG4gIGlmIChzdGF0dXMgPT09ICdsb2FkaW5nJyB8fCBsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTkwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0zMiB3LTMyIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIGRhcms6Ym9yZGVyLWJsdWUtNDAwXCI+PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKCFzZXNzaW9uKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTkwMFwiPlxuICAgICAgPGhlYWRlciBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHNoYWRvdy1zbSBib3JkZXItYiBkYXJrOmJvcmRlci1ncmF5LTcwMCBzdGlja3kgdG9wLTAgei0xMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHB5LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgY3Vyc29yLXBvaW50ZXJcIiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZCcpfT5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggYmctYmx1ZS02MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPE5ld3NwYXBlciBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJtbC0zIHRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgICAgICAgUGFuZWwgZGUgTm90aWNpYXNcbiAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8VGhlbWVUb2dnbGUgLz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICAgIHtzZXNzaW9uLnVzZXI/Lm5hbWUgfHwgJ1VzdWFyaW8nfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTaWduT3V0fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSByb3VuZGVkLWZ1bGwgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNTAwIGRhcms6aG92ZXI6dGV4dC13aGl0ZSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1ibHVlLTUwMCBkYXJrOmZvY3VzOnJpbmctb2Zmc2V0LWdyYXktODAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxMb2dPdXQgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9oZWFkZXI+XG5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwXCI+TGlzdGEgZGUgTm90aWNpYXM8L2gxPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvbm90aWNpYXMvbnVldmEnKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJvdW5kZWQtbWQgc2hhZG93LXNtIHRleHQtd2hpdGUgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cIi1tbC0xIG1yLTIgaC01IHctNVwiIC8+XG4gICAgICAgICAgICBOdWV2YSBOb3RpY2lhXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBGaWx0ZXJzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTQgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIC10cmFuc2xhdGUteS0xLzIgaC01IHctNSB0ZXh0LWdyYXktNDAwIGRhcms6dGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkJ1c2NhciBwb3IgdMOtdHVsby4uLlwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgcHItNCBweS0yIHctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTIwMCBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRDYXRlZ29yaWF9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZENhdGVnb3JpYShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHAtMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTIwMCBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+VG9kYXMgbGFzIGNhdGVnb3LDrWFzPC9vcHRpb24+XG4gICAgICAgICAgICAgICAge2NhdGVnb3JpYXMubWFwKChjYXQpID0+IChcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjYXQuaWR9IHZhbHVlPXtjYXQuaWQudG9TdHJpbmcoKX0+XG4gICAgICAgICAgICAgICAgICAgIHtjYXQubm9tYnJlfVxuICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkRXN0YWRvfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VsZWN0ZWRFc3RhZG8oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwLTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGJnLXdoaXRlIGRhcms6YmctZ3JheS03MDAgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtZ3JheS0yMDAgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlRvZG9zIGxvcyBlc3RhZG9zPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkJPUlJBRE9SXCI+Qm9ycmFkb3I8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRU5fUkVWSVNJT05cIj5FbiBSZXZpc2nDs248L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQVBST0JBREFcIj5BcHJvYmFkYTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQVUJMSUNBREFcIj5QdWJsaWNhZGE8L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIHAtMiBiZy1ncmF5LTIwMCBkYXJrOmJnLWdyYXktNzAwIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMjAwIHJvdW5kZWQtbWQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaG92ZXI6YmctZ3JheS0zMDAgZGFyazpob3ZlcjpiZy1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8RmlsdGVyIGNsYXNzTmFtZT1cIm1yLTIgaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgIEZpbHRyb3MgQXZhbnphZG9zXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE5ld3MgTGlzdCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTMgbWQ6c3BhY2UteS00XCI+XG4gICAgICAgICAge25vdGljaWFzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICBub3RpY2lhcy5tYXAoKG5vdGljaWEpID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e25vdGljaWEuaWR9IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNzAwIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1zaGFkb3cgZHVyYXRpb24tMjAwXCI+XG4gICAgICAgICAgICAgICAgey8qIE1vYmlsZSBMYXlvdXQgKDwgNzY4cHgpICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmxvY2sgbWQ6aGlkZGVuIHAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtMyBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBJbWFnZW4gZW4gbcOzdmlsICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgaC0xNiBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAge25vdGljaWEuaW1hZ2VuVXJsID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e25vdGljaWEuaW1hZ2VuVXJsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e25vdGljaWEudGl0dWxvfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBiZy1ncmF5LTEwMCBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEltYWdlSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtZ3JheS00MDAgZGFyazp0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBDb250ZW5pZG8gcHJpbmNpcGFsIGVuIG3Ds3ZpbCAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBGZWNoYSB5IGVzdGFkbyBlbiBtw7N2aWwgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUobm90aWNpYS5jcmVhdGVkQXQpLnRvTG9jYWxlRGF0ZVN0cmluZygnZXMtRVMnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF5OiAnMi1kaWdpdCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9udGg6ICdzaG9ydCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTAuNSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBub3RpY2lhLmVzdGFkbyA9PT0gJ1BVQkxJQ0FEQScgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwIGRhcms6YmctZ3JlZW4tOTAwIGRhcms6dGV4dC1ncmVlbi0yMDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbm90aWNpYS5lc3RhZG8gPT09ICdBUFJPQkFEQScgPyAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCBkYXJrOmJnLWJsdWUtOTAwIGRhcms6dGV4dC1ibHVlLTIwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICBub3RpY2lhLmVzdGFkbyA9PT0gJ0VOX1JFVklTSU9OJyA/ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCBkYXJrOmJnLXllbGxvdy05MDAgZGFyazp0ZXh0LXllbGxvdy0yMDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAgZGFyazpiZy1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtub3RpY2lhLmVzdGFkbyA9PT0gJ1BVQkxJQ0FEQScgPyAnUHVibGljYWRvJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICBub3RpY2lhLmVzdGFkbyA9PT0gJ0FQUk9CQURBJyA/ICdBcHJvYmFkYScgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgbm90aWNpYS5lc3RhZG8gPT09ICdFTl9SRVZJU0lPTicgPyAnRW4gUmV2aXNpw7NuJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAnQm9ycmFkb3InfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIFTDrXR1bG8gZW4gbcOzdmlsICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxoM1xuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwIGhvdmVyOnRleHQtYmx1ZS02MDAgZGFyazpob3Zlcjp0ZXh0LWJsdWUtNDAwIGN1cnNvci1wb2ludGVyIGxpbmUtY2xhbXAtMiBtYi0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKGAvbm90aWNpYXMvJHtub3RpY2lhLmlkfWApfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtub3RpY2lhLnRpdHVsb31cbiAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIEF1dG9yIGVuIG3Ds3ZpbCAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFBvcjoge25vdGljaWEudXNlcj8ubmFtZSB8fCAnTi9BJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIENhdGVnb3LDrWEgZW4gbcOzdmlsICovfVxuICAgICAgICAgICAgICAgICAge25vdGljaWEuY2F0ZWdvcmlhICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMyBoLTMgcm91bmRlZC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IG5vdGljaWEuY2F0ZWdvcmlhLmNvbG9yIH19XG4gICAgICAgICAgICAgICAgICAgICAgPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtub3RpY2lhLmNhdGVnb3JpYS5ub21icmV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBCb3RvbmVzIGVuIG3Ds3ZpbCAtIG3DoXMgZ3JhbmRlcyBwYXJhIHRvdWNoICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaChgL25vdGljaWFzLyR7bm90aWNpYS5pZH1gKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC00IHB5LTIgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZSB0ZXh0LXNtIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiVmVyIG5vdGljaWFcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxFeHRlcm5hbExpbmsgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5WZXI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goYC9ub3RpY2lhcy8ke25vdGljaWEuaWR9L2VkaXRhcmApfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB4LTQgcHktMiBiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNzAwIHRleHQtd2hpdGUgdGV4dC1zbSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkVkaXRhciBub3RpY2lhXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RWRpdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkVkaXRhcjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGUobm90aWNpYS5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgYmctcmVkLTYwMCBob3ZlcjpiZy1yZWQtNzAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFbGltaW5hciBub3RpY2lhXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogRXN0YWTDrXN0aWNhcyBlbiBtw7N2aWwgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHRleHQteHMgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEltYWdlSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntub3RpY2lhLmltYWdlblVybCA/IDEgOiAwfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEZpbGUgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj4wPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TWljIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+MDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFZlcnNpb25lcyBkZSBJQSBlbiBtw7N2aWwgLSBTb2xvIG1vc3RyYXIgc2kgdGllbmUgdmVyc2lvbmVzICovfVxuICAgICAgICAgICAgICAgICAge25vdGljaWEudmVyc2lvbmVzICYmIG5vdGljaWEudmVyc2lvbmVzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTMgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHB0LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogSGVhZGVyIGRlIHZlcnNpb25lcyAtIGNsaWNrZWFibGUgcGFyYSBleHBhbmRpci9jb2xhcHNhciAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdG9nZ2xlVmVyc2lvbmVzKG5vdGljaWEuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCb3QgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBWZXJzaW9uZXMgSUEgKHtub3RpY2lhLnZlcnNpb25lcy5sZW5ndGh9KVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtleHBhbmRlZE5vdGljaWFzLmhhcyhub3RpY2lhLmlkKSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25VcCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIENvbnRlbmlkbyBleHBhbmRpYmxlIGRlIHZlcnNpb25lcyBlbiBtw7N2aWwgKi99XG4gICAgICAgICAgICAgICAgICAgICAge2V4cGFuZGVkTm90aWNpYXMuaGFzKG5vdGljaWEuaWQpICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMyBzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge25vdGljaWEudmVyc2lvbmVzLm1hcCgodmVyc2lvbikgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17dmVyc2lvbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMyBiZy1ncmF5LTUwIGRhcms6YmctZ3JheS03MDAgcm91bmRlZC1sZyBib3JkZXItbC00IGJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlcnNpb24uZGlhcmlvLm5vbWJyZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTEgdGV4dC14cyBmb250LW1lZGl1bSByb3VuZGVkLWZ1bGwgJHtnZXRFc3RhZG9WZXJzaW9uQmFkZ2UodmVyc2lvbi5lc3RhZG8pfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2ZXJzaW9uLmVzdGFkb31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbGluZS1jbGFtcC0yIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlcnNpb24udGl0dWxvfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXhzIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlBvcjoge3ZlcnNpb24udXN1YXJpby5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKGAvbm90aWNpYXMvJHtub3RpY2lhLmlkfS9yZXZpc2lvbmApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwIGhvdmVyOnRleHQtYmx1ZS04MDAgZGFyazpob3Zlcjp0ZXh0LWJsdWUtMzAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZlciDihpJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogVGFibGV0ICYgRGVza3RvcCBMYXlvdXQgKD49IDc2OHB4KSAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpmbGV4IGl0ZW1zLWNlbnRlciBwLTQgbGc6cC02XCI+XG4gICAgICAgICAgICAgICAgICB7LyogQ29sdW1uYSBpenF1aWVyZGEgLSBJbmZvcm1hY2nDs24gdGVtcG9yYWwgeSBhdXRvcsOtYSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1zdGFydCBzcGFjZS15LTIgdy0yOCBsZzp3LTMyIHhsOnctMzYgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICB7LyogRMOtYSB5IGhvcmEgZGUgY3JlYWNpw7NuICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUobm90aWNpYS5jcmVhdGVkQXQpLnRvTG9jYWxlRGF0ZVN0cmluZygnZXMtRVMnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBkYXk6ICcyLWRpZ2l0JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIG1vbnRoOiAnc2hvcnQnXG4gICAgICAgICAgICAgICAgICAgICAgfSl9LCB7bmV3IERhdGUobm90aWNpYS5jcmVhdGVkQXQpLnRvTG9jYWxlVGltZVN0cmluZygnZXMtRVMnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW51dGU6ICcyLWRpZ2l0J1xuICAgICAgICAgICAgICAgICAgICAgIH0pfSBoc1xuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogRXN0YWRvICovfVxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIGxnOnB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgIG5vdGljaWEuZXN0YWRvID09PSAnUFVCTElDQURBJyA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAgZGFyazpiZy1ncmVlbi05MDAgZGFyazp0ZXh0LWdyZWVuLTIwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgIG5vdGljaWEuZXN0YWRvID09PSAnQVBST0JBREEnID8gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgZGFyazpiZy1ibHVlLTkwMCBkYXJrOnRleHQtYmx1ZS0yMDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICBub3RpY2lhLmVzdGFkbyA9PT0gJ0VOX1JFVklTSU9OJyA/ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCBkYXJrOmJnLXllbGxvdy05MDAgZGFyazp0ZXh0LXllbGxvdy0yMDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHtub3RpY2lhLmVzdGFkbyA9PT0gJ1BVQkxJQ0FEQScgPyAnUHVibGljYWRvJyA6XG4gICAgICAgICAgICAgICAgICAgICAgIG5vdGljaWEuZXN0YWRvID09PSAnQVBST0JBREEnID8gJ0Fwcm9iYWRhJyA6XG4gICAgICAgICAgICAgICAgICAgICAgIG5vdGljaWEuZXN0YWRvID09PSAnRU5fUkVWSVNJT04nID8gJ0VuIFJldmlzacOzbicgOlxuICAgICAgICAgICAgICAgICAgICAgICAnQm9ycmFkb3InfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIE5vbWJyZSBkZWwgYXV0b3IgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgIFBvcjoge25vdGljaWEudXNlcj8ubmFtZSB8fCAnTi9BJ31cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIENvbnRlbmlkbyBwcmluY2lwYWwgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBweC0zIGxnOnB4LTQgeGw6cHgtNiBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBUw610dWxvICovfVxuICAgICAgICAgICAgICAgICAgICA8aDNcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtIGxnOnRleHQtYmFzZSB4bDp0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtZ3JheS0xMDAgaG92ZXI6dGV4dC1ibHVlLTYwMCBkYXJrOmhvdmVyOnRleHQtYmx1ZS00MDAgY3Vyc29yLXBvaW50ZXIgbGluZS1jbGFtcC0yIG1iLTJcIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKGAvbm90aWNpYXMvJHtub3RpY2lhLmlkfWApfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge25vdGljaWEudGl0dWxvfVxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBDYXRlZ29yw61hICovfVxuICAgICAgICAgICAgICAgICAgICB7bm90aWNpYS5jYXRlZ29yaWEgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMyBoLTMgcm91bmRlZC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogbm90aWNpYS5jYXRlZ29yaWEuY29sb3IgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge25vdGljaWEuY2F0ZWdvcmlhLm5vbWJyZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogSW1hZ2VuICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIwIGgtMTYgbGc6dy0yNCBsZzpoLTIwIHhsOnctMjggeGw6aC0yMiBmbGV4LXNocmluay0wIG1sLTMgbGc6bWwtNFwiPlxuICAgICAgICAgICAgICAgICAgICB7bm90aWNpYS5pbWFnZW5VcmwgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtub3RpY2lhLmltYWdlblVybH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17bm90aWNpYS50aXR1bG99XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBiZy1ncmF5LTEwMCBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZUljb24gY2xhc3NOYW1lPVwiaC02IHctNiBsZzpoLTggbGc6dy04IHRleHQtZ3JheS00MDAgZGFyazp0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogQ29sdW1uYSBkZXJlY2hhIC0gQm90b25lcyB5IGVzdGFkw61zdGljYXMgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIHNwYWNlLXktMyBtbC0zIGxnOm1sLTQgeGw6bWwtNlwiPlxuICAgICAgICAgICAgICAgICAgICB7LyogQm90b25lcyBkZSBhY2Npw7NuIGhvcml6b250YWxlcyAtIHNvbG8gaWNvbm9zICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBsZzpzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaChgL25vdGljaWFzLyR7bm90aWNpYS5pZH1gKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiBsZzpwLTIuNSBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiVmVyIG5vdGljaWFcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxFeHRlcm5hbExpbmsgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goYC9ub3RpY2lhcy8ke25vdGljaWEuaWR9L2VkaXRhcmApfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGxnOnAtMi41IGJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkVkaXRhciBub3RpY2lhXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RWRpdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGUobm90aWNpYS5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgbGc6cC0yLjUgYmctcmVkLTYwMCBob3ZlcjpiZy1yZWQtNzAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFbGltaW5hciBub3RpY2lhXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogRXN0YWTDrXN0aWNhcyBkZWJham8gZGUgbG9zIGJvdG9uZXMgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgc3BhY2UteS0xIHRleHQteHMgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgcHQtMiBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEltYWdlSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntub3RpY2lhLmltYWdlblVybCA/IDEgOiAwfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEZpbGUgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj4wPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TWljIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+MDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBWZXJzaW9uZXMgZGUgSUEgLSBTb2xvIG1vc3RyYXIgc2kgdGllbmUgdmVyc2lvbmVzICovfVxuICAgICAgICAgICAgICAgIHtub3RpY2lhLnZlcnNpb25lcyAmJiBub3RpY2lhLnZlcnNpb25lcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBIZWFkZXIgZGUgdmVyc2lvbmVzIC0gY2xpY2tlYWJsZSBwYXJhIGV4cGFuZGlyL2NvbGFwc2FyICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0zIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTcwMCBjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMCBkYXJrOmhvdmVyOmJnLWdyYXktNjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB0b2dnbGVWZXJzaW9uZXMobm90aWNpYS5pZCl9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJvdCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZlcnNpb25lcyBJQSAoe25vdGljaWEudmVyc2lvbmVzLmxlbmd0aH0pXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtub3RpY2lhLnZlcnNpb25lcy5tYXAoKHZlcnNpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17dmVyc2lvbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMiBweS0xIHRleHQteHMgZm9udC1tZWRpdW0gcm91bmRlZC1mdWxsICR7Z2V0RXN0YWRvVmVyc2lvbkJhZGdlKHZlcnNpb24uZXN0YWRvKX1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVyc2lvbi5kaWFyaW8ubm9tYnJlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2V4cGFuZGVkTm90aWNpYXMuaGFzKG5vdGljaWEuaWQpID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uVXAgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogQ29udGVuaWRvIGV4cGFuZGlibGUgZGUgdmVyc2lvbmVzICovfVxuICAgICAgICAgICAgICAgICAgICB7ZXhwYW5kZWROb3RpY2lhcy5oYXMobm90aWNpYS5pZCkgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS0zIGJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bm90aWNpYS52ZXJzaW9uZXMubWFwKCh2ZXJzaW9uKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt2ZXJzaW9uLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWxnIGJvcmRlci1sLTQgYm9yZGVyLWdyZWVuLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlcnNpb24uZGlhcmlvLm5vbWJyZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHRleHQteHMgZm9udC1tZWRpdW0gcm91bmRlZC1mdWxsICR7Z2V0RXN0YWRvVmVyc2lvbkJhZGdlKHZlcnNpb24uZXN0YWRvKX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2ZXJzaW9uLmVzdGFkb31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUodmVyc2lvbi5jcmVhdGVkQXQpLnRvTG9jYWxlRGF0ZVN0cmluZygnZXMtRVMnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXk6ICcyLWRpZ2l0JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiAnMi1kaWdpdCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlcnNpb24udm9sYW50YSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVcIj5Wb2xhbnRhPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTYwMCBkYXJrOnRleHQtZ3JlZW4tNDAwIGZvbnQtbWVkaXVtIHVwcGVyY2FzZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVyc2lvbi52b2xhbnRhfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZVwiPlTDrXR1bG88L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwIGxpbmUtY2xhbXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlcnNpb24udGl0dWxvfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlcnNpb24uc3VidGl0dWxvICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZVwiPlN1YnTDrXR1bG88L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGxpbmUtY2xhbXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVyc2lvbi5zdWJ0aXR1bG99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlXCI+Q29udGVuaWRvPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbGluZS1jbGFtcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVyc2lvbi5jb250ZW5pZG99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC0yIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgR2VuZXJhZG8gcG9yOiB7dmVyc2lvbi51c3VhcmlvLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goYC9ub3RpY2lhcy8ke25vdGljaWEuaWR9L3JldmlzaW9uYCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwIGhvdmVyOnRleHQtYmx1ZS04MDAgZGFyazpob3Zlcjp0ZXh0LWJsdWUtMzAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBWZXIgZGV0YWxsZXMg4oaSXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpXG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS04IHRleHQtY2VudGVyIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgIE5vIHNlIGVuY29udHJhcm9uIG5vdGljaWFzXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUGFnaW5hdGlvbiAqL31cbiAgICAgICAge3BhZ2luYXRpb24gJiYgcGFnaW5hdGlvbi5wYWdlcyA+IDEgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBtZDptdC02IGJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCBweC00IG1kOnB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgTW9zdHJhbmRvIHDDoWdpbmEge3BhZ2luYXRpb24ucGFnZX0gZGUge3BhZ2luYXRpb24ucGFnZXN9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDdXJyZW50UGFnZShNYXRoLm1heCgxLCBjdXJyZW50UGFnZSAtIDEpKX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2N1cnJlbnRQYWdlID09PSAxfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTMwMCBiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwIGRhcms6aG92ZXI6YmctZ3JheS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBBbnRlcmlvclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRQYWdlKE1hdGgubWluKHBhZ2luYXRpb24ucGFnZXMsIGN1cnJlbnRQYWdlICsgMSkpfVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17Y3VycmVudFBhZ2UgPT09IHBhZ2luYXRpb24ucGFnZXN9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktMzAwIGJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTAgZGFyazpob3ZlcjpiZy1ncmF5LTcwMCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIFNpZ3VpZW50ZVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgIDwvbWFpbj5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsidXNlU2Vzc2lvbiIsInNpZ25PdXQiLCJ1c2VSb3V0ZXIiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIlBsdXMiLCJTZWFyY2giLCJGaWx0ZXIiLCJFZGl0IiwiVHJhc2gyIiwiQ2xvY2siLCJDaGVja0NpcmNsZSIsIkZpbGVUZXh0IiwiRXh0ZXJuYWxMaW5rIiwiSW1hZ2UiLCJJbWFnZUljb24iLCJGaWxlIiwiTWljIiwiTG9nT3V0IiwiVXNlciIsIk5ld3NwYXBlciIsIkJvdCIsIkNoZXZyb25Eb3duIiwiQ2hldnJvblVwIiwiVGhlbWVUb2dnbGUiLCJOb3RpY2lhc1BhZ2UiLCJzZXNzaW9uIiwiZGF0YSIsInN0YXR1cyIsInJvdXRlciIsIm5vdGljaWFzIiwic2V0Tm90aWNpYXMiLCJjYXRlZ29yaWFzIiwic2V0Q2F0ZWdvcmlhcyIsInBhZ2luYXRpb24iLCJzZXRQYWdpbmF0aW9uIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzZWFyY2giLCJzZXRTZWFyY2giLCJzZWxlY3RlZENhdGVnb3JpYSIsInNldFNlbGVjdGVkQ2F0ZWdvcmlhIiwic2VsZWN0ZWRFc3RhZG8iLCJzZXRTZWxlY3RlZEVzdGFkbyIsImV4cGFuZGVkTm90aWNpYXMiLCJzZXRFeHBhbmRlZE5vdGljaWFzIiwiU2V0IiwiY3VycmVudFBhZ2UiLCJzZXRDdXJyZW50UGFnZSIsInB1c2giLCJsb2FkQ2F0ZWdvcmlhcyIsImxvYWROb3RpY2lhcyIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImpzb24iLCJlcnJvciIsImNvbnNvbGUiLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJwYWdlIiwidG9TdHJpbmciLCJsaW1pdCIsImFwcGVuZCIsImhhbmRsZURlbGV0ZSIsImlkIiwiY29uZmlybSIsIm1ldGhvZCIsImFsZXJ0IiwiaGFuZGxlU2lnbk91dCIsImNhbGxiYWNrVXJsIiwidG9nZ2xlVmVyc2lvbmVzIiwibm90aWNpYUlkIiwibmV3RXhwYW5kZWQiLCJoYXMiLCJkZWxldGUiLCJhZGQiLCJnZXRFc3RhZG9WZXJzaW9uQmFkZ2UiLCJlc3RhZG8iLCJiYWRnZXMiLCJnZXRFc3RhZG9Db2xvciIsImdldEVzdGFkb0ljb24iLCJjbGFzc05hbWUiLCJkaXYiLCJoZWFkZXIiLCJvbkNsaWNrIiwiaDEiLCJzcGFuIiwidXNlciIsIm5hbWUiLCJidXR0b24iLCJtYWluIiwiaW5wdXQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInNlbGVjdCIsIm9wdGlvbiIsIm1hcCIsImNhdCIsIm5vbWJyZSIsImxlbmd0aCIsIm5vdGljaWEiLCJpbWFnZW5VcmwiLCJpbWciLCJzcmMiLCJhbHQiLCJ0aXR1bG8iLCJEYXRlIiwiY3JlYXRlZEF0IiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiZGF5IiwibW9udGgiLCJoMyIsImNhdGVnb3JpYSIsInN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwiY29sb3IiLCJ0aXRsZSIsInZlcnNpb25lcyIsInZlcnNpb24iLCJkaWFyaW8iLCJwIiwidXN1YXJpbyIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJ2b2xhbnRhIiwic3VidGl0dWxvIiwiY29udGVuaWRvIiwicGFnZXMiLCJNYXRoIiwibWF4IiwiZGlzYWJsZWQiLCJtaW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/noticias/page.tsx\n"));

/***/ })

});