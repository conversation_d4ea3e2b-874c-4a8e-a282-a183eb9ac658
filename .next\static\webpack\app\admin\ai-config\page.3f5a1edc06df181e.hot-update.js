"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/ai-config/page",{

/***/ "(app-pages-browser)/./src/app/admin/ai-config/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/ai-config/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Eye,EyeOff,Loader2,Save,Settings,TestTube,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OPENAI_MODELS = [\n    'gpt-3.5-turbo',\n    'gpt-3.5-turbo-16k',\n    'gpt-4',\n    'gpt-4-turbo',\n    'gpt-4-turbo-preview',\n    'gpt-4o',\n    'gpt-4o-mini',\n    'gpt-4o-2024-11-20',\n    'gpt-4o-2024-08-06',\n    'gpt-4o-2024-05-13',\n    'o1-preview',\n    'o1-mini'\n];\nconst GEMINI_MODELS = [\n    'gemini-1.5-flash',\n    'gemini-1.5-flash-8b',\n    'gemini-1.5-pro',\n    'gemini-2.0-flash-exp',\n    'gemini-2.0-flash-thinking-exp-1219',\n    'gemini-exp-1114',\n    'gemini-exp-1121',\n    'gemini-exp-1206',\n    'learnlm-1.5-pro-experimental'\n];\nfunction AIConfigPage() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        openaiModel: 'gpt-3.5-turbo',\n        openaiMaxTokens: 2000,\n        openaiTemperature: 0.7,\n        geminiModel: 'gemini-pro',\n        geminiMaxTokens: 2000,\n        geminiTemperature: 0.7,\n        defaultProvider: 'OPENAI',\n        isActive: true\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testing, setTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showApiKeys, setShowApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Verificar autenticación y permisos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIConfigPage.useEffect\": ()=>{\n            if (status === 'loading') return;\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                router.push('/auth/signin');\n                return;\n            }\n            if (session.user.role !== 'ADMIN') {\n                router.push('/admin');\n                return;\n            }\n            loadConfig();\n        }\n    }[\"AIConfigPage.useEffect\"], [\n        session,\n        status,\n        router\n    ]);\n    const loadConfig = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/admin/ai-config');\n            if (response.ok) {\n                const data = await response.json();\n                setConfig(data);\n            } else {\n                console.error('Error loading AI config');\n            }\n        } catch (error) {\n            console.error('Error loading AI config:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveConfig = async ()=>{\n        try {\n            setSaving(true);\n            const response = await fetch('/api/admin/ai-config', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(config)\n            });\n            if (response.ok) {\n                const savedConfig = await response.json();\n                setConfig(savedConfig);\n                alert('✅ Configuración guardada exitosamente');\n            } else {\n                const error = await response.json();\n                alert(\"❌ Error: \".concat(error.error));\n            }\n        } catch (error) {\n            console.error('Error saving config:', error);\n            alert('❌ Error al guardar la configuración');\n        } finally{\n            setSaving(false);\n        }\n    };\n    const testConnection = async (provider)=>{\n        try {\n            setTesting(provider);\n            const apiKey = provider === 'OPENAI' ? config.openaiApiKey : config.geminiApiKey;\n            const response = await fetch('/api/admin/ai-config/test', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    provider,\n                    apiKey: apiKey === '***' ? undefined : apiKey\n                })\n            });\n            if (response.ok) {\n                const result = await response.json();\n                setConnectionStatus((prev)=>({\n                        ...prev,\n                        [provider]: result\n                    }));\n            } else {\n                const error = await response.json();\n                setConnectionStatus((prev)=>({\n                        ...prev,\n                        [provider]: {\n                            provider,\n                            isConnected: false,\n                            error: error.error,\n                            timestamp: new Date().toISOString()\n                        }\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error testing \".concat(provider, \" connection:\"), error);\n            setConnectionStatus((prev)=>({\n                    ...prev,\n                    [provider]: {\n                        provider,\n                        isConnected: false,\n                        error: 'Error de conexión',\n                        timestamp: new Date().toISOString()\n                    }\n                }));\n        } finally{\n            setTesting(null);\n        }\n    };\n    const toggleApiKeyVisibility = (provider)=>{\n        setShowApiKeys((prev)=>({\n                ...prev,\n                [provider]: !prev[provider]\n            }));\n    };\n    const renderConnectionStatus = (provider)=>{\n        const status = connectionStatus[provider];\n        if (!status) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 text-sm \".concat(status.isConnected ? 'text-green-600' : 'text-red-600'),\n            children: [\n                status.isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: status.isConnected ? 'Conectado' : \"Error: \".concat(status.error)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                status.timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-500\",\n                    children: [\n                        \"(\",\n                        new Date(status.timestamp).toLocaleTimeString(),\n                        \")\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Cargando configuraci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Configuraci\\xf3n de IA\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Configura los proveedores de inteligencia artificial para la reescritura de noticias.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Configuraci\\xf3n Global\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Proveedor por Defecto\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: config.defaultProvider,\n                                                onChange: (e)=>setConfig((prev)=>({\n                                                            ...prev,\n                                                            defaultProvider: e.target.value\n                                                        })),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"OPENAI\",\n                                                        children: \"OpenAI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"GEMINI\",\n                                                        children: \"Google Gemini\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"Proveedor utilizado por defecto para todos los diarios\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                    children: \"OpenAI Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>testConnection('OPENAI'),\n                                            disabled: testing === 'OPENAI',\n                                            className: \"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50 rounded-md transition-colors\",\n                                            children: [\n                                                testing === 'OPENAI' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Probar Conexi\\xf3n\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                renderConnectionStatus('OPENAI'),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"API Key\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: showApiKeys.openai ? 'text' : 'password',\n                                                            value: config.openaiApiKey || '',\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        openaiApiKey: e.target.value\n                                                                    })),\n                                                            placeholder: \"sk-...\",\n                                                            className: \"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>toggleApiKeyVisibility('openai'),\n                                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                            children: showApiKeys.openai ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Modelo\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: config.openaiModel,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                openaiModel: e.target.value\n                                                            })),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    children: OPENAI_MODELS.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: model,\n                                                            children: model\n                                                        }, model, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Max Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.openaiMaxTokens,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                openaiMaxTokens: parseInt(e.target.value) || 2000\n                                                            })),\n                                                    min: \"100\",\n                                                    max: \"4000\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Temperature\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.openaiTemperature,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                openaiTemperature: parseFloat(e.target.value) || 0.7\n                                                            })),\n                                                    min: \"0\",\n                                                    max: \"2\",\n                                                    step: \"0.1\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                    children: \"Google Gemini Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>testConnection('GEMINI'),\n                                            disabled: testing === 'GEMINI',\n                                            className: \"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-purple-700 bg-purple-100 hover:bg-purple-200 disabled:opacity-50 rounded-md transition-colors\",\n                                            children: [\n                                                testing === 'GEMINI' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Probar Conexi\\xf3n\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this),\n                                renderConnectionStatus('GEMINI'),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"API Key\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: showApiKeys.gemini ? 'text' : 'password',\n                                                            value: config.geminiApiKey || '',\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        geminiApiKey: e.target.value\n                                                                    })),\n                                                            placeholder: \"AIza...\",\n                                                            className: \"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>toggleApiKeyVisibility('gemini'),\n                                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                            children: showApiKeys.gemini ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Modelo\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: config.geminiModel,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                geminiModel: e.target.value\n                                                            })),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\",\n                                                    children: GEMINI_MODELS.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: model,\n                                                            children: model\n                                                        }, model, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Max Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.geminiMaxTokens,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                geminiMaxTokens: parseInt(e.target.value) || 2000\n                                                            })),\n                                                    min: \"100\",\n                                                    max: \"8000\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Temperature\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.geminiTemperature,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                geminiTemperature: parseFloat(e.target.value) || 0.7\n                                                            })),\n                                                    min: \"0\",\n                                                    max: \"2\",\n                                                    step: \"0.1\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-600 mt-0.5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-yellow-800 mb-2\",\n                                                children: \"Informaci\\xf3n Importante\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-sm text-yellow-700 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Las API keys se almacenan de forma segura en la base de datos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Puedes usar variables de entorno (OPENAI_API_KEY, GEMINI_API_KEY) como respaldo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Los diarios pueden configurarse individualmente para usar un proveedor espec\\xedfico\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• La configuraci\\xf3n global se aplica a todos los diarios que no tengan configuraci\\xf3n espec\\xedfica\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Aseg\\xfarate de probar las conexiones antes de guardar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/admin'),\n                                    className: \"px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: saveConfig,\n                                    disabled: saving,\n                                    className: \"flex items-center space-x-2 px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-md transition-colors\",\n                                    children: [\n                                        saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Eye_EyeOff_Loader2_Save_Settings_TestTube_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: saving ? 'Guardando...' : 'Guardar Configuración'\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n            lineNumber: 248,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\admin\\\\ai-config\\\\page.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(AIConfigPage, \"KDIi27SBLViOW5P6NIXUifHX6/c=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = AIConfigPage;\nvar _c;\n$RefreshReg$(_c, \"AIConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/ai-config/page.tsx\n"));

/***/ })

});