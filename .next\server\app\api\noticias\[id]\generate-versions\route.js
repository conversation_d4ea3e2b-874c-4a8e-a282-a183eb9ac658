/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/noticias/[id]/generate-versions/route";
exports.ids = ["app/api/noticias/[id]/generate-versions/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_noticias_id_generate_versions_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/noticias/[id]/generate-versions/route.ts */ \"(rsc)/./src/app/api/noticias/[id]/generate-versions/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/noticias/[id]/generate-versions/route\",\n        pathname: \"/api/noticias/[id]/generate-versions\",\n        filename: \"route\",\n        bundlePath: \"app/api/noticias/[id]/generate-versions/route\"\n    },\n    resolvedPagePath: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\api\\\\noticias\\\\[id]\\\\generate-versions\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_noticias_id_generate_versions_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/noticias/[id]/generate-versions/route.ts":
/*!**************************************************************!*\
  !*** ./src/app/api/noticias/[id]/generate-versions/route.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/openai */ \"(rsc)/./src/lib/openai.ts\");\n\n\n\n\n\n// POST /api/noticias/[id]/generate-versions - Generar versiones con IA\nasync function POST(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        const id = parseInt(params.id);\n        if (isNaN(id)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID inválido'\n            }, {\n                status: 400\n            });\n        }\n        // Obtener datos del request\n        const body = await request.json();\n        console.log('Body recibido:', body);\n        const { diarioIds } = body; // Array de IDs de diarios seleccionados\n        console.log('diarioIds extraídos:', diarioIds);\n        if (!diarioIds || !Array.isArray(diarioIds) || diarioIds.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Debe seleccionar al menos un diario'\n            }, {\n                status: 400\n            });\n        }\n        // Verificar que la noticia existe\n        const noticia = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.findUnique({\n            where: {\n                id\n            },\n            include: {\n                categoria: true,\n                user: true\n            }\n        });\n        if (!noticia) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Noticia no encontrada'\n            }, {\n                status: 404\n            });\n        }\n        // Verificar permisos (solo el autor o admin/editor pueden generar versiones)\n        const userRole = session.user.role;\n        const isAuthor = noticia.userId === parseInt(session.user.id);\n        const canGenerate = isAuthor || userRole === 'ADMIN' || userRole === 'EDITOR';\n        if (!canGenerate) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No tienes permisos para generar versiones de esta noticia'\n            }, {\n                status: 403\n            });\n        }\n        // Obtener los diarios seleccionados\n        const diarios = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.diario.findMany({\n            where: {\n                id: {\n                    in: diarioIds\n                },\n                isActive: true\n            }\n        });\n        if (diarios.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No se encontraron diarios válidos'\n            }, {\n                status: 400\n            });\n        }\n        const generatedVersions = [];\n        const errors = [];\n        // Generar versión para cada diario\n        for (const diario of diarios){\n            try {\n                console.log(`Generando versión para ${diario.nombre}...`);\n                const rewriteResponse = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_4__.rewriteNoticia)({\n                    titulo: noticia.titulo,\n                    subtitulo: noticia.subtitulo || undefined,\n                    volanta: noticia.volanta || undefined,\n                    contenido: noticia.contenido,\n                    resumen: noticia.resumen || undefined,\n                    prompt: diario.prompt,\n                    diarioNombre: diario.nombre\n                });\n                // Guardar la versión generada en la base de datos\n                const versionGuardada = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.versionNoticia.create({\n                    data: {\n                        titulo: rewriteResponse.titulo,\n                        subtitulo: rewriteResponse.subtitulo,\n                        volanta: rewriteResponse.volanta,\n                        contenido: rewriteResponse.contenido,\n                        resumen: rewriteResponse.resumen,\n                        promptUsado: diario.prompt,\n                        metadatos: JSON.stringify(rewriteResponse.metadatos),\n                        noticiaId: noticia.id,\n                        diarioId: diario.id,\n                        generadaPor: parseInt(session.user.id),\n                        estado: 'GENERADA'\n                    },\n                    include: {\n                        diario: true,\n                        usuario: {\n                            select: {\n                                id: true,\n                                name: true,\n                                email: true\n                            }\n                        }\n                    }\n                });\n                generatedVersions.push(versionGuardada);\n                console.log(`✅ Versión generada para ${diario.nombre}`);\n            } catch (error) {\n                console.error(`Error generando versión para ${diario.nombre}:`, error);\n                errors.push({\n                    diario: diario.nombre,\n                    error: error instanceof Error ? error.message : 'Error desconocido'\n                });\n            }\n        }\n        // Cambiar el estado de la noticia a EN_REVISION si se generaron versiones\n        if (generatedVersions.length > 0 && noticia.estado === 'BORRADOR') {\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.update({\n                where: {\n                    id\n                },\n                data: {\n                    estado: 'EN_REVISION'\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Se generaron ${generatedVersions.length} versiones exitosamente`,\n            versiones: generatedVersions,\n            errores: errors\n        });\n    } catch (error) {\n        console.error('Error en generate-versions:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/noticias/[id]/generate-versions/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const user = await prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        }\n                    });\n                    if (!user) {\n                        return null;\n                    }\n                    const isPasswordValid = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        return null;\n                    }\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error during authentication:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    },\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rewriteNoticia: () => (/* binding */ rewriteNoticia),\n/* harmony export */   testOpenAIConnection: () => (/* binding */ testOpenAIConnection)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/.pnpm/openai@5.10.1_zod@3.25.75/node_modules/openai/index.mjs\");\n\n// Configuración de OpenAI\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY || 'your-openai-api-key-here'\n});\nasync function rewriteNoticia(request) {\n    const startTime = Date.now();\n    try {\n        // Construir el prompt completo\n        const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"subtitulo\": \"nuevo subtítulo\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n        const completion = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional.\"\n                },\n                {\n                    role: \"user\",\n                    content: fullPrompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 2000\n        });\n        const responseText = completion.choices[0]?.message?.content;\n        if (!responseText) {\n            throw new Error('No se recibió respuesta de OpenAI');\n        }\n        // Intentar parsear la respuesta JSON\n        let parsedResponse;\n        try {\n            parsedResponse = JSON.parse(responseText);\n        } catch (parseError) {\n            // Si falla el parsing, intentar extraer JSON del texto\n            const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                parsedResponse = JSON.parse(jsonMatch[0]);\n            } else {\n                throw new Error('La respuesta de OpenAI no está en formato JSON válido');\n            }\n        }\n        const endTime = Date.now();\n        const generationTime = endTime - startTime;\n        return {\n            titulo: parsedResponse.titulo || request.titulo,\n            subtitulo: parsedResponse.subtitulo,\n            volanta: parsedResponse.volanta,\n            contenido: parsedResponse.contenido || request.contenido,\n            resumen: parsedResponse.resumen,\n            metadatos: {\n                modelo: completion.model,\n                tokens_usados: completion.usage?.total_tokens || 0,\n                tiempo_generacion: generationTime,\n                diario: request.diarioNombre\n            }\n        };\n    } catch (error) {\n        console.error('Error en rewriteNoticia:', error);\n        throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);\n    }\n}\nasync function testOpenAIConnection() {\n    try {\n        const completion = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: [\n                {\n                    role: \"user\",\n                    content: \"Responde solo con 'OK'\"\n                }\n            ],\n            max_tokens: 5\n        });\n        return completion.choices[0]?.message?.content?.includes('OK') || false;\n    } catch (error) {\n        console.error('Error testing OpenAI connection:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkc6XFxERUwgU1VSIEZJTkFMXFxwYW5lbCB1bmlmaWNhZG8gdmVyc2lvbiAyXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XHJcblxyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTsgIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@babel+runtime@7.27.6","vendor-chunks/jose@4.15.9","vendor-chunks/openid-client@5.7.1","vendor-chunks/oauth@0.9.15","vendor-chunks/object-hash@2.2.0","vendor-chunks/preact@10.26.9","vendor-chunks/uuid@8.3.2","vendor-chunks/yallist@4.0.0","vendor-chunks/preact-render-to-string@5.2.6_preact@10.26.9","vendor-chunks/lru-cache@6.0.0","vendor-chunks/cookie@0.7.2","vendor-chunks/oidc-token-hash@5.1.0","vendor-chunks/@panva+hkdf@1.2.1","vendor-chunks/openai@5.10.1_zod@3.25.75"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();