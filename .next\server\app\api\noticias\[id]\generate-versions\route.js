/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/noticias/[id]/generate-versions/route";
exports.ids = ["app/api/noticias/[id]/generate-versions/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_noticias_id_generate_versions_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/noticias/[id]/generate-versions/route.ts */ \"(rsc)/./src/app/api/noticias/[id]/generate-versions/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/noticias/[id]/generate-versions/route\",\n        pathname: \"/api/noticias/[id]/generate-versions\",\n        filename: \"route\",\n        bundlePath: \"app/api/noticias/[id]/generate-versions/route\"\n    },\n    resolvedPagePath: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\api\\\\noticias\\\\[id]\\\\generate-versions\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_noticias_id_generate_versions_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/noticias/[id]/generate-versions/route.ts":
/*!**************************************************************!*\
  !*** ./src/app/api/noticias/[id]/generate-versions/route.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai-service */ \"(rsc)/./src/lib/ai-service.ts\");\n\n\n\n\n\n// POST /api/noticias/[id]/generate-versions - Generar versiones con IA\nasync function POST(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        const id = parseInt(params.id);\n        if (isNaN(id)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID inválido'\n            }, {\n                status: 400\n            });\n        }\n        // Obtener datos del request\n        const body = await request.json();\n        console.log('Body recibido:', body);\n        const { diarioIds } = body; // Array de IDs de diarios seleccionados\n        console.log('diarioIds extraídos:', diarioIds);\n        if (!diarioIds || !Array.isArray(diarioIds) || diarioIds.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Debe seleccionar al menos un diario'\n            }, {\n                status: 400\n            });\n        }\n        // Verificar que la noticia existe\n        const noticia = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.findUnique({\n            where: {\n                id\n            },\n            include: {\n                categoria: true,\n                user: true\n            }\n        });\n        if (!noticia) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Noticia no encontrada'\n            }, {\n                status: 404\n            });\n        }\n        // Verificar permisos (solo el autor o admin/editor pueden generar versiones)\n        const userRole = session.user.role;\n        const isAuthor = noticia.userId === parseInt(session.user.id);\n        const canGenerate = isAuthor || userRole === 'ADMIN' || userRole === 'EDITOR';\n        if (!canGenerate) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No tienes permisos para generar versiones de esta noticia'\n            }, {\n                status: 403\n            });\n        }\n        // Obtener los diarios seleccionados\n        const diarios = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.diario.findMany({\n            where: {\n                id: {\n                    in: diarioIds\n                },\n                isActive: true\n            }\n        });\n        if (diarios.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No se encontraron diarios válidos'\n            }, {\n                status: 400\n            });\n        }\n        const generatedVersions = [];\n        const errors = [];\n        // Generar versión para cada diario\n        for (const diario of diarios){\n            try {\n                console.log(`Generando versión para ${diario.nombre}...`);\n                const rewriteResponse = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_4__.rewriteNoticia)({\n                    titulo: noticia.titulo,\n                    subtitulo: noticia.subtitulo || undefined,\n                    volanta: noticia.volanta || undefined,\n                    contenido: noticia.contenido,\n                    resumen: noticia.resumen || undefined,\n                    prompt: diario.prompt,\n                    diarioNombre: diario.nombre\n                }, diario.id);\n                // Guardar la versión generada en la base de datos\n                const versionGuardada = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.versionNoticia.create({\n                    data: {\n                        titulo: rewriteResponse.titulo,\n                        subtitulo: rewriteResponse.subtitulo,\n                        volanta: rewriteResponse.volanta,\n                        contenido: rewriteResponse.contenido,\n                        resumen: rewriteResponse.resumen,\n                        promptUsado: diario.prompt,\n                        metadatos: JSON.stringify(rewriteResponse.metadatos),\n                        noticiaId: noticia.id,\n                        diarioId: diario.id,\n                        generadaPor: parseInt(session.user.id),\n                        estado: 'GENERADA'\n                    },\n                    include: {\n                        diario: true,\n                        usuario: {\n                            select: {\n                                id: true,\n                                name: true,\n                                email: true\n                            }\n                        }\n                    }\n                });\n                generatedVersions.push(versionGuardada);\n                console.log(`✅ Versión generada para ${diario.nombre}`);\n            } catch (error) {\n                console.error(`Error generando versión para ${diario.nombre}:`, error);\n                errors.push({\n                    diario: diario.nombre,\n                    error: error instanceof Error ? error.message : 'Error desconocido'\n                });\n            }\n        }\n        // Cambiar el estado de la noticia a EN_REVISION si se generaron versiones\n        if (generatedVersions.length > 0 && noticia.estado === 'BORRADOR') {\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.update({\n                where: {\n                    id\n                },\n                data: {\n                    estado: 'EN_REVISION'\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Se generaron ${generatedVersions.length} versiones exitosamente`,\n            versiones: generatedVersions,\n            errores: errors\n        });\n    } catch (error) {\n        console.error('Error en generate-versions:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/noticias/[id]/generate-versions/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ai-service.ts":
/*!*******************************!*\
  !*** ./src/lib/ai-service.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAIConfig: () => (/* binding */ getAIConfig),\n/* harmony export */   rewriteNoticia: () => (/* binding */ rewriteNoticia),\n/* harmony export */   testGeminiConnection: () => (/* binding */ testGeminiConnection),\n/* harmony export */   testOpenAIConnection: () => (/* binding */ testOpenAIConnection)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/.pnpm/openai@5.10.1_zod@3.25.75/node_modules/openai/index.mjs\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_2__.PrismaClient();\n// Obtener configuración de IA\nasync function getAIConfig() {\n    let config = await prisma.aIConfig.findFirst({\n        where: {\n            isActive: true\n        },\n        orderBy: {\n            createdAt: 'desc'\n        }\n    });\n    // Migrar modelos obsoletos\n    if (config && (config.geminiModel === 'gemini-pro' || config.geminiModel === 'gemini-1.5-flash')) {\n        const newModel = 'gemini-2.5-flash';\n        console.log(`🔄 Migrando modelo Gemini obsoleto de ${config.geminiModel} a ${newModel}`);\n        config = await prisma.aIConfig.update({\n            where: {\n                id: config.id\n            },\n            data: {\n                geminiModel: newModel\n            }\n        });\n    }\n    if (!config) {\n        // Configuración por defecto\n        return {\n            openaiApiKey: process.env.OPENAI_API_KEY,\n            openaiModel: 'gpt-3.5-turbo',\n            openaiMaxTokens: 2000,\n            openaiTemperature: 0.7,\n            geminiApiKey: process.env.GEMINI_API_KEY,\n            geminiModel: 'gemini-2.5-flash',\n            geminiMaxTokens: 2000,\n            geminiTemperature: 0.7,\n            defaultProvider: 'OPENAI'\n        };\n    }\n    return {\n        openaiApiKey: config.openaiApiKey || process.env.OPENAI_API_KEY,\n        openaiModel: config.openaiModel,\n        openaiMaxTokens: config.openaiMaxTokens,\n        openaiTemperature: config.openaiTemperature,\n        geminiApiKey: config.geminiApiKey || process.env.GEMINI_API_KEY,\n        geminiModel: config.geminiModel,\n        geminiMaxTokens: config.geminiMaxTokens,\n        geminiTemperature: config.geminiTemperature,\n        defaultProvider: config.defaultProvider\n    };\n}\n// Reescribir noticia usando OpenAI\nasync function rewriteWithOpenAI(request, config) {\n    const startTime = Date.now();\n    if (!config.openaiApiKey) {\n        throw new Error('OpenAI API key no configurada');\n    }\n    const openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        apiKey: config.openaiApiKey\n    });\n    const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- NO generes subtítulo, solo volanta, título, resumen y contenido\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n    const completion = await openai.chat.completions.create({\n        model: config.openaiModel,\n        messages: [\n            {\n                role: \"system\",\n                content: \"Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional.\"\n            },\n            {\n                role: \"user\",\n                content: fullPrompt\n            }\n        ],\n        temperature: config.openaiTemperature,\n        max_tokens: config.openaiMaxTokens\n    });\n    const responseText = completion.choices[0]?.message?.content;\n    if (!responseText) {\n        throw new Error('No se recibió respuesta de OpenAI');\n    }\n    // Intentar parsear la respuesta JSON\n    let parsedResponse;\n    try {\n        parsedResponse = JSON.parse(responseText);\n    } catch (parseError) {\n        // Si falla el parsing, intentar extraer JSON del texto\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            parsedResponse = JSON.parse(jsonMatch[0]);\n        } else {\n            throw new Error('La respuesta de OpenAI no está en formato JSON válido');\n        }\n    }\n    const endTime = Date.now();\n    const generationTime = endTime - startTime;\n    return {\n        titulo: parsedResponse.titulo || request.titulo,\n        volanta: parsedResponse.volanta,\n        contenido: parsedResponse.contenido || request.contenido,\n        resumen: parsedResponse.resumen,\n        metadatos: {\n            modelo: completion.model,\n            tokens_usados: completion.usage?.total_tokens || 0,\n            tiempo_generacion: generationTime,\n            diario: request.diarioNombre,\n            proveedor: 'OpenAI'\n        }\n    };\n}\n// Reescribir noticia usando Google Gemini\nasync function rewriteWithGemini(request, config) {\n    const startTime = Date.now();\n    if (!config.geminiApiKey) {\n        throw new Error('Google Gemini API key no configurada');\n    }\n    const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(config.geminiApiKey);\n    const model = genAI.getGenerativeModel({\n        model: config.geminiModel,\n        generationConfig: {\n            temperature: config.geminiTemperature,\n            maxOutputTokens: config.geminiMaxTokens\n        }\n    });\n    const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- NO generes subtítulo, solo volanta, título, resumen y contenido\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n    const result = await model.generateContent(fullPrompt);\n    const response = await result.response;\n    const responseText = response.text();\n    if (!responseText) {\n        throw new Error('No se recibió respuesta de Google Gemini');\n    }\n    // Intentar parsear la respuesta JSON\n    let parsedResponse;\n    try {\n        parsedResponse = JSON.parse(responseText);\n    } catch (parseError) {\n        // Si falla el parsing, intentar extraer JSON del texto\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            parsedResponse = JSON.parse(jsonMatch[0]);\n        } else {\n            throw new Error('La respuesta de Google Gemini no está en formato JSON válido');\n        }\n    }\n    const endTime = Date.now();\n    const generationTime = endTime - startTime;\n    return {\n        titulo: parsedResponse.titulo || request.titulo,\n        volanta: parsedResponse.volanta,\n        contenido: parsedResponse.contenido || request.contenido,\n        resumen: parsedResponse.resumen,\n        metadatos: {\n            modelo: config.geminiModel,\n            tokens_usados: 0,\n            tiempo_generacion: generationTime,\n            diario: request.diarioNombre,\n            proveedor: 'Google Gemini'\n        }\n    };\n}\n// Función principal para reescribir noticias\nasync function rewriteNoticia(request, diarioId) {\n    try {\n        const config = await getAIConfig();\n        let provider = config.defaultProvider;\n        // Si se especifica un diario, verificar su configuración específica\n        if (diarioId) {\n            const diario = await prisma.diario.findUnique({\n                where: {\n                    id: diarioId\n                }\n            });\n            if (diario && !diario.useGlobalConfig && diario.aiProvider) {\n                provider = diario.aiProvider;\n                // Si el diario tiene un modelo específico, usarlo\n                if (diario.aiModel) {\n                    if (provider === 'OPENAI') {\n                        config.openaiModel = diario.aiModel;\n                    } else {\n                        config.geminiModel = diario.aiModel;\n                    }\n                }\n            }\n        }\n        // Ejecutar reescritura según el proveedor\n        if (provider === 'GEMINI') {\n            return await rewriteWithGemini(request, config);\n        } else {\n            return await rewriteWithOpenAI(request, config);\n        }\n    } catch (error) {\n        console.error('Error en rewriteNoticia:', error);\n        throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);\n    }\n}\n// Función para probar conexión con OpenAI\nasync function testOpenAIConnection() {\n    try {\n        console.log('🔍 Probando conexión con OpenAI...');\n        const config = await getAIConfig();\n        const keyToUse = config.openaiApiKey || process.env.OPENAI_API_KEY;\n        console.log('🔑 API Key disponible:', !!keyToUse);\n        console.log('📝 Modelo a usar:', config.openaiModel);\n        if (!keyToUse) {\n            console.error('❌ No hay API key disponible');\n            return false;\n        }\n        const openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            apiKey: keyToUse\n        });\n        console.log('📡 Enviando petición de prueba...');\n        const completion = await openai.chat.completions.create({\n            model: config.openaiModel,\n            messages: [\n                {\n                    role: \"user\",\n                    content: \"Responde solo con 'OK'\"\n                }\n            ],\n            max_tokens: 5,\n            temperature: 0\n        });\n        console.log('📥 Respuesta recibida:', completion.choices[0]?.message?.content);\n        const isOk = completion.choices[0]?.message?.content?.includes('OK') || false;\n        console.log('✅ Conexión exitosa:', isOk);\n        return isOk;\n    } catch (error) {\n        console.error('❌ Error testing OpenAI connection:', error);\n        if (error instanceof Error) {\n            console.error('❌ Error message:', error.message);\n            console.error('❌ Error stack:', error.stack);\n        }\n        return false;\n    }\n}\n// Función para probar conexión con Google Gemini\nasync function testGeminiConnection() {\n    try {\n        console.log('🔍 Probando conexión con Google Gemini...');\n        const config = await getAIConfig();\n        const keyToUse = config.geminiApiKey || process.env.GEMINI_API_KEY;\n        console.log('🔑 API Key disponible:', !!keyToUse);\n        console.log('📝 Modelo a usar:', config.geminiModel);\n        if (!keyToUse) {\n            console.error('❌ No hay API key disponible para Gemini');\n            return false;\n        }\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(keyToUse);\n        const model = genAI.getGenerativeModel({\n            model: config.geminiModel,\n            generationConfig: {\n                temperature: 0,\n                maxOutputTokens: 10\n            }\n        });\n        console.log('📡 Enviando petición de prueba a Gemini...');\n        const result = await model.generateContent(\"Responde solo con 'OK'\");\n        const response = await result.response;\n        const text = response.text();\n        console.log('📥 Respuesta recibida de Gemini:', text);\n        const isOk = text.includes('OK');\n        console.log('✅ Conexión Gemini exitosa:', isOk);\n        return isOk;\n    } catch (error) {\n        console.error('❌ Error testing Gemini connection:', error);\n        if (error instanceof Error) {\n            console.error('❌ Error message:', error.message);\n            console.error('❌ Error stack:', error.stack);\n        }\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ai-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const user = await prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        }\n                    });\n                    if (!user) {\n                        return null;\n                    }\n                    const isPasswordValid = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        return null;\n                    }\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error during authentication:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    },\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkc6XFxERUwgU1VSIEZJTkFMXFxwYW5lbCB1bmlmaWNhZG8gdmVyc2lvbiAyXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XHJcblxyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTsgIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@babel+runtime@7.27.6","vendor-chunks/jose@4.15.9","vendor-chunks/openid-client@5.7.1","vendor-chunks/oauth@0.9.15","vendor-chunks/object-hash@2.2.0","vendor-chunks/preact@10.26.9","vendor-chunks/uuid@8.3.2","vendor-chunks/yallist@4.0.0","vendor-chunks/preact-render-to-string@5.2.6_preact@10.26.9","vendor-chunks/lru-cache@6.0.0","vendor-chunks/cookie@0.7.2","vendor-chunks/oidc-token-hash@5.1.0","vendor-chunks/@panva+hkdf@1.2.1","vendor-chunks/openai@5.10.1_zod@3.25.75","vendor-chunks/@google+generative-ai@0.24.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&page=%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2F%5Bid%5D%2Fgenerate-versions%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();