import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/admin/diarios-config - Obtener configuración de diarios
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de admin
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Solo los administradores pueden acceder a esta configuración' },
        { status: 403 }
      );
    }

    const diarios = await prisma.diario.findMany({
      orderBy: { nombre: 'asc' },
    });

    return NextResponse.json({ diarios });

  } catch (error) {
    console.error('Error al obtener configuración de diarios:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/diarios-config - Actualizar configuración de diarios
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de admin
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Solo los administradores pueden modificar esta configuración' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { diarioId, nombre, descripcion, prompt, isActive, aiProvider, aiModel, useGlobalConfig } = body;

    if (!diarioId) {
      return NextResponse.json(
        { error: 'diarioId es requerido' },
        { status: 400 }
      );
    }

    // Validaciones básicas
    if (nombre && nombre.trim().length === 0) {
      return NextResponse.json(
        { error: 'El nombre no puede estar vacío' },
        { status: 400 }
      );
    }

    if (prompt && prompt.trim().length === 0) {
      return NextResponse.json(
        { error: 'El prompt no puede estar vacío' },
        { status: 400 }
      );
    }

    // Construir objeto de actualización
    const updateData: any = {};
    if (nombre !== undefined) updateData.nombre = nombre.trim();
    if (descripcion !== undefined) updateData.descripcion = descripcion?.trim() || null;
    if (prompt !== undefined) updateData.prompt = prompt.trim();
    if (isActive !== undefined) updateData.isActive = Boolean(isActive);
    if (aiProvider !== undefined) updateData.aiProvider = aiProvider;
    if (aiModel !== undefined) updateData.aiModel = aiModel?.trim() || null;
    if (useGlobalConfig !== undefined) updateData.useGlobalConfig = Boolean(useGlobalConfig);

    // Actualizar el diario
    const diarioActualizado = await prisma.diario.update({
      where: { id: diarioId },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      diario: diarioActualizado,
    });

  } catch (error) {
    console.error('Error al actualizar configuración de diario:', error);

    // Manejar errores específicos de Prisma
    if (error instanceof Error && error.message.includes('Record to update not found')) {
      return NextResponse.json(
        { error: 'Diario no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/admin/diarios-config - Crear nuevo diario
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de admin
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Solo los administradores pueden crear diarios' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { nombre, descripcion, prompt, aiProvider, aiModel, useGlobalConfig } = body;

    // Validaciones
    if (!nombre || nombre.trim().length === 0) {
      return NextResponse.json(
        { error: 'El nombre es requerido' },
        { status: 400 }
      );
    }

    if (!prompt || prompt.trim().length === 0) {
      return NextResponse.json(
        { error: 'El prompt es requerido' },
        { status: 400 }
      );
    }

    // Verificar que no existe un diario con el mismo nombre
    const diarioExistente = await prisma.diario.findUnique({
      where: { nombre: nombre.trim() },
    });

    if (diarioExistente) {
      return NextResponse.json(
        { error: 'Ya existe un diario con ese nombre' },
        { status: 400 }
      );
    }

    // Crear el nuevo diario
    const nuevoDiario = await prisma.diario.create({
      data: {
        nombre: nombre.trim(),
        descripcion: descripcion?.trim() || null,
        prompt: prompt.trim(),
        isActive: true,
        aiProvider: aiProvider || 'OPENAI',
        aiModel: aiModel?.trim() || null,
        useGlobalConfig: useGlobalConfig !== undefined ? Boolean(useGlobalConfig) : true,
      },
    });

    return NextResponse.json({
      success: true,
      diario: nuevoDiario,
    }, { status: 201 });

  } catch (error) {
    console.error('Error al crear diario:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}