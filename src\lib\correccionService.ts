import { Correccion, CreateCorreccionData, UpdateCorreccionData, CorreccionFilters } from '@/types/correccion';

const API_BASE_URL = '/api/correcciones';

export class CorreccionService {
  // Obtener todas las correcciones con filtros opcionales
  static async getCorrecciones(filters?: CorreccionFilters): Promise<Correccion[]> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
    }

    const response = await fetch(`${API_BASE_URL}?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error('Error al obtener correcciones');
    }

    return response.json();
  }

  // Obtener una corrección por ID
  static async getCorreccion(id: string): Promise<Correccion> {
    const response = await fetch(`${API_BASE_URL}/${id}`);
    
    if (!response.ok) {
      throw new Error('Error al obtener la corrección');
    }

    return response.json();
  }

  // Crear una nueva corrección
  static async createCorreccion(data: CreateCorreccionData): Promise<Correccion> {
    const response = await fetch(API_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Error al crear la corrección');
    }

    return response.json();
  }

  // Actualizar una corrección
  static async updateCorreccion(id: string, data: UpdateCorreccionData): Promise<Correccion> {
    const response = await fetch(`${API_BASE_URL}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Error al actualizar la corrección');
    }

    return response.json();
  }

  // Eliminar una corrección
  static async deleteCorreccion(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Error al eliminar la corrección');
    }
  }

  // Subir imagen para una corrección
  static async uploadImage(correccionId: string, file: File): Promise<{ imagenUrl: string }> {
    const formData = new FormData();
    formData.append('image', file);

    const response = await fetch(`${API_BASE_URL}/${correccionId}/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Error al subir la imagen');
    }

    return response.json();
  }

  // Obtener estadísticas de correcciones
  static async getStats(): Promise<{
    total: number;
    pendientes: number;
    enRevision: number;
    completadas: number;
    rechazadas: number;
    porPrioridad: {
      baja: number;
      media: number;
      alta: number;
      urgente: number;
    };
    recientes: any[];
  }> {
    const response = await fetch(`${API_BASE_URL}/stats`);
    
    if (!response.ok) {
      throw new Error('Error al obtener estadísticas');
    }

    return response.json();
  }
} 