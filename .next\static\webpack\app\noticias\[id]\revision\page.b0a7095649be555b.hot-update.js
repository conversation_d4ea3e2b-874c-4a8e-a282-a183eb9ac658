"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/noticias/[id]/revision/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/external-link.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/external-link.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ExternalLink)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 3h6v6\",\n            key: \"1q9fwt\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 14 21 3\",\n            key: \"gplh6r\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\",\n            key: \"a6xqqp\"\n        }\n    ]\n];\nconst ExternalLink = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"external-link\", __iconNode);\n //# sourceMappingURL=external-link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/external-link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/noticias/[id]/revision/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RevisionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Calendar,CheckCircle,ChevronDown,ChevronUp,Clock,ExternalLink,FileText,Plus,Send,User,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RevisionPage() {\n    var _session_user, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = Array.isArray(params.id) ? params.id[0] : params.id;\n    const [noticia, setNoticia] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [versiones, setVersiones] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [expandedVersions, setExpandedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Set());\n    // Estados para generación incremental\n    const [diarios, setDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [showGenerateMore, setShowGenerateMore] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedNewDiarios, setSelectedNewDiarios] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        status,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            if (session && id) {\n                loadData();\n            }\n        }\n    }[\"RevisionPage.useEffect\"], [\n        session,\n        id\n    ]);\n    // Debug: monitorear cambios en selectedNewDiarios\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RevisionPage.useEffect\": ()=>{\n            console.log('selectedNewDiarios changed:', selectedNewDiarios);\n        }\n    }[\"RevisionPage.useEffect\"], [\n        selectedNewDiarios\n    ]);\n    const loadData = async ()=>{\n        try {\n            // Cargar noticia\n            const noticiaResponse = await fetch(\"/api/noticias/\".concat(id));\n            if (noticiaResponse.ok) {\n                const noticiaData = await noticiaResponse.json();\n                setNoticia(noticiaData);\n            }\n            // Cargar versiones\n            const versionesResponse = await fetch(\"/api/noticias/\".concat(id, \"/versions\"));\n            if (versionesResponse.ok) {\n                const versionesData = await versionesResponse.json();\n                setVersiones(versionesData.versiones);\n                // Expandir todas las versiones por defecto\n                setExpandedVersions(new Set(versionesData.versiones.map((v)=>v.id)));\n            }\n            // Cargar diarios disponibles\n            const diariosResponse = await fetch('/api/diarios');\n            if (diariosResponse.ok) {\n                const diariosData = await diariosResponse.json();\n                setDiarios(diariosData.diarios);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos:', error);\n            alert('Error al cargar los datos');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVersionStateChange = async (versionId, estado)=>{\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/versions\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    versionId,\n                    estado\n                })\n            });\n            if (response.ok) {\n                await loadData(); // Recargar datos\n                alert(\"✅ Estado actualizado a: \".concat(estado));\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al actualizar estado:', error);\n            alert('Error al actualizar el estado de la versión');\n        }\n    };\n    const toggleVersionExpansion = (versionId)=>{\n        const newExpanded = new Set(expandedVersions);\n        if (newExpanded.has(versionId)) {\n            newExpanded.delete(versionId);\n        } else {\n            newExpanded.add(versionId);\n        }\n        setExpandedVersions(newExpanded);\n    };\n    const getEstadoBadge = (estado)=>{\n        const badges = {\n            'GENERADA': 'bg-blue-100 text-blue-800',\n            'APROBADA': 'bg-green-100 text-green-800',\n            'RECHAZADA': 'bg-red-100 text-red-800',\n            'EN_REVISION': 'bg-yellow-100 text-yellow-800'\n        };\n        return badges[estado] || 'bg-gray-100 text-gray-800';\n    };\n    const getEstadoIcon = (estado)=>{\n        switch(estado){\n            case 'APROBADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 16\n                }, this);\n            case 'RECHAZADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 16\n                }, this);\n            case 'EN_REVISION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // Obtener diarios que ya tienen versiones generadas\n    const getDiariosConVersiones = ()=>{\n        return versiones.map((v)=>v.diario.id);\n    };\n    // Obtener diarios disponibles para generar (que no tienen versiones)\n    const getDiariosDisponibles = ()=>{\n        const diariosConVersiones = getDiariosConVersiones();\n        return diarios.filter((d)=>!diariosConVersiones.includes(d.id));\n    };\n    // Manejar generación incremental\n    const handleGenerateMore = async ()=>{\n        console.log('selectedNewDiarios:', selectedNewDiarios);\n        if (selectedNewDiarios.length === 0) {\n            alert('Selecciona al menos un diario para generar versiones');\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const response = await fetch(\"/api/noticias/\".concat(id, \"/generate-versions\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    diarioIds: selectedNewDiarios\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                alert(\"✅ \".concat(data.generadas, \" versiones generadas exitosamente\"));\n                // Recargar datos para mostrar las nuevas versiones\n                await loadData();\n                // Limpiar selección y cerrar modal\n                setSelectedNewDiarios([]);\n                setShowGenerateMore(false);\n            } else {\n                const data = await response.json();\n                alert(\"❌ Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error('Error al generar versiones:', error);\n            alert('Error al generar las versiones');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando revisi\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, this);\n    }\n    if (!noticia) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Noticia no encontrada\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this);\n    }\n    // Verificar permisos\n    const canReview = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'ADMIN' || (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role) === 'EDITOR';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.back(),\n                                        className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Volver\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Revisi\\xf3n de Noticia\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Estado: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: noticia.estado.replace('_', ' ')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 text-sm font-medium rounded-full \".concat(noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800' : noticia.estado === 'APROBADA' ? 'bg-green-100 text-green-800' : noticia.estado === 'PUBLICADA' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'),\n                                    children: noticia.estado.replace('_', ' ')\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:grid lg:grid-cols-12 gap-6 lg:gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-7 xl:col-span-8 order-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-4 bg-blue-50 border-b border-blue-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold text-blue-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-6 w-6 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Noticia Original\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 text-sm text-blue-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    noticia.user.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    new Date(noticia.createdAt).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Categor\\xeda\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    style: {\n                                                                        color: noticia.categoria.color\n                                                                    },\n                                                                    children: noticia.categoria.nombre\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Estado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: noticia.estado.replace('_', ' ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Destacada\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: noticia.destacada ? 'Sí' : 'No'\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        noticia.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Volanta\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-600 font-medium uppercase tracking-wide\",\n                                                                    children: noticia.volanta\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"T\\xedtulo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl font-bold text-gray-900 leading-tight\",\n                                                                    children: noticia.titulo\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        noticia.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Subt\\xedtulo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg text-gray-700 font-medium\",\n                                                                    children: noticia.subtitulo\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        noticia.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Resumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-base text-gray-700 leading-relaxed\",\n                                                                    children: noticia.resumen\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        noticia.imagenUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Imagen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: noticia.imagenUrl,\n                                                                    alt: noticia.imagenAlt || noticia.titulo,\n                                                                    className: \"w-full max-w-2xl h-64 object-cover rounded-lg mt-2\",\n                                                                    onError: (e)=>{\n                                                                        e.currentTarget.style.display = 'none';\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Contenido\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none\",\n                                                                    children: noticia.contenido\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-4 border-t border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                                children: [\n                                                                    noticia.autor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Autor:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 417,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \" \",\n                                                                            noticia.autor\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    noticia.fuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Fuente:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 422,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \" \",\n                                                                            noticia.fuente\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    noticia.urlFuente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"md:col-span-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"URL Fuente:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            ' ',\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                href: noticia.urlFuente,\n                                                                                target: \"_blank\",\n                                                                                rel: \"noopener noreferrer\",\n                                                                                className: \"text-blue-600 hover:text-blue-800 underline\",\n                                                                                children: noticia.urlFuente\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-5 xl:col-span-4 order-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-bold text-gray-900 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    \"Versiones IA (\",\n                                                    versiones.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Versiones reescritas autom\\xe1ticamente.\",\n                                                    canReview && ' Puedes aprobar o rechazar cada una.'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 11\n                                    }, this),\n                                    diarios.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 bg-white shadow rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-base font-semibold text-gray-900 mb-3\",\n                                                        children: \"Estado por Diario\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    getDiariosDisponibles().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowGenerateMore(!showGenerateMore),\n                                                        className: \"w-full flex items-center justify-center space-x-2 px-3 py-2 text-xs font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Generar M\\xe1s\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 gap-3 mb-4\",\n                                                children: diarios.map((diario)=>{\n                                                    const tieneVersion = getDiariosConVersiones().includes(diario.id);\n                                                    const version = versiones.find((v)=>v.diario.id === diario.id);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg border \".concat(tieneVersion ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: diario.nombre\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    tieneVersion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            tieneVersion && version ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getEstadoBadge(version.estado)),\n                                                                        children: version.estado\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: new Date(version.createdAt).toLocaleDateString('es-ES', {\n                                                                            day: '2-digit',\n                                                                            month: 'short'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Pendiente\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, diario.id, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 15\n                                            }, this),\n                                            showGenerateMore && getDiariosDisponibles().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-semibold text-gray-900 mb-3\",\n                                                        children: \"Seleccionar Diarios\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 mb-4\",\n                                                        children: getDiariosDisponibles().map((diario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: selectedNewDiarios.includes(diario.id),\n                                                                        onChange: (e)=>{\n                                                                            console.log('Checkbox changed:', diario.nombre, e.target.checked);\n                                                                            if (e.target.checked) {\n                                                                                const newSelection = [\n                                                                                    ...selectedNewDiarios,\n                                                                                    diario.id\n                                                                                ];\n                                                                                console.log('Adding diario:', diario.id, 'New selection:', newSelection);\n                                                                                setSelectedNewDiarios(newSelection);\n                                                                            } else {\n                                                                                const newSelection = selectedNewDiarios.filter((id)=>id !== diario.id);\n                                                                                console.log('Removing diario:', diario.id, 'New selection:', newSelection);\n                                                                                setSelectedNewDiarios(newSelection);\n                                                                            }\n                                                                        },\n                                                                        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 530,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                                children: diario.nombre\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 548,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            diario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: diario.descripcion\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 550,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, diario.id, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 text-center\",\n                                                                children: [\n                                                                    selectedNewDiarios.length,\n                                                                    \" de \",\n                                                                    getDiariosDisponibles().length,\n                                                                    \" seleccionados\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleGenerateMore,\n                                                                        disabled: isGenerating || selectedNewDiarios.length === 0,\n                                                                        className: \"w-full flex items-center justify-center space-x-2 px-3 py-2 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors\",\n                                                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 569,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Generando...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 570,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 574,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Generar\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 575,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowGenerateMore(false),\n                                                                        className: \"w-full px-3 py-2 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            getDiariosDisponibles().length === 0 && versiones.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4 bg-green-50 rounded-lg border border-green-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-8 w-8 text-green-600 mx-auto mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-green-800\",\n                                                        children: \"\\xa1Todas las versiones han sido generadas!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-600\",\n                                                        children: \"Se han creado versiones para todos los diarios disponibles.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, this),\n                                    versiones.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white shadow rounded-lg p-6 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-400 mx-auto mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-900 mb-2\",\n                                                children: \"No hay versiones\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: \"Las versiones aparecer\\xe1n aqu\\xed una vez generadas.\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this),\n                    versiones.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-6 w-6 mr-2 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Versiones Generadas por IA (\",\n                                            versiones.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: [\n                                            \"Versiones reescritas autom\\xe1ticamente para diferentes diarios.\",\n                                            canReview && ' Puedes aprobar o rechazar cada versión individualmente.'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: versiones.map((version, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white shadow-lg rounded-lg overflow-hidden border-l-4 border-green-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-4 bg-green-50 border-b border-green-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>toggleVersionExpansion(version.id),\n                                                                        className: \"flex items-center space-x-2 text-green-900 hover:text-green-700 transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    getEstadoIcon(version.estado),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"text-lg font-bold\",\n                                                                                        children: version.diario.nombre\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                        lineNumber: 645,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            expandedVersions.has(version.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 650,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 652,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 text-xs font-medium rounded-full \".concat(getEstadoBadge(version.estado)),\n                                                                        children: version.estado\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 656,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            canReview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'APROBADA'),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 668,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Aprobar\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 669,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 664,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'RECHAZADA'),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 675,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Rechazar\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 676,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleVersionStateChange(version.id, 'EN_REVISION'),\n                                                                        className: \"flex items-center space-x-1 px-3 py-1 text-sm font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 rounded-md transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 682,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"En Revisi\\xf3n\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                lineNumber: 683,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center space-x-4 text-sm text-green-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Generado por \",\n                                                                    version.usuario.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    new Date(version.createdAt).toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            version.metadatos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    version.metadatos.tokens_usados,\n                                                                    \" tokens\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 19\n                                            }, this),\n                                            expandedVersions.has(version.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        version.volanta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Volanta\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-green-600 font-medium uppercase tracking-wide\",\n                                                                    children: version.volanta\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"T\\xedtulo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-xl font-bold text-gray-900 leading-tight\",\n                                                                    children: version.titulo\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        version.subtitulo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Subt\\xedtulo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg text-gray-700 font-medium\",\n                                                                    children: version.subtitulo\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        version.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Resumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-base text-gray-700 leading-relaxed\",\n                                                                    children: version.resumen\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 740,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Contenido\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 747,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-base text-gray-700 leading-relaxed whitespace-pre-wrap mt-2 prose max-w-none\",\n                                                                    children: version.contenido\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 746,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        version.metadatos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-4 border-t border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                                    children: \"Informaci\\xf3n T\\xe9cnica\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 756,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Modelo:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 759,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \" \",\n                                                                                version.metadatos.modelo\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 758,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Tokens:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 762,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \" \",\n                                                                                version.metadatos.tokens_usados\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Tiempo:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                                    lineNumber: 765,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \" \",\n                                                                                version.metadatos.tiempo_generacion,\n                                                                                \"ms\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                            lineNumber: 764,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                    lineNumber: 757,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        version.promptUsado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-4 border-t border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                                className: \"group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                                        className: \"cursor-pointer text-xs font-medium text-gray-500 uppercase tracking-wide hover:text-gray-700\",\n                                                                        children: \"Ver Prompt Utilizado\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 775,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2 p-3 bg-gray-50 rounded text-sm text-gray-700 font-mono whitespace-pre-wrap\",\n                                                                        children: version.promptUsado\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 778,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        version.diario.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-4 border-t border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: version.diario.url,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"inline-flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Calendar_CheckCircle_ChevronDown_ChevronUp_Clock_ExternalLink_FileText_Plus_Send_User_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Ver en \",\n                                                                            version.diario.nombre\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, version.id, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\noticias\\\\[id]\\\\revision\\\\page.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, this);\n}\n_s(RevisionPage, \"RoxOeDmqFYne6sGVAGdOGde1QMU=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = RevisionPage;\nvar _c;\n$RefreshReg$(_c, \"RevisionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/noticias/[id]/revision/page.tsx\n"));

/***/ })

});