'use client';

import { Correccion } from '@/types/correccion';
import { X, Calendar, User, AlertCircle, Clock, CheckCircle, XCircle, TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface CorreccionDetailProps {
  correccion: Correccion;
  onClose: () => void;
  onEdit?: () => void;
}

export default function CorreccionDetail({ correccion, onClose, onEdit }: CorreccionDetailProps) {
  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'PENDIENTE':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'EN_REVISION':
        return <AlertCircle className="h-5 w-5 text-blue-500" />;
      case 'COMPLETADA':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'RECHAZADA':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Minus className="h-5 w-5 text-gray-500" />;
    }
  };

  const getPrioridadIcon = (prioridad: string) => {
    switch (prioridad) {
      case 'URGENTE':
        return <TrendingUp className="h-5 w-5 text-red-500" />;
      case 'ALTA':
        return <TrendingUp className="h-5 w-5 text-orange-500" />;
      case 'MEDIA':
        return <Minus className="h-5 w-5 text-yellow-500" />;
      case 'BAJA':
        return <TrendingDown className="h-5 w-5 text-green-500" />;
      default:
        return <Minus className="h-5 w-5 text-gray-500" />;
    }
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'PENDIENTE':
        return 'bg-yellow-100 text-yellow-800';
      case 'EN_REVISION':
        return 'bg-blue-100 text-blue-800';
      case 'COMPLETADA':
        return 'bg-green-100 text-green-800';
      case 'RECHAZADA':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPrioridadColor = (prioridad: string) => {
    switch (prioridad) {
      case 'URGENTE':
        return 'bg-red-100 text-red-800';
      case 'ALTA':
        return 'bg-orange-100 text-orange-800';
      case 'MEDIA':
        return 'bg-yellow-100 text-yellow-800';
      case 'BAJA':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-6 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {correccion.titulo}
            </h3>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {new Date(correccion.fechaPublicacion).toLocaleDateString()}
              </div>
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                {correccion.user?.name || 'N/A'}
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Estado y Prioridad */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getEstadoIcon(correccion.estado)}
                <span className="font-medium text-gray-900">Estado</span>
              </div>
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getEstadoColor(correccion.estado)}`}>
                {correccion.estado.replace('_', ' ')}
              </span>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getPrioridadIcon(correccion.prioridad)}
                <span className="font-medium text-gray-900">Prioridad</span>
              </div>
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getPrioridadColor(correccion.prioridad)}`}>
                {correccion.prioridad}
              </span>
            </div>
          </div>

          {/* Medio */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Medio de Comunicación</h4>
            <p className="text-gray-700 bg-gray-50 p-3 rounded-md">
              {correccion.medio}
            </p>
          </div>

          {/* Contenido */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Descripción de la Corrección</h4>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-gray-700 whitespace-pre-wrap">
                {correccion.contenido}
              </p>
            </div>
          </div>

          {/* Observaciones */}
          {correccion.observaciones && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Observaciones</h4>
              <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
                <p className="text-gray-700 whitespace-pre-wrap">
                  {correccion.observaciones}
                </p>
              </div>
            </div>
          )}

          {/* Información adicional */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Fecha de Corrección</h4>
              <p className="text-gray-700">
                {new Date(correccion.fechaCorreccion).toLocaleDateString()}
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Creado</h4>
              <p className="text-gray-700">
                {new Date(correccion.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Imagen si existe */}
          {correccion.imagenUrl && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Imagen Adjunta</h4>
              <div className="bg-gray-50 p-4 rounded-md">
                <img
                  src={correccion.imagenUrl}
                  alt="Imagen de la corrección"
                  className="max-w-full h-auto rounded-md"
                />
              </div>
            </div>
          )}

          {/* Botones de acción */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cerrar
            </button>
            {onEdit && (
              <button
                onClick={onEdit}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                Editar
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 