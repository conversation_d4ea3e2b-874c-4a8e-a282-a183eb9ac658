/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/noticias/stats/route";
exports.ids = ["app/api/noticias/stats/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2Fstats%2Froute&page=%2Fapi%2Fnoticias%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2Fstats%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2Fstats%2Froute&page=%2Fapi%2Fnoticias%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2Fstats%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_DEL_SUR_FINAL_project_bolt_sb1_mtrb5vnz_panel_unificado_v2_src_app_api_noticias_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/noticias/stats/route.ts */ \"(rsc)/./src/app/api/noticias/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/noticias/stats/route\",\n        pathname: \"/api/noticias/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/noticias/stats/route\"\n    },\n    resolvedPagePath: \"G:\\\\DEL SUR FINAL\\\\project-bolt-sb1-mtrb5vnz\\\\panel-unificado-v2\\\\src\\\\app\\\\api\\\\noticias\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_DEL_SUR_FINAL_project_bolt_sb1_mtrb5vnz_panel_unificado_v2_src_app_api_noticias_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2Fstats%2Froute&page=%2Fapi%2Fnoticias%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2Fstats%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/noticias/stats/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/noticias/stats/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n// GET /api/noticias/stats - Obtener estadísticas de noticias\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        // Obtener estadísticas generales\n        const [totalNoticias, noticiasPublicadas, noticiasBorrador, noticiasEnRevision, noticiasAprobadas, noticiasDestacadas, noticiasPorCategoria] = await Promise.all([\n            // Total de noticias\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.count(),\n            // Noticias publicadas\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.count({\n                where: {\n                    publicada: true\n                }\n            }),\n            // Noticias en borrador\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.count({\n                where: {\n                    estado: 'BORRADOR'\n                }\n            }),\n            // Noticias en revisión\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.count({\n                where: {\n                    estado: 'EN_REVISION'\n                }\n            }),\n            // Noticias aprobadas\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.count({\n                where: {\n                    estado: 'APROBADA'\n                }\n            }),\n            // Noticias destacadas\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.count({\n                where: {\n                    destacada: true\n                }\n            }),\n            // Noticias por categoría\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.groupBy({\n                by: [\n                    'categoriaId'\n                ],\n                _count: {\n                    categoriaId: true\n                },\n                where: {\n                    categoria: {\n                        isActive: true\n                    }\n                }\n            })\n        ]);\n        // Obtener nombres de categorías para las estadísticas\n        const categorias = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.categoria.findMany({\n            where: {\n                isActive: true\n            },\n            select: {\n                id: true,\n                nombre: true,\n                color: true\n            }\n        });\n        // Mapear estadísticas por categoría con nombres\n        const noticiasPorCategoriaConNombres = noticiasPorCategoria.map((stat)=>{\n            const categoria = categorias.find((c)=>c.id === stat.categoriaId);\n            return {\n                categoriaId: stat.categoriaId,\n                categoriaNombre: categoria?.nombre || 'Sin categoría',\n                categoriaColor: categoria?.color || '#6B7280',\n                count: stat._count.categoriaId\n            };\n        });\n        // Noticias recientes (últimas 5)\n        const noticiasRecientes = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.noticia.findMany({\n            take: 5,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                categoria: {\n                    select: {\n                        nombre: true,\n                        color: true\n                    }\n                },\n                user: {\n                    select: {\n                        name: true\n                    }\n                }\n            }\n        });\n        const stats = {\n            total: totalNoticias,\n            publicadas: noticiasPublicadas,\n            borrador: noticiasBorrador,\n            enRevision: noticiasEnRevision,\n            aprobadas: noticiasAprobadas,\n            destacadas: noticiasDestacadas,\n            porCategoria: noticiasPorCategoriaConNombres,\n            recientes: noticiasRecientes\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(stats);\n    } catch (error) {\n        console.error('Error al obtener estadísticas:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/noticias/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const user = await prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        }\n                    });\n                    if (!user) {\n                        return null;\n                    }\n                    const isPasswordValid = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        return null;\n                    }\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error during authentication:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    },\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkc6XFxERUwgU1VSIEZJTkFMXFxwcm9qZWN0LWJvbHQtc2IxLW10cmI1dm56XFxwYW5lbC11bmlmaWNhZG8tdjJcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcclxuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZDtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hOyAiXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@babel+runtime@7.27.6","vendor-chunks/jose@4.15.9","vendor-chunks/openid-client@5.7.1","vendor-chunks/oauth@0.9.15","vendor-chunks/object-hash@2.2.0","vendor-chunks/preact@10.26.9","vendor-chunks/uuid@8.3.2","vendor-chunks/yallist@4.0.0","vendor-chunks/preact-render-to-string@5.2.6_preact@10.26.9","vendor-chunks/lru-cache@6.0.0","vendor-chunks/cookie@0.7.2","vendor-chunks/oidc-token-hash@5.1.0","vendor-chunks/@panva+hkdf@1.2.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnoticias%2Fstats%2Froute&page=%2Fapi%2Fnoticias%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnoticias%2Fstats%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cproject-bolt-sb1-mtrb5vnz%5Cpanel-unificado-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();