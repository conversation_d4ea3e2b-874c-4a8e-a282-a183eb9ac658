{"pages": {"/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/admin/ai-config/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/admin/ai-config/page.js"], "/api/auth/[...nextauth]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/auth/[...nextauth]/route.js"], "/api/admin/ai-config/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/admin/ai-config/route.js"], "/auth/signin/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/auth/signin/page.js"], "/dashboard/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/page.js"], "/api/noticias/stats/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/stats/route.js"], "/api/admin/users/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/admin/users/route.js"], "/admin/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/admin/page.js"], "/api/admin/ai-config/test/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/admin/ai-config/test/route.js"], "/noticias/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/page.js"], "/api/categorias/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/categorias/route.js"], "/api/noticias/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/route.js"], "/noticias/[id]/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/[id]/page.js"], "/api/diarios/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/diarios/route.js"], "/api/noticias/[id]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/route.js"], "/api/noticias/[id]/generate-versions/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/generate-versions/route.js"], "/api/noticias/[id]/versions/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/versions/route.js"], "/noticias/[id]/revision/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/[id]/revision/page.js"], "/noticias/nueva/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/nueva/page.js"], "/admin/diarios/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/admin/diarios/page.js"], "/api/admin/diarios-config/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/admin/diarios-config/route.js"]}}