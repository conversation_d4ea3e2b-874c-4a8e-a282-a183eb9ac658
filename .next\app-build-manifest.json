{"pages": {"/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/api/auth/[...nextauth]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/auth/[...nextauth]/route.js"], "/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/page.js"], "/dashboard/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/page.js"], "/api/noticias/stats/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/stats/route.js"], "/api/admin/users/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/admin/users/route.js"], "/noticias/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/page.js"], "/api/categorias/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/categorias/route.js"], "/api/noticias/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/route.js"], "/noticias/nueva/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/nueva/page.js"], "/noticias/[id]/editar/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/[id]/editar/page.js"], "/api/noticias/[id]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/route.js"], "/noticias/[id]/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/[id]/page.js"]}}