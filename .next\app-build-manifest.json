{"pages": {"/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/noticias/[id]/revision/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/[id]/revision/page.js"], "/api/auth/[...nextauth]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/auth/[...nextauth]/route.js"], "/api/noticias/[id]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/route.js"], "/api/noticias/[id]/versions/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/versions/route.js"], "/api/diarios/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/diarios/route.js"], "/noticias/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/page.js"], "/api/categorias/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/categorias/route.js"], "/api/noticias/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/route.js"], "/api/noticias/[id]/versions/[versionId]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/versions/[versionId]/route.js"], "/auth/signin/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/auth/signin/page.js"], "/dashboard/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/page.js"], "/api/noticias/stats/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/stats/route.js"], "/api/admin/users/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/admin/users/route.js"], "/noticias/[id]/editar/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/[id]/editar/page.js"], "/admin/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/admin/page.js"], "/admin/diarios/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/admin/diarios/page.js"], "/api/admin/diarios-config/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/admin/diarios-config/route.js"], "/admin/usuarios/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/admin/usuarios/page.js"], "/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/page.js"], "/admin/ai-config/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/admin/ai-config/page.js"], "/api/admin/ai-config/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/admin/ai-config/route.js"], "/api/admin/ai-config/test/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/admin/ai-config/test/route.js"]}}