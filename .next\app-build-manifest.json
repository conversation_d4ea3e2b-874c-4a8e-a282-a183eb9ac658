{"pages": {"/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/noticias/[id]/revision/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/[id]/revision/page.js"], "/api/auth/[...nextauth]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/auth/[...nextauth]/route.js"], "/api/noticias/[id]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/route.js"], "/api/noticias/[id]/versions/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/versions/route.js"], "/api/diarios/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/diarios/route.js"], "/noticias/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/noticias/page.js"], "/api/categorias/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/categorias/route.js"], "/api/noticias/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/route.js"], "/api/noticias/[id]/versions/[versionId]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/noticias/[id]/versions/[versionId]/route.js"], "/_not-found/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/_not-found/page.js"]}}