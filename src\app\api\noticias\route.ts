import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/noticias - Obtener todas las noticias
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const categoria = searchParams.get('categoria');
    const estado = searchParams.get('estado');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Construir filtros
    const where: any = {};
    
    if (categoria) {
      where.categoriaId = parseInt(categoria);
    }
    
    if (estado) {
      where.estado = estado;
    }
    
    if (search) {
      where.OR = [
        { titulo: { contains: search, mode: 'insensitive' } },
        { volanta: { contains: search, mode: 'insensitive' } },
        { contenido: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Obtener noticias con relaciones incluyendo versiones de IA
    const noticias = await prisma.noticia.findMany({
      where,
      include: {
        categoria: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        versiones: {
          include: {
            diario: {
              select: {
                id: true,
                nombre: true,
                descripcion: true,
              },
            },
            usuario: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    // Contar total para paginación
    const total = await prisma.noticia.count({ where });

    return NextResponse.json({
      noticias,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error al obtener noticias:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/noticias - Crear nueva noticia
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('Session user ID:', session.user.id, 'Type:', typeof session.user.id);

    // Verificar si es FormData o JSON
    const contentType = request.headers.get('content-type');
    
    let formData: any = {};
    
    if (contentType?.includes('multipart/form-data')) {
      // Manejar FormData
      const formDataObj = await request.formData();
      formData = {
        volanta: formDataObj.get('volanta') as string,
        titulo: formDataObj.get('titulo') as string,
        resumen: formDataObj.get('resumen') as string,
        contenido: formDataObj.get('contenido') as string,
        imagenUrl: formDataObj.get('imagenUrl') as string,
        categoriaId: formDataObj.get('categoriaId') as string,
      };
    } else {
      // Manejar JSON
      formData = await request.json();
    }

    console.log('Form data received:', formData);

    const {
      volanta,
      titulo,
      resumen,
      contenido,
      imagenUrl,
      categoriaId,
    } = formData;

    // Validaciones básicas
    if (!volanta || !titulo || !contenido) {
      return NextResponse.json(
        { error: 'Volanta, título y contenido son requeridos' },
        { status: 400 }
      );
    }

    // Verificar que el usuario existe
    const user = await prisma.user.findUnique({
      where: { id: parseInt(session.user.id) }
    });

    if (!user) {
      console.log('User not found with ID:', session.user.id);
      return NextResponse.json(
        { error: 'Usuario no encontrado. Por favor, cierre sesión e inicie sesión nuevamente.' },
        { status: 401 }
      );
    }

    console.log('User found:', user.id, user.email);

    // Verificar que la categoría existe si se proporciona
    if (categoriaId) {
      const categoria = await prisma.categoria.findUnique({
        where: { id: parseInt(categoriaId) }
      });

      if (!categoria) {
        console.log('Category not found with ID:', categoriaId);
        return NextResponse.json(
          { error: 'Categoría no encontrada' },
          { status: 404 }
        );
      }

      console.log('Category found:', categoria.id, categoria.nombre);
    }

    const dataToCreate = {
      volanta,
      titulo,
      resumen,
      contenido,
      imagenUrl,
      categoriaId: categoriaId ? parseInt(categoriaId) : null,
      estado: 'BORRADOR' as const,
      userId: parseInt(session.user.id),
    };

    console.log('Data to create:', dataToCreate);

    // Crear la noticia
    const noticia = await prisma.noticia.create({
      data: dataToCreate,
      include: {
        categoria: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('Noticia created successfully:', noticia.id);

    return NextResponse.json(noticia, { status: 201 });
  } catch (error) {
    console.error('Error al crear noticia:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 