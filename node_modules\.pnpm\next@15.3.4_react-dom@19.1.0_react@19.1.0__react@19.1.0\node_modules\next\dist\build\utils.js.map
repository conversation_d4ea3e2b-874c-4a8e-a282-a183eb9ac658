{"version": 3, "sources": ["../../src/build/utils.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../server/config-shared'\nimport type { ExperimentalPPRConfig } from '../server/lib/experimental/ppr'\nimport type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { AssetBinding } from './webpack/loaders/get-module-build-info'\nimport type { PageConfig, ServerRuntime } from '../types'\nimport type { BuildManifest } from '../server/get-page-files'\nimport type {\n  Redirect,\n  Rewrite,\n  Header,\n  CustomRoutes,\n} from '../lib/load-custom-routes'\nimport type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from './webpack/plugins/middleware-plugin'\nimport type { WebpackLayerName } from '../lib/constants'\nimport type { AppPageModule } from '../server/route-modules/app-page/module'\nimport type { RouteModule } from '../server/route-modules/route-module'\nimport type { NextComponentType } from '../shared/lib/utils'\n\nimport '../server/require-hook'\nimport '../server/node-polyfill-crypto'\nimport '../server/node-environment'\n\nimport {\n  green,\n  yellow,\n  red,\n  cyan,\n  white,\n  bold,\n  underline,\n} from '../lib/picocolors'\nimport getGzipSize from 'next/dist/compiled/gzip-size'\nimport textTable from 'next/dist/compiled/text-table'\nimport path from 'path'\nimport { promises as fs } from 'fs'\nimport { isValidElementType } from 'next/dist/compiled/react-is'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport browserslist from 'next/dist/compiled/browserslist'\nimport {\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  INSTRUMENTATION_HOOK_FILENAME,\n  WEBPACK_LAYERS,\n} from '../lib/constants'\nimport {\n  MODERN_BROWSERSLIST_TARGET,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n} from '../shared/lib/constants'\nimport prettyBytes from '../lib/pretty-bytes'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { findPageFile } from '../server/lib/find-page-file'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport * as Log from './output/log'\nimport { loadComponents } from '../server/load-components'\nimport type { LoadComponentsReturnType } from '../server/load-components'\nimport { trace } from '../trace'\nimport { setHttpClientAndAgentOptions } from '../server/setup-http-agent-env'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getRuntimeContext } from '../server/web/sandbox'\nimport { isClientReference } from '../lib/client-and-server-references'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { denormalizeAppPagePath } from '../shared/lib/page-path/denormalize-app-path'\nimport { RouteKind } from '../server/route-kind'\nimport type { PageExtensions } from './page-extensions-type'\nimport { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes'\nimport { checkIsRoutePPREnabled } from '../server/lib/experimental/ppr'\nimport type { FallbackMode } from '../lib/fallback'\nimport type { OutgoingHttpHeaders } from 'http'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport type { AppSegment } from './segment-config/app/app-segments'\nimport { collectSegments } from './segment-config/app/app-segments'\nimport { createIncrementalCache } from '../export/helpers/create-incremental-cache'\nimport { collectRootParamKeys } from './segment-config/app/collect-root-param-keys'\nimport { buildAppStaticPaths } from './static-paths/app'\nimport { buildPagesStaticPaths } from './static-paths/pages'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { CacheControl } from '../server/lib/cache-control'\nimport { formatExpire, formatRevalidate } from './output/format'\n\nexport type ROUTER_TYPE = 'pages' | 'app'\n\n// Use `print()` for expected console output\nconst print = console.log\n\nconst RESERVED_PAGE = /^\\/(_app|_error|_document|api(\\/|$))/\nconst fileGzipStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStatGzip = (file: string) => {\n  const cached = fileGzipStats[file]\n  if (cached) return cached\n  return (fileGzipStats[file] = getGzipSize.file(file))\n}\n\nconst fileSize = async (file: string) => (await fs.stat(file)).size\n\nconst fileStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStat = (file: string) => {\n  const cached = fileStats[file]\n  if (cached) return cached\n  return (fileStats[file] = fileSize(file))\n}\n\nexport function unique<T>(main: ReadonlyArray<T>, sub: ReadonlyArray<T>): T[] {\n  return [...new Set([...main, ...sub])]\n}\n\nexport function difference<T>(\n  main: ReadonlyArray<T> | ReadonlySet<T>,\n  sub: ReadonlyArray<T> | ReadonlySet<T>\n): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...a].filter((x) => !b.has(x))\n}\n\n/**\n * Return an array of the items shared by both arrays.\n */\nfunction intersect<T>(main: ReadonlyArray<T>, sub: ReadonlyArray<T>): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...new Set([...a].filter((x) => b.has(x)))]\n}\n\nfunction sum(a: ReadonlyArray<number>): number {\n  return a.reduce((size, stat) => size + stat, 0)\n}\n\ntype ComputeFilesGroup = {\n  files: ReadonlyArray<string>\n  size: {\n    total: number\n  }\n}\n\ntype ComputeFilesManifest = {\n  unique: ComputeFilesGroup\n  common: ComputeFilesGroup\n}\n\ntype ComputeFilesManifestResult = {\n  router: {\n    pages: ComputeFilesManifest\n    app?: ComputeFilesManifest\n  }\n  sizes: Map<string, number>\n}\n\nlet cachedBuildManifest: BuildManifest | undefined\nlet cachedAppBuildManifest: AppBuildManifest | undefined\n\nlet lastCompute: ComputeFilesManifestResult | undefined\nlet lastComputePageInfo: boolean | undefined\n\nexport async function computeFromManifest(\n  manifests: {\n    build: BuildManifest\n    app?: AppBuildManifest\n  },\n  distPath: string,\n  gzipSize: boolean = true,\n  pageInfos?: Map<string, PageInfo>\n): Promise<ComputeFilesManifestResult> {\n  if (\n    Object.is(cachedBuildManifest, manifests.build) &&\n    lastComputePageInfo === !!pageInfos &&\n    Object.is(cachedAppBuildManifest, manifests.app)\n  ) {\n    return lastCompute!\n  }\n\n  // Determine the files that are in pages and app and count them, this will\n  // tell us if they are unique or common.\n\n  const countBuildFiles = (\n    map: Map<string, number>,\n    key: string,\n    manifest: Record<string, ReadonlyArray<string>>\n  ) => {\n    for (const file of manifest[key]) {\n      if (key === '/_app') {\n        map.set(file, Infinity)\n      } else if (map.has(file)) {\n        map.set(file, map.get(file)! + 1)\n      } else {\n        map.set(file, 1)\n      }\n    }\n  }\n\n  const files: {\n    pages: {\n      each: Map<string, number>\n      expected: number\n    }\n    app?: {\n      each: Map<string, number>\n      expected: number\n    }\n  } = {\n    pages: { each: new Map(), expected: 0 },\n  }\n\n  for (const key in manifests.build.pages) {\n    if (pageInfos) {\n      const pageInfo = pageInfos.get(key)\n      // don't include AMP pages since they don't rely on shared bundles\n      // AMP First pages are not under the pageInfos key\n      if (pageInfo?.isHybridAmp) {\n        continue\n      }\n    }\n\n    files.pages.expected++\n    countBuildFiles(files.pages.each, key, manifests.build.pages)\n  }\n\n  // Collect the build files form the app manifest.\n  if (manifests.app?.pages) {\n    files.app = { each: new Map<string, number>(), expected: 0 }\n\n    for (const key in manifests.app.pages) {\n      files.app.expected++\n      countBuildFiles(files.app.each, key, manifests.app.pages)\n    }\n  }\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n  const stats = new Map<string, number>()\n\n  // For all of the files in the pages and app manifests, compute the file size\n  // at once.\n\n  await Promise.all(\n    [\n      ...new Set<string>([\n        ...files.pages.each.keys(),\n        ...(files.app?.each.keys() ?? []),\n      ]),\n    ].map(async (f) => {\n      try {\n        // Add the file size to the stats.\n        stats.set(f, await getSize(path.join(distPath, f)))\n      } catch {}\n    })\n  )\n\n  const groupFiles = async (listing: {\n    each: Map<string, number>\n    expected: number\n  }): Promise<ComputeFilesManifest> => {\n    const entries = [...listing.each.entries()]\n\n    const shapeGroup = (group: [string, number][]): ComputeFilesGroup =>\n      group.reduce(\n        (acc, [f]) => {\n          acc.files.push(f)\n\n          const size = stats.get(f)\n          if (typeof size === 'number') {\n            acc.size.total += size\n          }\n\n          return acc\n        },\n        {\n          files: [] as string[],\n          size: {\n            total: 0,\n          },\n        }\n      )\n\n    return {\n      unique: shapeGroup(entries.filter(([, len]) => len === 1)),\n      common: shapeGroup(\n        entries.filter(\n          ([, len]) => len === listing.expected || len === Infinity\n        )\n      ),\n    }\n  }\n\n  lastCompute = {\n    router: {\n      pages: await groupFiles(files.pages),\n      app: files.app ? await groupFiles(files.app) : undefined,\n    },\n    sizes: stats,\n  }\n\n  cachedBuildManifest = manifests.build\n  cachedAppBuildManifest = manifests.app\n  lastComputePageInfo = !!pageInfos\n  return lastCompute!\n}\n\nexport function isMiddlewareFilename(file?: string | null) {\n  return file === MIDDLEWARE_FILENAME || file === `src/${MIDDLEWARE_FILENAME}`\n}\n\nexport function isInstrumentationHookFilename(file?: string | null) {\n  return (\n    file === INSTRUMENTATION_HOOK_FILENAME ||\n    file === `src/${INSTRUMENTATION_HOOK_FILENAME}`\n  )\n}\n\nconst filterAndSortList = (\n  list: ReadonlyArray<string>,\n  routeType: ROUTER_TYPE,\n  hasCustomApp: boolean\n) => {\n  let pages: string[]\n  if (routeType === 'app') {\n    // filter out static app route of /favicon.ico\n    pages = list.filter((e) => e !== '/favicon.ico')\n  } else {\n    // filter built-in pages\n    pages = list\n      .slice()\n      .filter(\n        (e) =>\n          !(\n            e === '/_document' ||\n            e === '/_error' ||\n            (!hasCustomApp && e === '/_app')\n          )\n      )\n  }\n  return pages.sort((a, b) => a.localeCompare(b))\n}\n\nexport interface PageInfo {\n  isHybridAmp?: boolean\n  size: number\n  totalSize: number\n  isStatic: boolean\n  isSSG: boolean\n  /**\n   * If true, it means that the route has partial prerendering enabled.\n   */\n  isRoutePPREnabled: boolean\n  ssgPageRoutes: string[] | null\n  initialCacheControl: CacheControl | undefined\n  pageDuration: number | undefined\n  ssgPageDurations: number[] | undefined\n  runtime: ServerRuntime\n  hasEmptyPrelude?: boolean\n  hasPostponed?: boolean\n  isDynamicAppRoute?: boolean\n}\n\nexport type PageInfos = Map<string, PageInfo>\n\nexport interface RoutesUsingEdgeRuntime {\n  [route: string]: 0\n}\n\nexport function collectRoutesUsingEdgeRuntime(\n  input: PageInfos\n): RoutesUsingEdgeRuntime {\n  const routesUsingEdgeRuntime: RoutesUsingEdgeRuntime = {}\n  for (const [route, info] of input.entries()) {\n    if (isEdgeRuntime(info.runtime)) {\n      routesUsingEdgeRuntime[route] = 0\n    }\n  }\n\n  return routesUsingEdgeRuntime\n}\n\nexport async function printTreeView(\n  lists: {\n    pages: ReadonlyArray<string>\n    app: ReadonlyArray<string> | undefined\n  },\n  pageInfos: Map<string, PageInfo>,\n  {\n    distPath,\n    buildId,\n    pagesDir,\n    pageExtensions,\n    buildManifest,\n    appBuildManifest,\n    middlewareManifest,\n    useStaticPages404,\n    gzipSize = true,\n  }: {\n    distPath: string\n    buildId: string\n    pagesDir?: string\n    pageExtensions: PageExtensions\n    buildManifest: BuildManifest\n    appBuildManifest?: AppBuildManifest\n    middlewareManifest: MiddlewareManifest\n    useStaticPages404: boolean\n    gzipSize?: boolean\n  }\n) {\n  const getPrettySize = (\n    _size: number,\n    { strong }: { strong?: boolean } = {}\n  ): string => {\n    const size = process.env.__NEXT_PRIVATE_DETERMINISTIC_BUILD_OUTPUT\n      ? 'N/A kB'\n      : prettyBytes(_size)\n\n    return strong ? white(bold(size)) : size\n  }\n\n  // Can be overridden for test purposes to omit the build duration output.\n  const MIN_DURATION = process.env.__NEXT_PRIVATE_DETERMINISTIC_BUILD_OUTPUT\n    ? Infinity // Don't ever log build durations.\n    : 300\n\n  const getPrettyDuration = (_duration: number): string => {\n    const duration = `${_duration} ms`\n    // green for 300-1000ms\n    if (_duration < 1000) return green(duration)\n    // yellow for 1000-2000ms\n    if (_duration < 2000) return yellow(duration)\n    // red for >= 2000ms\n    return red(bold(duration))\n  }\n\n  const getCleanName = (fileName: string) =>\n    fileName\n      // Trim off `static/`\n      .replace(/^static\\//, '')\n      // Re-add `static/` for root files\n      .replace(/^<buildId>/, 'static')\n      // Remove file hash\n      .replace(/(?:^|[.-])([0-9a-z]{6})[0-9a-z]{14}(?=\\.)/, '.$1')\n\n  // Check if we have a custom app.\n  const hasCustomApp = !!(\n    pagesDir && (await findPageFile(pagesDir, '/_app', pageExtensions, false))\n  )\n\n  // Collect all the symbols we use so we can print the icons out.\n  const usedSymbols = new Set()\n\n  const messages: [string, string, string, string, string][] = []\n\n  const stats = await computeFromManifest(\n    { build: buildManifest, app: appBuildManifest },\n    distPath,\n    gzipSize,\n    pageInfos\n  )\n\n  const printFileTree = async ({\n    list,\n    routerType,\n  }: {\n    list: ReadonlyArray<string>\n    routerType: ROUTER_TYPE\n  }) => {\n    const filteredPages = filterAndSortList(list, routerType, hasCustomApp)\n    if (filteredPages.length === 0) {\n      return\n    }\n\n    let showRevalidate = false\n    let showExpire = false\n\n    for (const page of filteredPages) {\n      const cacheControl = pageInfos.get(page)?.initialCacheControl\n\n      if (cacheControl?.revalidate) {\n        showRevalidate = true\n      }\n\n      if (cacheControl?.expire) {\n        showExpire = true\n      }\n\n      if (showRevalidate && showExpire) {\n        break\n      }\n    }\n\n    messages.push(\n      [\n        routerType === 'app' ? 'Route (app)' : 'Route (pages)',\n        'Size',\n        'First Load JS',\n        showRevalidate ? 'Revalidate' : '',\n        showExpire ? 'Expire' : '',\n      ].map((entry) => underline(entry)) as [\n        string,\n        string,\n        string,\n        string,\n        string,\n      ]\n    )\n\n    filteredPages.forEach((item, i, arr) => {\n      const border =\n        i === 0\n          ? arr.length === 1\n            ? '─'\n            : '┌'\n          : i === arr.length - 1\n            ? '└'\n            : '├'\n\n      const pageInfo = pageInfos.get(item)\n      const ampFirst = buildManifest.ampFirstPages.includes(item)\n      const totalDuration =\n        (pageInfo?.pageDuration || 0) +\n        (pageInfo?.ssgPageDurations?.reduce((a, b) => a + (b || 0), 0) || 0)\n\n      let symbol: string\n\n      if (item === '/_app' || item === '/_app.server') {\n        symbol = ' '\n      } else if (isEdgeRuntime(pageInfo?.runtime)) {\n        symbol = 'ƒ'\n      } else if (pageInfo?.isRoutePPREnabled) {\n        if (\n          // If the page has an empty prelude, then it's equivalent to a dynamic page\n          pageInfo?.hasEmptyPrelude ||\n          // ensure we don't mark dynamic paths that postponed as being dynamic\n          // since in this case we're able to partially prerender it\n          (pageInfo.isDynamicAppRoute && !pageInfo.hasPostponed)\n        ) {\n          symbol = 'ƒ'\n        } else if (!pageInfo?.hasPostponed) {\n          symbol = '○'\n        } else {\n          symbol = '◐'\n        }\n      } else if (pageInfo?.isStatic) {\n        symbol = '○'\n      } else if (pageInfo?.isSSG) {\n        symbol = '●'\n      } else {\n        symbol = 'ƒ'\n      }\n\n      usedSymbols.add(symbol)\n\n      messages.push([\n        `${border} ${symbol} ${item}${\n          totalDuration > MIN_DURATION\n            ? ` (${getPrettyDuration(totalDuration)})`\n            : ''\n        }`,\n        pageInfo\n          ? ampFirst\n            ? cyan('AMP')\n            : pageInfo.size >= 0\n              ? getPrettySize(pageInfo.size)\n              : ''\n          : '',\n        pageInfo\n          ? ampFirst\n            ? cyan('AMP')\n            : pageInfo.size >= 0\n              ? getPrettySize(pageInfo.totalSize, { strong: true })\n              : ''\n          : '',\n        showRevalidate && pageInfo?.initialCacheControl\n          ? formatRevalidate(pageInfo.initialCacheControl)\n          : '',\n        showExpire && pageInfo?.initialCacheControl\n          ? formatExpire(pageInfo.initialCacheControl)\n          : '',\n      ])\n\n      const uniqueCssFiles =\n        buildManifest.pages[item]?.filter(\n          (file) =>\n            file.endsWith('.css') &&\n            stats.router[routerType]?.unique.files.includes(file)\n        ) || []\n\n      if (uniqueCssFiles.length > 0) {\n        const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n        uniqueCssFiles.forEach((file, index, { length }) => {\n          const innerSymbol = index === length - 1 ? '└' : '├'\n          const size = stats.sizes.get(file)\n          messages.push([\n            `${contSymbol}   ${innerSymbol} ${getCleanName(file)}`,\n            typeof size === 'number' ? getPrettySize(size) : '',\n            '',\n            '',\n            '',\n          ])\n        })\n      }\n\n      if (pageInfo?.ssgPageRoutes?.length) {\n        const totalRoutes = pageInfo.ssgPageRoutes.length\n        const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n        let routes: { route: string; duration: number; avgDuration?: number }[]\n        if (\n          pageInfo.ssgPageDurations &&\n          pageInfo.ssgPageDurations.some((d) => d > MIN_DURATION)\n        ) {\n          const previewPages = totalRoutes === 8 ? 8 : Math.min(totalRoutes, 7)\n          const routesWithDuration = pageInfo.ssgPageRoutes\n            .map((route, idx) => ({\n              route,\n              duration: pageInfo.ssgPageDurations![idx] || 0,\n            }))\n            .sort(({ duration: a }, { duration: b }) =>\n              // Sort by duration\n              // keep too small durations in original order at the end\n              a <= MIN_DURATION && b <= MIN_DURATION ? 0 : b - a\n            )\n          routes = routesWithDuration.slice(0, previewPages)\n          const remainingRoutes = routesWithDuration.slice(previewPages)\n          if (remainingRoutes.length) {\n            const remaining = remainingRoutes.length\n            const avgDuration = Math.round(\n              remainingRoutes.reduce(\n                (total, { duration }) => total + duration,\n                0\n              ) / remainingRoutes.length\n            )\n            routes.push({\n              route: `[+${remaining} more paths]`,\n              duration: 0,\n              avgDuration,\n            })\n          }\n        } else {\n          const previewPages = totalRoutes === 4 ? 4 : Math.min(totalRoutes, 3)\n          routes = pageInfo.ssgPageRoutes\n            .slice(0, previewPages)\n            .map((route) => ({ route, duration: 0 }))\n          if (totalRoutes > previewPages) {\n            const remaining = totalRoutes - previewPages\n            routes.push({ route: `[+${remaining} more paths]`, duration: 0 })\n          }\n        }\n\n        routes.forEach(\n          ({ route, duration, avgDuration }, index, { length }) => {\n            const innerSymbol = index === length - 1 ? '└' : '├'\n\n            const initialCacheControl =\n              pageInfos.get(route)?.initialCacheControl\n\n            messages.push([\n              `${contSymbol}   ${innerSymbol} ${route}${\n                duration > MIN_DURATION\n                  ? ` (${getPrettyDuration(duration)})`\n                  : ''\n              }${\n                avgDuration && avgDuration > MIN_DURATION\n                  ? ` (avg ${getPrettyDuration(avgDuration)})`\n                  : ''\n              }`,\n              '',\n              '',\n              showRevalidate && initialCacheControl\n                ? formatRevalidate(initialCacheControl)\n                : '',\n              showExpire && initialCacheControl\n                ? formatExpire(initialCacheControl)\n                : '',\n            ])\n          }\n        )\n      }\n    })\n\n    const sharedFilesSize = stats.router[routerType]?.common.size.total\n\n    const sharedFiles = process.env.__NEXT_PRIVATE_DETERMINISTIC_BUILD_OUTPUT\n      ? []\n      : stats.router[routerType]?.common.files ?? []\n\n    messages.push([\n      '+ First Load JS shared by all',\n      typeof sharedFilesSize === 'number'\n        ? getPrettySize(sharedFilesSize, { strong: true })\n        : '',\n      '',\n      '',\n      '',\n    ])\n    const sharedCssFiles: string[] = []\n    const sharedJsChunks = [\n      ...sharedFiles\n        .filter((file) => {\n          if (file.endsWith('.css')) {\n            sharedCssFiles.push(file)\n            return false\n          }\n          return true\n        })\n        .map((e) => e.replace(buildId, '<buildId>'))\n        .sort(),\n      ...sharedCssFiles.map((e) => e.replace(buildId, '<buildId>')).sort(),\n    ]\n\n    // if the some chunk are less than 10kb or we don't know the size, we only show the total size of the rest\n    const tenKbLimit = 10 * 1000\n    let restChunkSize = 0\n    let restChunkCount = 0\n    sharedJsChunks.forEach((fileName, index, { length }) => {\n      const innerSymbol = index + restChunkCount === length - 1 ? '└' : '├'\n\n      const originalName = fileName.replace('<buildId>', buildId)\n      const cleanName = getCleanName(fileName)\n      const size = stats.sizes.get(originalName)\n\n      if (!size || size < tenKbLimit) {\n        restChunkCount++\n        restChunkSize += size || 0\n        return\n      }\n\n      messages.push([\n        `  ${innerSymbol} ${cleanName}`,\n        getPrettySize(size),\n        '',\n        '',\n        '',\n      ])\n    })\n\n    if (restChunkCount > 0) {\n      messages.push([\n        `  └ other shared chunks (total)`,\n        getPrettySize(restChunkSize),\n        '',\n        '',\n        '',\n      ])\n    }\n  }\n\n  // If enabled, then print the tree for the app directory.\n  if (lists.app && stats.router.app) {\n    await printFileTree({\n      routerType: 'app',\n      list: lists.app,\n    })\n\n    messages.push(['', '', '', '', ''])\n  }\n\n  pageInfos.set('/404', {\n    ...(pageInfos.get('/404') || pageInfos.get('/_error'))!,\n    isStatic: useStaticPages404,\n  })\n\n  // If there's no app /_notFound page present, then the 404 is still using the pages/404\n  if (\n    !lists.pages.includes('/404') &&\n    !lists.app?.includes(UNDERSCORE_NOT_FOUND_ROUTE)\n  ) {\n    lists.pages = [...lists.pages, '/404']\n  }\n\n  // Print the tree view for the pages directory.\n  await printFileTree({\n    routerType: 'pages',\n    list: lists.pages,\n  })\n\n  const middlewareInfo = middlewareManifest.middleware?.['/']\n  if (middlewareInfo?.files.length > 0) {\n    const middlewareSizes = await Promise.all(\n      middlewareInfo.files\n        .map((dep) => `${distPath}/${dep}`)\n        .map(gzipSize ? fsStatGzip : fsStat)\n    )\n\n    messages.push(['', '', '', '', ''])\n    messages.push([\n      'ƒ Middleware',\n      getPrettySize(sum(middlewareSizes), { strong: true }),\n      '',\n      '',\n      '',\n    ])\n  }\n\n  print(\n    textTable(messages, {\n      align: ['l', 'r', 'r', 'r', 'r'],\n      stringLength: (str) => stripAnsi(str).length,\n    })\n  )\n\n  const staticFunctionInfo =\n    lists.app && stats.router.app ? 'generateStaticParams' : 'getStaticProps'\n  print()\n  print(\n    textTable(\n      [\n        usedSymbols.has('○') && [\n          '○',\n          '(Static)',\n          'prerendered as static content',\n        ],\n        usedSymbols.has('●') && [\n          '●',\n          '(SSG)',\n          `prerendered as static HTML (uses ${cyan(staticFunctionInfo)})`,\n        ],\n        usedSymbols.has('◐') && [\n          '◐',\n          '(Partial Prerender)',\n          'prerendered as static HTML with dynamic server-streamed content',\n        ],\n        usedSymbols.has('ƒ') && ['ƒ', '(Dynamic)', `server-rendered on demand`],\n      ].filter((x) => x) as [string, string, string][],\n      {\n        align: ['l', 'l', 'l'],\n        stringLength: (str) => stripAnsi(str).length,\n      }\n    )\n  )\n\n  print()\n}\n\nexport function printCustomRoutes({\n  redirects,\n  rewrites,\n  headers,\n}: CustomRoutes) {\n  const printRoutes = (\n    routes: Redirect[] | Rewrite[] | Header[],\n    type: 'Redirects' | 'Rewrites' | 'Headers'\n  ) => {\n    const isRedirects = type === 'Redirects'\n    const isHeaders = type === 'Headers'\n    print(underline(type))\n\n    /*\n        ┌ source\n        ├ permanent/statusCode\n        └ destination\n     */\n    const routesStr = (routes as any[])\n      .map((route: { source: string }) => {\n        let routeStr = `┌ source: ${route.source}\\n`\n\n        if (!isHeaders) {\n          const r = route as Rewrite\n          routeStr += `${isRedirects ? '├' : '└'} destination: ${\n            r.destination\n          }\\n`\n        }\n        if (isRedirects) {\n          const r = route as Redirect\n          routeStr += `└ ${\n            r.statusCode\n              ? `status: ${r.statusCode}`\n              : `permanent: ${r.permanent}`\n          }\\n`\n        }\n\n        if (isHeaders) {\n          const r = route as Header\n          routeStr += `└ headers:\\n`\n\n          for (let i = 0; i < r.headers.length; i++) {\n            const header = r.headers[i]\n            const last = i === headers.length - 1\n\n            routeStr += `  ${last ? '└' : '├'} ${header.key}: ${header.value}\\n`\n          }\n        }\n\n        return routeStr\n      })\n      .join('\\n')\n\n    print(`${routesStr}\\n`)\n  }\n\n  print()\n  if (redirects.length) {\n    printRoutes(redirects, 'Redirects')\n  }\n  if (headers.length) {\n    printRoutes(headers, 'Headers')\n  }\n\n  const combinedRewrites = [\n    ...rewrites.beforeFiles,\n    ...rewrites.afterFiles,\n    ...rewrites.fallback,\n  ]\n  if (combinedRewrites.length) {\n    printRoutes(combinedRewrites, 'Rewrites')\n  }\n}\n\nexport async function getJsPageSizeInKb(\n  routerType: ROUTER_TYPE,\n  page: string,\n  distPath: string,\n  buildManifest: BuildManifest,\n  appBuildManifest?: AppBuildManifest,\n  gzipSize: boolean = true,\n  cachedStats?: ComputeFilesManifestResult\n): Promise<[number, number]> {\n  const pageManifest = routerType === 'pages' ? buildManifest : appBuildManifest\n  if (!pageManifest) {\n    throw new Error('expected appBuildManifest with an \"app\" pageType')\n  }\n\n  // Normalize appBuildManifest keys\n  if (routerType === 'app') {\n    pageManifest.pages = Object.entries(pageManifest.pages).reduce(\n      (acc: Record<string, string[]>, [key, value]) => {\n        const newKey = normalizeAppPath(key)\n        acc[newKey] = value as string[]\n        return acc\n      },\n      {}\n    )\n  }\n\n  // If stats was not provided, then compute it again.\n  const stats =\n    cachedStats ??\n    (await computeFromManifest(\n      { build: buildManifest, app: appBuildManifest },\n      distPath,\n      gzipSize\n    ))\n\n  const pageData = stats.router[routerType]\n  if (!pageData) {\n    // This error shouldn't happen and represents an error in Next.js.\n    throw new Error('expected \"app\" manifest data with an \"app\" pageType')\n  }\n\n  const pagePath =\n    routerType === 'pages'\n      ? denormalizePagePath(page)\n      : denormalizeAppPagePath(page)\n\n  const fnFilterJs = (entry: string) => entry.endsWith('.js')\n\n  const pageFiles = (pageManifest.pages[pagePath] ?? []).filter(fnFilterJs)\n  const appFiles = (pageManifest.pages['/_app'] ?? []).filter(fnFilterJs)\n\n  const fnMapRealPath = (dep: string) => `${distPath}/${dep}`\n\n  const allFilesReal = unique(pageFiles, appFiles).map(fnMapRealPath)\n  const selfFilesReal = difference(\n    // Find the files shared by the pages files and the unique files...\n    intersect(pageFiles, pageData.unique.files),\n    // but without the common files.\n    pageData.common.files\n  ).map(fnMapRealPath)\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n\n  // Try to get the file size from the page data if available, otherwise do a\n  // raw compute.\n  const getCachedSize = async (file: string) => {\n    const key = file.slice(distPath.length + 1)\n    const size: number | undefined = stats.sizes.get(key)\n\n    // If the size wasn't in the stats bundle, then get it from the file\n    // directly.\n    if (typeof size !== 'number') {\n      return getSize(file)\n    }\n\n    return size\n  }\n\n  try {\n    // Doesn't use `Promise.all`, as we'd double compute duplicate files. This\n    // function is memoized, so the second one will instantly resolve.\n    const allFilesSize = sum(await Promise.all(allFilesReal.map(getCachedSize)))\n    const selfFilesSize = sum(\n      await Promise.all(selfFilesReal.map(getCachedSize))\n    )\n\n    return [selfFilesSize, allFilesSize]\n  } catch {}\n  return [-1, -1]\n}\n\ntype PageIsStaticResult = {\n  isRoutePPREnabled?: boolean\n  isStatic?: boolean\n  isAmpOnly?: boolean\n  isHybridAmp?: boolean\n  hasServerProps?: boolean\n  hasStaticProps?: boolean\n  prerenderedRoutes: PrerenderedRoute[] | undefined\n  prerenderFallbackMode: FallbackMode | undefined\n  rootParamKeys: readonly string[] | undefined\n  isNextImageImported?: boolean\n  traceIncludes?: string[]\n  traceExcludes?: string[]\n  appConfig?: AppSegmentConfig\n}\n\nexport async function isPageStatic({\n  dir,\n  page,\n  distDir,\n  configFileName,\n  runtimeEnvConfig,\n  httpAgentOptions,\n  locales,\n  defaultLocale,\n  parentId,\n  pageRuntime,\n  edgeInfo,\n  pageType,\n  dynamicIO,\n  authInterrupts,\n  originalAppPath,\n  isrFlushToDisk,\n  maxMemoryCacheSize,\n  nextConfigOutput,\n  cacheHandler,\n  cacheHandlers,\n  cacheLifeProfiles,\n  pprConfig,\n  buildId,\n  sriEnabled,\n}: {\n  dir: string\n  page: string\n  distDir: string\n  dynamicIO: boolean\n  authInterrupts: boolean\n  configFileName: string\n  runtimeEnvConfig: any\n  httpAgentOptions: NextConfigComplete['httpAgentOptions']\n  locales?: readonly string[]\n  defaultLocale?: string\n  parentId?: any\n  edgeInfo?: any\n  pageType?: 'pages' | 'app'\n  pageRuntime?: ServerRuntime\n  originalAppPath?: string\n  isrFlushToDisk?: boolean\n  maxMemoryCacheSize?: number\n  cacheHandler?: string\n  cacheHandlers?: Record<string, string | undefined>\n  cacheLifeProfiles?: {\n    [profile: string]: import('../server/use-cache/cache-life').CacheLife\n  }\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  pprConfig: ExperimentalPPRConfig | undefined\n  buildId: string\n  sriEnabled: boolean\n}): Promise<PageIsStaticResult> {\n  await createIncrementalCache({\n    cacheHandler,\n    cacheHandlers,\n    distDir,\n    dir,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  const isPageStaticSpan = trace('is-page-static-utils', parentId)\n  return isPageStaticSpan\n    .traceAsyncFn(async (): Promise<PageIsStaticResult> => {\n      require('../shared/lib/runtime-config.external').setConfig(\n        runtimeEnvConfig\n      )\n      setHttpClientAndAgentOptions({\n        httpAgentOptions,\n      })\n\n      let componentsResult: LoadComponentsReturnType\n      let prerenderedRoutes: PrerenderedRoute[] | undefined\n      let prerenderFallbackMode: FallbackMode | undefined\n      let appConfig: AppSegmentConfig = {}\n      let rootParamKeys: readonly string[] | undefined\n      let isClientComponent: boolean = false\n      const pathIsEdgeRuntime = isEdgeRuntime(pageRuntime)\n\n      if (pathIsEdgeRuntime) {\n        const runtime = await getRuntimeContext({\n          paths: edgeInfo.files.map((file: string) => path.join(distDir, file)),\n          edgeFunctionEntry: {\n            ...edgeInfo,\n            wasm: (edgeInfo.wasm ?? []).map((binding: AssetBinding) => ({\n              ...binding,\n              filePath: path.join(distDir, binding.filePath),\n            })),\n          },\n          name: edgeInfo.name,\n          useCache: true,\n          distDir,\n        })\n        const mod = (\n          await runtime.context._ENTRIES[`middleware_${edgeInfo.name}`]\n        ).ComponentMod\n\n        // This is not needed during require.\n        const buildManifest = {} as BuildManifest\n\n        isClientComponent = isClientReference(mod)\n        componentsResult = {\n          Component: mod.default,\n          Document: mod.Document,\n          App: mod.App,\n          routeModule: mod.routeModule,\n          page,\n          ComponentMod: mod,\n          pageConfig: mod.config || {},\n          buildManifest,\n          reactLoadableManifest: {},\n          getServerSideProps: mod.getServerSideProps,\n          getStaticPaths: mod.getStaticPaths,\n          getStaticProps: mod.getStaticProps,\n        }\n      } else {\n        componentsResult = await loadComponents({\n          distDir,\n          page: originalAppPath || page,\n          isAppPath: pageType === 'app',\n          isDev: false,\n          sriEnabled,\n        })\n      }\n      const Comp = componentsResult.Component as NextComponentType | undefined\n\n      const routeModule: RouteModule = componentsResult.routeModule\n\n      let isRoutePPREnabled: boolean = false\n\n      if (pageType === 'app') {\n        const ComponentMod: AppPageModule = componentsResult.ComponentMod\n\n        isClientComponent = isClientReference(componentsResult.ComponentMod)\n\n        let segments\n        try {\n          segments = await collectSegments(componentsResult)\n        } catch (err) {\n          throw new Error(`Failed to collect configuration for ${page}`, {\n            cause: err,\n          })\n        }\n\n        appConfig = reduceAppConfig(segments)\n\n        if (appConfig.dynamic === 'force-static' && pathIsEdgeRuntime) {\n          Log.warn(\n            `Page \"${page}\" is using runtime = 'edge' which is currently incompatible with dynamic = 'force-static'. Please remove either \"runtime\" or \"force-static\" for correct behavior`\n          )\n        }\n\n        rootParamKeys = collectRootParamKeys(componentsResult)\n\n        // A page supports partial prerendering if it is an app page and either\n        // the whole app has PPR enabled or this page has PPR enabled when we're\n        // in incremental mode.\n        isRoutePPREnabled =\n          routeModule.definition.kind === RouteKind.APP_PAGE &&\n          !isInterceptionRouteAppPath(page) &&\n          checkIsRoutePPREnabled(pprConfig, appConfig)\n\n        // If force dynamic was set and we don't have PPR enabled, then set the\n        // revalidate to 0.\n        // TODO: (PPR) remove this once PPR is enabled by default\n        if (appConfig.dynamic === 'force-dynamic' && !isRoutePPREnabled) {\n          appConfig.revalidate = 0\n        }\n\n        // If the page is dynamic and we're not in edge runtime, then we need to\n        // build the static paths. The edge runtime doesn't support static\n        // paths.\n        if (isDynamicRoute(page) && !pathIsEdgeRuntime) {\n          ;({ prerenderedRoutes, fallbackMode: prerenderFallbackMode } =\n            await buildAppStaticPaths({\n              dir,\n              page,\n              dynamicIO,\n              authInterrupts,\n              segments,\n              distDir,\n              requestHeaders: {},\n              isrFlushToDisk,\n              maxMemoryCacheSize,\n              cacheHandler,\n              cacheLifeProfiles,\n              ComponentMod,\n              nextConfigOutput,\n              isRoutePPREnabled,\n              buildId,\n              rootParamKeys,\n            }))\n        }\n      } else {\n        if (!Comp || !isValidElementType(Comp) || typeof Comp === 'string') {\n          throw new Error('INVALID_DEFAULT_EXPORT')\n        }\n      }\n\n      const hasGetInitialProps = !!Comp?.getInitialProps\n      const hasStaticProps = !!componentsResult.getStaticProps\n      const hasStaticPaths = !!componentsResult.getStaticPaths\n      const hasServerProps = !!componentsResult.getServerSideProps\n\n      // A page cannot be prerendered _and_ define a data requirement. That's\n      // contradictory!\n      if (hasGetInitialProps && hasStaticProps) {\n        throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT)\n      }\n\n      if (hasGetInitialProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT)\n      }\n\n      if (hasStaticProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n\n      const pageIsDynamic = isDynamicRoute(page)\n      // A page cannot have static parameters if it is not a dynamic page.\n      if (hasStaticProps && hasStaticPaths && !pageIsDynamic) {\n        throw new Error(\n          `getStaticPaths can only be used with dynamic pages, not '${page}'.` +\n            `\\nLearn more: https://nextjs.org/docs/routing/dynamic-routes`\n        )\n      }\n\n      if (hasStaticProps && pageIsDynamic && !hasStaticPaths) {\n        throw new Error(\n          `getStaticPaths is required for dynamic SSG pages and is missing for '${page}'.` +\n            `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n        )\n      }\n\n      if (hasStaticProps && hasStaticPaths) {\n        ;({ prerenderedRoutes, fallbackMode: prerenderFallbackMode } =\n          await buildPagesStaticPaths({\n            page,\n            locales,\n            defaultLocale,\n            configFileName,\n            getStaticPaths: componentsResult.getStaticPaths!,\n          }))\n      }\n\n      const isNextImageImported = (globalThis as any).__NEXT_IMAGE_IMPORTED\n      const config: PageConfig = isClientComponent\n        ? {}\n        : componentsResult.pageConfig\n\n      let isStatic = false\n      if (!hasStaticProps && !hasGetInitialProps && !hasServerProps) {\n        isStatic = true\n      }\n\n      // When PPR is enabled, any route may be completely static, so\n      // mark this route as static.\n      if (isRoutePPREnabled) {\n        isStatic = true\n      }\n\n      return {\n        isStatic,\n        isRoutePPREnabled,\n        isHybridAmp: config.amp === 'hybrid',\n        isAmpOnly: config.amp === true,\n        prerenderFallbackMode,\n        prerenderedRoutes,\n        rootParamKeys,\n        hasStaticProps,\n        hasServerProps,\n        isNextImageImported,\n        appConfig,\n      }\n    })\n    .catch((err) => {\n      if (err.message === 'INVALID_DEFAULT_EXPORT') {\n        throw err\n      }\n      console.error(err)\n      throw new Error(`Failed to collect page data for ${page}`)\n    })\n}\n\ntype ReducedAppConfig = Pick<\n  AppSegmentConfig,\n  | 'revalidate'\n  | 'dynamic'\n  | 'fetchCache'\n  | 'preferredRegion'\n  | 'experimental_ppr'\n  | 'runtime'\n  | 'maxDuration'\n>\n\n/**\n * Collect the app config from the generate param segments. This only gets a\n * subset of the config options.\n *\n * @param segments the generate param segments\n * @returns the reduced app config\n */\nexport function reduceAppConfig(\n  segments: Pick<AppSegment, 'config'>[]\n): ReducedAppConfig {\n  const config: ReducedAppConfig = {}\n\n  for (const segment of segments) {\n    const {\n      dynamic,\n      fetchCache,\n      preferredRegion,\n      revalidate,\n      experimental_ppr,\n      runtime,\n      maxDuration,\n    } = segment.config || {}\n\n    // TODO: should conflicting configs here throw an error\n    // e.g. if layout defines one region but page defines another\n\n    if (typeof preferredRegion !== 'undefined') {\n      config.preferredRegion = preferredRegion\n    }\n\n    if (typeof dynamic !== 'undefined') {\n      config.dynamic = dynamic\n    }\n\n    if (typeof fetchCache !== 'undefined') {\n      config.fetchCache = fetchCache\n    }\n\n    if (typeof revalidate !== 'undefined') {\n      config.revalidate = revalidate\n    }\n\n    // Any revalidate number overrides false, and shorter revalidate overrides\n    // longer (initially).\n    if (\n      typeof revalidate === 'number' &&\n      (typeof config.revalidate !== 'number' || revalidate < config.revalidate)\n    ) {\n      config.revalidate = revalidate\n    }\n\n    // If partial prerendering has been set, only override it if the current\n    // value is provided as it's resolved from root layout to leaf page.\n    if (typeof experimental_ppr !== 'undefined') {\n      config.experimental_ppr = experimental_ppr\n    }\n\n    if (typeof runtime !== 'undefined') {\n      config.runtime = runtime\n    }\n\n    if (typeof maxDuration !== 'undefined') {\n      config.maxDuration = maxDuration\n    }\n  }\n\n  return config\n}\n\nexport async function hasCustomGetInitialProps({\n  page,\n  distDir,\n  runtimeEnvConfig,\n  checkingApp,\n  sriEnabled,\n}: {\n  page: string\n  distDir: string\n  runtimeEnvConfig: any\n  checkingApp: boolean\n  sriEnabled: boolean\n}): Promise<boolean> {\n  require('../shared/lib/runtime-config.external').setConfig(runtimeEnvConfig)\n\n  const components = await loadComponents({\n    distDir,\n    page: page,\n    isAppPath: false,\n    isDev: false,\n    sriEnabled,\n  })\n  let mod = components.ComponentMod\n\n  if (checkingApp) {\n    mod = (await mod._app) || mod.default || mod\n  } else {\n    mod = mod.default || mod\n  }\n  mod = await mod\n  return mod.getInitialProps !== mod.origGetInitialProps\n}\n\nexport async function getDefinedNamedExports({\n  page,\n  distDir,\n  runtimeEnvConfig,\n  sriEnabled,\n}: {\n  page: string\n  distDir: string\n  runtimeEnvConfig: any\n  sriEnabled: boolean\n}): Promise<ReadonlyArray<string>> {\n  require('../shared/lib/runtime-config.external').setConfig(runtimeEnvConfig)\n  const components = await loadComponents({\n    distDir,\n    page: page,\n    isAppPath: false,\n    isDev: false,\n    sriEnabled,\n  })\n\n  return Object.keys(components.ComponentMod).filter((key) => {\n    return typeof components.ComponentMod[key] !== 'undefined'\n  })\n}\n\nexport function detectConflictingPaths(\n  combinedPages: string[],\n  ssgPages: Set<string>,\n  additionalGeneratedSSGPaths: Map<string, string[]>\n) {\n  const conflictingPaths = new Map<\n    string,\n    Array<{\n      path: string\n      page: string\n    }>\n  >()\n\n  const dynamicSsgPages = [...ssgPages].filter((page) => isDynamicRoute(page))\n  const additionalSsgPathsByPath: {\n    [page: string]: { [path: string]: string }\n  } = {}\n\n  additionalGeneratedSSGPaths.forEach((paths, pathsPage) => {\n    additionalSsgPathsByPath[pathsPage] ||= {}\n    paths.forEach((curPath) => {\n      const currentPath = curPath.toLowerCase()\n      additionalSsgPathsByPath[pathsPage][currentPath] = curPath\n    })\n  })\n\n  additionalGeneratedSSGPaths.forEach((paths, pathsPage) => {\n    paths.forEach((curPath) => {\n      const lowerPath = curPath.toLowerCase()\n      let conflictingPage = combinedPages.find(\n        (page) => page.toLowerCase() === lowerPath\n      )\n\n      if (conflictingPage) {\n        conflictingPaths.set(lowerPath, [\n          { path: curPath, page: pathsPage },\n          { path: conflictingPage, page: conflictingPage },\n        ])\n      } else {\n        let conflictingPath: string | undefined\n\n        conflictingPage = dynamicSsgPages.find((page) => {\n          if (page === pathsPage) return false\n\n          conflictingPath =\n            additionalGeneratedSSGPaths.get(page) == null\n              ? undefined\n              : additionalSsgPathsByPath[page][lowerPath]\n          return conflictingPath\n        })\n\n        if (conflictingPage && conflictingPath) {\n          conflictingPaths.set(lowerPath, [\n            { path: curPath, page: pathsPage },\n            { path: conflictingPath, page: conflictingPage },\n          ])\n        }\n      }\n    })\n  })\n\n  if (conflictingPaths.size > 0) {\n    let conflictingPathsOutput = ''\n\n    conflictingPaths.forEach((pathItems) => {\n      pathItems.forEach((pathItem, idx) => {\n        const isDynamic = pathItem.page !== pathItem.path\n\n        if (idx > 0) {\n          conflictingPathsOutput += 'conflicts with '\n        }\n\n        conflictingPathsOutput += `path: \"${pathItem.path}\"${\n          isDynamic ? ` from page: \"${pathItem.page}\" ` : ' '\n        }`\n      })\n      conflictingPathsOutput += '\\n'\n    })\n\n    Log.error(\n      'Conflicting paths returned from getStaticPaths, paths must be unique per page.\\n' +\n        'See more info here: https://nextjs.org/docs/messages/conflicting-ssg-paths\\n\\n' +\n        conflictingPathsOutput\n    )\n    process.exit(1)\n  }\n}\n\nexport async function copyTracedFiles(\n  dir: string,\n  distDir: string,\n  pageKeys: readonly string[],\n  appPageKeys: readonly string[] | undefined,\n  tracingRoot: string,\n  serverConfig: NextConfigComplete,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>\n) {\n  const outputPath = path.join(distDir, 'standalone')\n  let moduleType = false\n  const nextConfig = {\n    ...serverConfig,\n    distDir: `./${path.relative(dir, distDir)}`,\n  }\n  try {\n    const packageJsonPath = path.join(distDir, '../package.json')\n    const packageJsonContent = await fs.readFile(packageJsonPath, 'utf8')\n    const packageJson = JSON.parse(packageJsonContent)\n    moduleType = packageJson.type === 'module'\n\n    // we always copy the package.json to the standalone\n    // folder to ensure any resolving logic is maintained\n    const packageJsonOutputPath = path.join(\n      outputPath,\n      path.relative(tracingRoot, dir),\n      'package.json'\n    )\n    await fs.mkdir(path.dirname(packageJsonOutputPath), { recursive: true })\n    await fs.writeFile(packageJsonOutputPath, packageJsonContent)\n  } catch {}\n  const copiedFiles = new Set()\n  await fs.rm(outputPath, { recursive: true, force: true })\n\n  async function handleTraceFiles(traceFilePath: string) {\n    const traceData = JSON.parse(await fs.readFile(traceFilePath, 'utf8')) as {\n      files: string[]\n    }\n    const copySema = new Sema(10, { capacity: traceData.files.length })\n    const traceFileDir = path.dirname(traceFilePath)\n\n    await Promise.all(\n      traceData.files.map(async (relativeFile) => {\n        await copySema.acquire()\n\n        const tracedFilePath = path.join(traceFileDir, relativeFile)\n        const fileOutputPath = path.join(\n          outputPath,\n          path.relative(tracingRoot, tracedFilePath)\n        )\n\n        if (!copiedFiles.has(fileOutputPath)) {\n          copiedFiles.add(fileOutputPath)\n\n          await fs.mkdir(path.dirname(fileOutputPath), { recursive: true })\n          const symlink = await fs.readlink(tracedFilePath).catch(() => null)\n\n          if (symlink) {\n            try {\n              await fs.symlink(symlink, fileOutputPath)\n            } catch (e: any) {\n              if (e.code !== 'EEXIST') {\n                throw e\n              }\n            }\n          } else {\n            await fs.copyFile(tracedFilePath, fileOutputPath)\n          }\n        }\n\n        await copySema.release()\n      })\n    )\n  }\n\n  async function handleEdgeFunction(page: EdgeFunctionDefinition) {\n    async function handleFile(file: string) {\n      const originalPath = path.join(distDir, file)\n      const fileOutputPath = path.join(\n        outputPath,\n        path.relative(tracingRoot, distDir),\n        file\n      )\n      await fs.mkdir(path.dirname(fileOutputPath), { recursive: true })\n      await fs.copyFile(originalPath, fileOutputPath)\n    }\n    await Promise.all([\n      page.files.map(handleFile),\n      page.wasm?.map((file) => handleFile(file.filePath)),\n      page.assets?.map((file) => handleFile(file.filePath)),\n    ])\n  }\n\n  const edgeFunctionHandlers: Promise<any>[] = []\n\n  for (const middleware of Object.values(middlewareManifest.middleware)) {\n    if (isMiddlewareFilename(middleware.name)) {\n      edgeFunctionHandlers.push(handleEdgeFunction(middleware))\n    }\n  }\n\n  for (const page of Object.values(middlewareManifest.functions)) {\n    edgeFunctionHandlers.push(handleEdgeFunction(page))\n  }\n\n  await Promise.all(edgeFunctionHandlers)\n\n  for (const page of pageKeys) {\n    if (middlewareManifest.functions.hasOwnProperty(page)) {\n      continue\n    }\n    const route = normalizePagePath(page)\n\n    if (staticPages.has(route)) {\n      continue\n    }\n\n    const pageFile = path.join(\n      distDir,\n      'server',\n      'pages',\n      `${normalizePagePath(page)}.js`\n    )\n    const pageTraceFile = `${pageFile}.nft.json`\n    await handleTraceFiles(pageTraceFile).catch((err) => {\n      if (err.code !== 'ENOENT' || (page !== '/404' && page !== '/500')) {\n        Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n      }\n    })\n  }\n\n  if (hasNodeMiddleware) {\n    const middlewareFile = path.join(distDir, 'server', 'middleware.js')\n    const middlewareTrace = `${middlewareFile}.nft.json`\n    await handleTraceFiles(middlewareTrace)\n  }\n\n  if (appPageKeys) {\n    for (const page of appPageKeys) {\n      if (middlewareManifest.functions.hasOwnProperty(page)) {\n        continue\n      }\n      const pageFile = path.join(distDir, 'server', 'app', `${page}.js`)\n      const pageTraceFile = `${pageFile}.nft.json`\n      await handleTraceFiles(pageTraceFile).catch((err) => {\n        Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n      })\n    }\n  }\n\n  if (hasInstrumentationHook) {\n    await handleTraceFiles(\n      path.join(distDir, 'server', 'instrumentation.js.nft.json')\n    )\n  }\n\n  await handleTraceFiles(path.join(distDir, 'next-server.js.nft.json'))\n  const serverOutputPath = path.join(\n    outputPath,\n    path.relative(tracingRoot, dir),\n    'server.js'\n  )\n  await fs.mkdir(path.dirname(serverOutputPath), { recursive: true })\n\n  await fs.writeFile(\n    serverOutputPath,\n    `${\n      moduleType\n        ? `performance.mark('next-start');\nimport path from 'path'\nimport { fileURLToPath } from 'url'\nimport module from 'module'\nconst require = module.createRequire(import.meta.url)\nconst __dirname = fileURLToPath(new URL('.', import.meta.url))\n`\n        : `const path = require('path')`\n    }\n\nconst dir = path.join(__dirname)\n\nprocess.env.NODE_ENV = 'production'\nprocess.chdir(__dirname)\n\nconst currentPort = parseInt(process.env.PORT, 10) || 3000\nconst hostname = process.env.HOSTNAME || '0.0.0.0'\n\nlet keepAliveTimeout = parseInt(process.env.KEEP_ALIVE_TIMEOUT, 10)\nconst nextConfig = ${JSON.stringify(nextConfig)}\n\nprocess.env.__NEXT_PRIVATE_STANDALONE_CONFIG = JSON.stringify(nextConfig)\n\nrequire('next')\nconst { startServer } = require('next/dist/server/lib/start-server')\n\nif (\n  Number.isNaN(keepAliveTimeout) ||\n  !Number.isFinite(keepAliveTimeout) ||\n  keepAliveTimeout < 0\n) {\n  keepAliveTimeout = undefined\n}\n\nstartServer({\n  dir,\n  isDev: false,\n  config: nextConfig,\n  hostname,\n  port: currentPort,\n  allowRetry: false,\n  keepAliveTimeout,\n}).catch((err) => {\n  console.error(err);\n  process.exit(1);\n});`\n  )\n}\n\nexport function isReservedPage(page: string) {\n  return RESERVED_PAGE.test(page)\n}\n\nexport function isAppBuiltinNotFoundPage(page: string) {\n  return /next[\\\\/]dist[\\\\/]client[\\\\/]components[\\\\/]not-found-error/.test(\n    page\n  )\n}\n\nexport function isCustomErrorPage(page: string) {\n  return page === '/404' || page === '/500'\n}\n\nexport function isMiddlewareFile(file: string) {\n  return (\n    file === `/${MIDDLEWARE_FILENAME}` || file === `/src/${MIDDLEWARE_FILENAME}`\n  )\n}\n\nexport function isInstrumentationHookFile(file: string) {\n  return (\n    file === `/${INSTRUMENTATION_HOOK_FILENAME}` ||\n    file === `/src/${INSTRUMENTATION_HOOK_FILENAME}`\n  )\n}\n\nexport function getPossibleInstrumentationHookFilenames(\n  folder: string,\n  extensions: string[]\n) {\n  const files = []\n  for (const extension of extensions) {\n    files.push(\n      path.join(folder, `${INSTRUMENTATION_HOOK_FILENAME}.${extension}`),\n      path.join(folder, `src`, `${INSTRUMENTATION_HOOK_FILENAME}.${extension}`)\n    )\n  }\n\n  return files\n}\n\nexport function getPossibleMiddlewareFilenames(\n  folder: string,\n  extensions: string[]\n) {\n  return extensions.map((extension) =>\n    path.join(folder, `${MIDDLEWARE_FILENAME}.${extension}`)\n  )\n}\n\nexport class NestedMiddlewareError extends Error {\n  constructor(\n    nestedFileNames: string[],\n    mainDir: string,\n    pagesOrAppDir: string\n  ) {\n    super(\n      `Nested Middleware is not allowed, found:\\n` +\n        `${nestedFileNames.map((file) => `pages${file}`).join('\\n')}\\n` +\n        `Please move your code to a single file at ${path.join(\n          path.posix.sep,\n          path.relative(mainDir, path.resolve(pagesOrAppDir, '..')),\n          'middleware'\n        )} instead.\\n` +\n        `Read More - https://nextjs.org/docs/messages/nested-middleware`\n    )\n  }\n}\n\nexport function getSupportedBrowsers(\n  dir: string,\n  isDevelopment: boolean\n): string[] {\n  let browsers: any\n  try {\n    const browsersListConfig = browserslist.loadConfig({\n      path: dir,\n      env: isDevelopment ? 'development' : 'production',\n    })\n    // Running `browserslist` resolves `extends` and other config features into a list of browsers\n    if (browsersListConfig && browsersListConfig.length > 0) {\n      browsers = browserslist(browsersListConfig)\n    }\n  } catch {}\n\n  // When user has browserslist use that target\n  if (browsers && browsers.length > 0) {\n    return browsers\n  }\n\n  // Uses modern browsers as the default.\n  return MODERN_BROWSERSLIST_TARGET\n}\n\nexport function isWebpackServerOnlyLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(\n    layer && WEBPACK_LAYERS.GROUP.serverOnly.includes(layer as any)\n  )\n}\n\nexport function isWebpackClientOnlyLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(\n    layer && WEBPACK_LAYERS.GROUP.clientOnly.includes(layer as any)\n  )\n}\n\nexport function isWebpackDefaultLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return (\n    layer === null ||\n    layer === undefined ||\n    layer === WEBPACK_LAYERS.pagesDirBrowser ||\n    layer === WEBPACK_LAYERS.pagesDirEdge ||\n    layer === WEBPACK_LAYERS.pagesDirNode\n  )\n}\n\nexport function isWebpackBundledLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(layer && WEBPACK_LAYERS.GROUP.bundled.includes(layer as any))\n}\n\nexport function isWebpackAppPagesLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(layer && WEBPACK_LAYERS.GROUP.appPages.includes(layer as any))\n}\n\nexport function collectMeta({\n  status,\n  headers,\n}: {\n  status?: number\n  headers?: OutgoingHttpHeaders\n}): {\n  status?: number\n  headers?: Record<string, string>\n} {\n  const meta: {\n    status?: number\n    headers?: Record<string, string>\n  } = {}\n\n  if (status !== 200) {\n    meta.status = status\n  }\n\n  if (headers && Object.keys(headers).length) {\n    meta.headers = {}\n\n    // normalize header values as initialHeaders\n    // must be Record<string, string>\n    for (const key in headers) {\n      // set-cookie is already handled - the middleware cookie setting case\n      // isn't needed for the prerender manifest since it can't read cookies\n      if (key === 'x-middleware-set-cookie') continue\n\n      let value = headers[key]\n\n      if (Array.isArray(value)) {\n        if (key === 'set-cookie') {\n          value = value.join(',')\n        } else {\n          value = value[value.length - 1]\n        }\n      }\n\n      if (typeof value === 'string') {\n        meta.headers[key] = value\n      }\n    }\n  }\n\n  return meta\n}\n"], "names": ["NestedMiddlewareError", "collectMeta", "collectRoutesUsingEdgeRuntime", "computeFromManifest", "copyTracedFiles", "detectConflictingPaths", "difference", "getDefinedNamedExports", "getJsPageSizeInKb", "getPossibleInstrumentationHookFilenames", "getPossibleMiddlewareFilenames", "getSupportedBrowsers", "hasCustomGetInitialProps", "isAppBuiltinNotFoundPage", "isCustomErrorPage", "isInstrumentationHookFile", "isInstrumentationHookFilename", "isMiddlewareFile", "isMiddlewareFilename", "isPageStatic", "isReservedPage", "isWebpackAppPagesLayer", "isWebpackBundledLayer", "isWebpackClientOnlyLayer", "isWebpackDefaultLayer", "isWebpackServerOnlyLayer", "printCustomRoutes", "printTreeView", "reduceAppConfig", "unique", "print", "console", "log", "RESERVED_PAGE", "fileGzipStats", "fsStatGzip", "file", "cached", "getGzipSize", "fileSize", "fs", "stat", "size", "fileStats", "fsStat", "main", "sub", "Set", "a", "b", "filter", "x", "has", "intersect", "sum", "reduce", "cachedBuildManifest", "cachedAppBuildManifest", "lastCompute", "lastComputePageInfo", "manifests", "distPath", "gzipSize", "pageInfos", "files", "Object", "is", "build", "app", "countBuildFiles", "map", "key", "manifest", "set", "Infinity", "get", "pages", "each", "Map", "expected", "pageInfo", "isHybridAmp", "getSize", "stats", "Promise", "all", "keys", "f", "path", "join", "groupFiles", "listing", "entries", "shapeGroup", "group", "acc", "push", "total", "len", "common", "router", "undefined", "sizes", "MIDDLEWARE_FILENAME", "INSTRUMENTATION_HOOK_FILENAME", "filterAndSortList", "list", "routeType", "hasCustomApp", "e", "slice", "sort", "localeCompare", "input", "routesUsingEdgeRuntime", "route", "info", "isEdgeRuntime", "runtime", "lists", "buildId", "pagesDir", "pageExtensions", "buildManifest", "appBuildManifest", "middlewareManifest", "useStaticPages404", "getPrettySize", "_size", "strong", "process", "env", "__NEXT_PRIVATE_DETERMINISTIC_BUILD_OUTPUT", "prettyBytes", "white", "bold", "MIN_DURATION", "getPrettyDuration", "_duration", "duration", "green", "yellow", "red", "getCleanName", "fileName", "replace", "findPageFile", "usedSymbols", "messages", "printFileTree", "routerType", "filteredPages", "length", "showRevalidate", "showExpire", "page", "cacheControl", "initialCacheControl", "revalidate", "expire", "entry", "underline", "for<PERSON>ach", "item", "i", "arr", "border", "ampFirs<PERSON>", "ampFirstPages", "includes", "totalDuration", "pageDuration", "ssgPageDurations", "symbol", "isRoutePPREnabled", "hasEmptyPrelude", "isDynamicAppRoute", "hasPostponed", "isStatic", "isSSG", "add", "cyan", "totalSize", "formatRevalidate", "formatExpire", "uniqueCssFiles", "endsWith", "contSymbol", "index", "innerSymbol", "ssgPageRoutes", "totalRoutes", "routes", "some", "d", "previewPages", "Math", "min", "routesWithDuration", "idx", "remainingRoutes", "remaining", "avgDuration", "round", "sharedFilesSize", "sharedFiles", "sharedCssFiles", "sharedJsChunks", "tenKbLimit", "restChunkSize", "restChunkCount", "originalName", "cleanName", "UNDERSCORE_NOT_FOUND_ROUTE", "middlewareInfo", "middleware", "middlewareSizes", "dep", "textTable", "align", "stringLength", "str", "stripAnsi", "staticFunctionInfo", "redirects", "rewrites", "headers", "printRoutes", "type", "isRedirects", "isHeaders", "routesStr", "routeStr", "source", "r", "destination", "statusCode", "permanent", "header", "last", "value", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "cachedStats", "pageManifest", "Error", "new<PERSON>ey", "normalizeAppPath", "pageData", "pagePath", "denormalizePagePath", "denormalizeAppPagePath", "fnFilterJs", "pageFiles", "appFiles", "fnMapRealPath", "allFilesReal", "selfFilesReal", "getCachedSize", "allFilesSize", "selfFilesSize", "dir", "distDir", "configFileName", "runtimeEnvConfig", "httpAgentOptions", "locales", "defaultLocale", "parentId", "pageRuntime", "edgeInfo", "pageType", "dynamicIO", "authInterrupts", "originalAppPath", "isrFlushToDisk", "maxMemoryCacheSize", "nextConfigOutput", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheLifeProfiles", "pprConfig", "sriEnabled", "createIncrementalCache", "flushToDisk", "cacheMaxMemorySize", "isPageStaticSpan", "trace", "traceAsyncFn", "require", "setConfig", "setHttpClientAndAgentOptions", "componentsResult", "prerenderedRoutes", "prerenderFallbackMode", "appConfig", "rootParamKeys", "isClientComponent", "pathIsEdgeRuntime", "getRuntimeContext", "paths", "edgeFunctionEntry", "wasm", "binding", "filePath", "name", "useCache", "mod", "context", "_ENTRIES", "ComponentMod", "isClientReference", "Component", "default", "Document", "App", "routeModule", "pageConfig", "config", "reactLoadableManifest", "getServerSideProps", "getStaticPaths", "getStaticProps", "loadComponents", "isAppPath", "isDev", "Comp", "segments", "collectSegments", "err", "cause", "dynamic", "Log", "warn", "collectRootParamKeys", "definition", "kind", "RouteKind", "APP_PAGE", "isInterceptionRouteAppPath", "checkIsRoutePPREnabled", "isDynamicRoute", "fallbackMode", "buildAppStaticPaths", "requestHeaders", "isValidElementType", "hasGetInitialProps", "getInitialProps", "hasStaticProps", "hasStaticPaths", "hasServerProps", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "pageIsDynamic", "buildPagesStaticPaths", "isNextImageImported", "globalThis", "__NEXT_IMAGE_IMPORTED", "amp", "isAmpOnly", "catch", "message", "error", "segment", "fetchCache", "preferredRegion", "experimental_ppr", "maxDuration", "checkingApp", "components", "_app", "origGetInitialProps", "combinedPages", "ssgPages", "additionalGeneratedSSGPaths", "conflictingPaths", "dynamicSsgPages", "additionalSsgPathsByPath", "pathsPage", "curPath", "currentPath", "toLowerCase", "lowerPath", "conflictingPage", "find", "conflicting<PERSON><PERSON>", "conflictingPathsOutput", "pathItems", "pathItem", "isDynamic", "exit", "pageKeys", "appPageKeys", "tracingRoot", "serverConfig", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "outputPath", "moduleType", "nextConfig", "relative", "packageJsonPath", "packageJsonContent", "readFile", "packageJson", "JSON", "parse", "packageJsonOutputPath", "mkdir", "dirname", "recursive", "writeFile", "copiedFiles", "rm", "force", "handleTraceFiles", "traceFilePath", "traceData", "copySema", "<PERSON><PERSON>", "capacity", "traceFileDir", "relativeFile", "acquire", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileOutputPath", "symlink", "readlink", "code", "copyFile", "release", "handleEdgeFunction", "handleFile", "originalPath", "assets", "edgeFunctionHandlers", "values", "functions", "hasOwnProperty", "normalizePagePath", "pageFile", "pageTraceFile", "middlewareFile", "middlewareTrace", "serverOutputPath", "stringify", "test", "folder", "extensions", "extension", "constructor", "nestedFileNames", "mainDir", "pagesOrAppDir", "posix", "sep", "resolve", "isDevelopment", "browsers", "browsersListConfig", "browserslist", "loadConfig", "MODERN_BROWSERSLIST_TARGET", "layer", "Boolean", "WEBPACK_LAYERS", "GROUP", "serverOnly", "clientOnly", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "bundled", "appPages", "status", "meta", "Array", "isArray"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqwDaA,qBAAqB;eAArBA;;IAoFGC,WAAW;eAAXA;;IA5+CAC,6BAA6B;eAA7BA;;IA7MMC,mBAAmB;eAAnBA;;IAs1CAC,eAAe;eAAfA;;IAxFNC,sBAAsB;eAAtBA;;IA9yCAC,UAAU;eAAVA;;IAqxCMC,sBAAsB;eAAtBA;;IAzfAC,iBAAiB;eAAjBA;;IAi2BNC,uCAAuC;eAAvCA;;IAeAC,8BAA8B;eAA9BA;;IA4BAC,oBAAoB;eAApBA;;IApbMC,wBAAwB;eAAxBA;;IAkXNC,wBAAwB;eAAxBA;;IAMAC,iBAAiB;eAAjBA;;IAUAC,yBAAyB;eAAzBA;;IAn7CAC,6BAA6B;eAA7BA;;IA66CAC,gBAAgB;eAAhBA;;IAj7CAC,oBAAoB;eAApBA;;IAwsBMC,YAAY;eAAZA;;IA2tBNC,cAAc;eAAdA;;IAiIAC,sBAAsB;eAAtBA;;IANAC,qBAAqB;eAArBA;;IApBAC,wBAAwB;eAAxBA;;IAQAC,qBAAqB;eAArBA;;IAhBAC,wBAAwB;eAAxBA;;IA/+BAC,iBAAiB;eAAjBA;;IAxcMC,aAAa;eAAbA;;IA46BNC,eAAe;eAAfA;;IA1rCAC,MAAM;eAANA;;;QAvFT;QACA;QACA;4BAUA;iEACiB;kEACF;6DACL;oBACc;yBACI;kEACb;qEACG;2BAQlB;4BAIA;oEACiB;2BACO;8BACF;+BACC;6DACT;gCACU;uBAET;mCACuB;2BACxB;qCACe;mCACF;yBACA;2CACA;0BACD;oCACM;2BACb;oCAEiB;qBACJ;6BAKP;wCACO;sCACF;qBACD;uBACE;wBAGS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI/C,4CAA4C;AAC5C,MAAMC,QAAQC,QAAQC,GAAG;AAEzB,MAAMC,gBAAgB;AACtB,MAAMC,gBAA8D,CAAC;AACrE,MAAMC,aAAa,CAACC;IAClB,MAAMC,SAASH,aAAa,CAACE,KAAK;IAClC,IAAIC,QAAQ,OAAOA;IACnB,OAAQH,aAAa,CAACE,KAAK,GAAGE,iBAAW,CAACF,IAAI,CAACA;AACjD;AAEA,MAAMG,WAAW,OAAOH,OAAiB,AAAC,CAAA,MAAMI,YAAE,CAACC,IAAI,CAACL,KAAI,EAAGM,IAAI;AAEnE,MAAMC,YAA0D,CAAC;AACjE,MAAMC,SAAS,CAACR;IACd,MAAMC,SAASM,SAAS,CAACP,KAAK;IAC9B,IAAIC,QAAQ,OAAOA;IACnB,OAAQM,SAAS,CAACP,KAAK,GAAGG,SAASH;AACrC;AAEO,SAASP,OAAUgB,IAAsB,EAAEC,GAAqB;IACrE,OAAO;WAAI,IAAIC,IAAI;eAAIF;eAASC;SAAI;KAAE;AACxC;AAEO,SAASxC,WACduC,IAAuC,EACvCC,GAAsC;IAEtC,MAAME,IAAI,IAAID,IAAIF;IAClB,MAAMI,IAAI,IAAIF,IAAID;IAClB,OAAO;WAAIE;KAAE,CAACE,MAAM,CAAC,CAACC,IAAM,CAACF,EAAEG,GAAG,CAACD;AACrC;AAEA;;CAEC,GACD,SAASE,UAAaR,IAAsB,EAAEC,GAAqB;IACjE,MAAME,IAAI,IAAID,IAAIF;IAClB,MAAMI,IAAI,IAAIF,IAAID;IAClB,OAAO;WAAI,IAAIC,IAAI;eAAIC;SAAE,CAACE,MAAM,CAAC,CAACC,IAAMF,EAAEG,GAAG,CAACD;KAAK;AACrD;AAEA,SAASG,IAAIN,CAAwB;IACnC,OAAOA,EAAEO,MAAM,CAAC,CAACb,MAAMD,OAASC,OAAOD,MAAM;AAC/C;AAsBA,IAAIe;AACJ,IAAIC;AAEJ,IAAIC;AACJ,IAAIC;AAEG,eAAexD,oBACpByD,SAGC,EACDC,QAAgB,EAChBC,WAAoB,IAAI,EACxBC,SAAiC;QAyD7BH,gBAmBMI;IA1EV,IACEC,OAAOC,EAAE,CAACV,qBAAqBI,UAAUO,KAAK,KAC9CR,wBAAwB,CAAC,CAACI,aAC1BE,OAAOC,EAAE,CAACT,wBAAwBG,UAAUQ,GAAG,GAC/C;QACA,OAAOV;IACT;IAEA,0EAA0E;IAC1E,wCAAwC;IAExC,MAAMW,kBAAkB,CACtBC,KACAC,KACAC;QAEA,KAAK,MAAMpC,QAAQoC,QAAQ,CAACD,IAAI,CAAE;YAChC,IAAIA,QAAQ,SAAS;gBACnBD,IAAIG,GAAG,CAACrC,MAAMsC;YAChB,OAAO,IAAIJ,IAAIlB,GAAG,CAAChB,OAAO;gBACxBkC,IAAIG,GAAG,CAACrC,MAAMkC,IAAIK,GAAG,CAACvC,QAAS;YACjC,OAAO;gBACLkC,IAAIG,GAAG,CAACrC,MAAM;YAChB;QACF;IACF;IAEA,MAAM4B,QASF;QACFY,OAAO;YAAEC,MAAM,IAAIC;YAAOC,UAAU;QAAE;IACxC;IAEA,IAAK,MAAMR,OAAOX,UAAUO,KAAK,CAACS,KAAK,CAAE;QACvC,IAAIb,WAAW;YACb,MAAMiB,WAAWjB,UAAUY,GAAG,CAACJ;YAC/B,kEAAkE;YAClE,kDAAkD;YAClD,IAAIS,4BAAAA,SAAUC,WAAW,EAAE;gBACzB;YACF;QACF;QAEAjB,MAAMY,KAAK,CAACG,QAAQ;QACpBV,gBAAgBL,MAAMY,KAAK,CAACC,IAAI,EAAEN,KAAKX,UAAUO,KAAK,CAACS,KAAK;IAC9D;IAEA,iDAAiD;IACjD,KAAIhB,iBAAAA,UAAUQ,GAAG,qBAAbR,eAAegB,KAAK,EAAE;QACxBZ,MAAMI,GAAG,GAAG;YAAES,MAAM,IAAIC;YAAuBC,UAAU;QAAE;QAE3D,IAAK,MAAMR,OAAOX,UAAUQ,GAAG,CAACQ,KAAK,CAAE;YACrCZ,MAAMI,GAAG,CAACW,QAAQ;YAClBV,gBAAgBL,MAAMI,GAAG,CAACS,IAAI,EAAEN,KAAKX,UAAUQ,GAAG,CAACQ,KAAK;QAC1D;IACF;IAEA,MAAMM,UAAUpB,WAAW3B,aAAaS;IACxC,MAAMuC,QAAQ,IAAIL;IAElB,6EAA6E;IAC7E,WAAW;IAEX,MAAMM,QAAQC,GAAG,CACf;WACK,IAAItC,IAAY;eACdiB,MAAMY,KAAK,CAACC,IAAI,CAACS,IAAI;eACpBtB,EAAAA,aAAAA,MAAMI,GAAG,qBAATJ,WAAWa,IAAI,CAACS,IAAI,OAAM,EAAE;SACjC;KACF,CAAChB,GAAG,CAAC,OAAOiB;QACX,IAAI;YACF,kCAAkC;YAClCJ,MAAMV,GAAG,CAACc,GAAG,MAAML,QAAQM,aAAI,CAACC,IAAI,CAAC5B,UAAU0B;QACjD,EAAE,OAAM,CAAC;IACX;IAGF,MAAMG,aAAa,OAAOC;QAIxB,MAAMC,UAAU;eAAID,QAAQd,IAAI,CAACe,OAAO;SAAG;QAE3C,MAAMC,aAAa,CAACC,QAClBA,MAAMvC,MAAM,CACV,CAACwC,KAAK,CAACR,EAAE;gBACPQ,IAAI/B,KAAK,CAACgC,IAAI,CAACT;gBAEf,MAAM7C,OAAOyC,MAAMR,GAAG,CAACY;gBACvB,IAAI,OAAO7C,SAAS,UAAU;oBAC5BqD,IAAIrD,IAAI,CAACuD,KAAK,IAAIvD;gBACpB;gBAEA,OAAOqD;YACT,GACA;gBACE/B,OAAO,EAAE;gBACTtB,MAAM;oBACJuD,OAAO;gBACT;YACF;QAGJ,OAAO;YACLpE,QAAQgE,WAAWD,QAAQ1C,MAAM,CAAC,CAAC,GAAGgD,IAAI,GAAKA,QAAQ;YACvDC,QAAQN,WACND,QAAQ1C,MAAM,CACZ,CAAC,GAAGgD,IAAI,GAAKA,QAAQP,QAAQZ,QAAQ,IAAImB,QAAQxB;QAGvD;IACF;IAEAhB,cAAc;QACZ0C,QAAQ;YACNxB,OAAO,MAAMc,WAAW1B,MAAMY,KAAK;YACnCR,KAAKJ,MAAMI,GAAG,GAAG,MAAMsB,WAAW1B,MAAMI,GAAG,IAAIiC;QACjD;QACAC,OAAOnB;IACT;IAEA3B,sBAAsBI,UAAUO,KAAK;IACrCV,yBAAyBG,UAAUQ,GAAG;IACtCT,sBAAsB,CAAC,CAACI;IACxB,OAAOL;AACT;AAEO,SAASxC,qBAAqBkB,IAAoB;IACvD,OAAOA,SAASmE,8BAAmB,IAAInE,SAAS,CAAC,IAAI,EAAEmE,8BAAmB,EAAE;AAC9E;AAEO,SAASvF,8BAA8BoB,IAAoB;IAChE,OACEA,SAASoE,wCAA6B,IACtCpE,SAAS,CAAC,IAAI,EAAEoE,wCAA6B,EAAE;AAEnD;AAEA,MAAMC,oBAAoB,CACxBC,MACAC,WACAC;IAEA,IAAIhC;IACJ,IAAI+B,cAAc,OAAO;QACvB,8CAA8C;QAC9C/B,QAAQ8B,KAAKxD,MAAM,CAAC,CAAC2D,IAAMA,MAAM;IACnC,OAAO;QACL,wBAAwB;QACxBjC,QAAQ8B,KACLI,KAAK,GACL5D,MAAM,CACL,CAAC2D,IACC,CACEA,CAAAA,MAAM,gBACNA,MAAM,aACL,CAACD,gBAAgBC,MAAM,OAAO;IAGzC;IACA,OAAOjC,MAAMmC,IAAI,CAAC,CAAC/D,GAAGC,IAAMD,EAAEgE,aAAa,CAAC/D;AAC9C;AA4BO,SAAS/C,8BACd+G,KAAgB;IAEhB,MAAMC,yBAAiD,CAAC;IACxD,KAAK,MAAM,CAACC,OAAOC,KAAK,IAAIH,MAAMrB,OAAO,GAAI;QAC3C,IAAIyB,IAAAA,4BAAa,EAACD,KAAKE,OAAO,GAAG;YAC/BJ,sBAAsB,CAACC,MAAM,GAAG;QAClC;IACF;IAEA,OAAOD;AACT;AAEO,eAAevF,cACpB4F,KAGC,EACDxD,SAAgC,EAChC,EACEF,QAAQ,EACR2D,OAAO,EACPC,QAAQ,EACRC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,EACjBhE,WAAW,IAAI,EAWhB;QAyWEyD,YAWoBM;IAlXvB,MAAME,gBAAgB,CACpBC,OACA,EAAEC,MAAM,EAAwB,GAAG,CAAC,CAAC;QAErC,MAAMvF,OAAOwF,QAAQC,GAAG,CAACC,yCAAyC,GAC9D,WACAC,IAAAA,oBAAW,EAACL;QAEhB,OAAOC,SAASK,IAAAA,iBAAK,EAACC,IAAAA,gBAAI,EAAC7F,SAASA;IACtC;IAEA,yEAAyE;IACzE,MAAM8F,eAAeN,QAAQC,GAAG,CAACC,yCAAyC,GACtE1D,SAAS,kCAAkC;OAC3C;IAEJ,MAAM+D,oBAAoB,CAACC;QACzB,MAAMC,WAAW,GAAGD,UAAU,GAAG,CAAC;QAClC,uBAAuB;QACvB,IAAIA,YAAY,MAAM,OAAOE,IAAAA,iBAAK,EAACD;QACnC,yBAAyB;QACzB,IAAID,YAAY,MAAM,OAAOG,IAAAA,kBAAM,EAACF;QACpC,oBAAoB;QACpB,OAAOG,IAAAA,eAAG,EAACP,IAAAA,gBAAI,EAACI;IAClB;IAEA,MAAMI,eAAe,CAACC,WACpBA,QACE,qBAAqB;SACpBC,OAAO,CAAC,aAAa,GACtB,kCAAkC;SACjCA,OAAO,CAAC,cAAc,SACvB,mBAAmB;SAClBA,OAAO,CAAC,6CAA6C;IAE1D,iCAAiC;IACjC,MAAMrC,eAAe,CAAC,CACpBa,CAAAA,YAAa,MAAMyB,IAAAA,0BAAY,EAACzB,UAAU,SAASC,gBAAgB,MAAM;IAG3E,gEAAgE;IAChE,MAAMyB,cAAc,IAAIpG;IAExB,MAAMqG,WAAuD,EAAE;IAE/D,MAAMjE,QAAQ,MAAMhF,oBAClB;QAAEgE,OAAOwD;QAAevD,KAAKwD;IAAiB,GAC9C/D,UACAC,UACAC;IAGF,MAAMsF,gBAAgB,OAAO,EAC3B3C,IAAI,EACJ4C,UAAU,EAIX;YAwNyBnE,0BAIpBA;QA3NJ,MAAMoE,gBAAgB9C,kBAAkBC,MAAM4C,YAAY1C;QAC1D,IAAI2C,cAAcC,MAAM,KAAK,GAAG;YAC9B;QACF;QAEA,IAAIC,iBAAiB;QACrB,IAAIC,aAAa;QAEjB,KAAK,MAAMC,QAAQJ,cAAe;gBACXxF;YAArB,MAAM6F,gBAAe7F,iBAAAA,UAAUY,GAAG,CAACgF,0BAAd5F,eAAqB8F,mBAAmB;YAE7D,IAAID,gCAAAA,aAAcE,UAAU,EAAE;gBAC5BL,iBAAiB;YACnB;YAEA,IAAIG,gCAAAA,aAAcG,MAAM,EAAE;gBACxBL,aAAa;YACf;YAEA,IAAID,kBAAkBC,YAAY;gBAChC;YACF;QACF;QAEAN,SAASpD,IAAI,CACX;YACEsD,eAAe,QAAQ,gBAAgB;YACvC;YACA;YACAG,iBAAiB,eAAe;YAChCC,aAAa,WAAW;SACzB,CAACpF,GAAG,CAAC,CAAC0F,QAAUC,IAAAA,qBAAS,EAACD;QAS7BT,cAAcW,OAAO,CAAC,CAACC,MAAMC,GAAGC;gBAc3BrF,4BA6DD2C,2BAsBE3C;YAhGJ,MAAMsF,SACJF,MAAM,IACFC,IAAIb,MAAM,KAAK,IACb,MACA,MACFY,MAAMC,IAAIb,MAAM,GAAG,IACjB,MACA;YAER,MAAMxE,WAAWjB,UAAUY,GAAG,CAACwF;YAC/B,MAAMI,WAAW5C,cAAc6C,aAAa,CAACC,QAAQ,CAACN;YACtD,MAAMO,gBACJ,AAAC1F,CAAAA,CAAAA,4BAAAA,SAAU2F,YAAY,KAAI,CAAA,IAC1B3F,CAAAA,CAAAA,6BAAAA,6BAAAA,SAAU4F,gBAAgB,qBAA1B5F,2BAA4BzB,MAAM,CAAC,CAACP,GAAGC,IAAMD,IAAKC,CAAAA,KAAK,CAAA,GAAI,OAAM,CAAA;YAEpE,IAAI4H;YAEJ,IAAIV,SAAS,WAAWA,SAAS,gBAAgB;gBAC/CU,SAAS;YACX,OAAO,IAAIxD,IAAAA,4BAAa,EAACrC,4BAAAA,SAAUsC,OAAO,GAAG;gBAC3CuD,SAAS;YACX,OAAO,IAAI7F,4BAAAA,SAAU8F,iBAAiB,EAAE;gBACtC,IACE,2EAA2E;gBAC3E9F,CAAAA,4BAAAA,SAAU+F,eAAe,KACzB,qEAAqE;gBACrE,0DAA0D;gBACzD/F,SAASgG,iBAAiB,IAAI,CAAChG,SAASiG,YAAY,EACrD;oBACAJ,SAAS;gBACX,OAAO,IAAI,EAAC7F,4BAAAA,SAAUiG,YAAY,GAAE;oBAClCJ,SAAS;gBACX,OAAO;oBACLA,SAAS;gBACX;YACF,OAAO,IAAI7F,4BAAAA,SAAUkG,QAAQ,EAAE;gBAC7BL,SAAS;YACX,OAAO,IAAI7F,4BAAAA,SAAUmG,KAAK,EAAE;gBAC1BN,SAAS;YACX,OAAO;gBACLA,SAAS;YACX;YAEA1B,YAAYiC,GAAG,CAACP;YAEhBzB,SAASpD,IAAI,CAAC;gBACZ,GAAGsE,OAAO,CAAC,EAAEO,OAAO,CAAC,EAAEV,OACrBO,gBAAgBlC,eACZ,CAAC,EAAE,EAAEC,kBAAkBiC,eAAe,CAAC,CAAC,GACxC,IACJ;gBACF1F,WACIuF,WACEc,IAAAA,gBAAI,EAAC,SACLrG,SAAStC,IAAI,IAAI,IACfqF,cAAc/C,SAAStC,IAAI,IAC3B,KACJ;gBACJsC,WACIuF,WACEc,IAAAA,gBAAI,EAAC,SACLrG,SAAStC,IAAI,IAAI,IACfqF,cAAc/C,SAASsG,SAAS,EAAE;oBAAErD,QAAQ;gBAAK,KACjD,KACJ;gBACJwB,mBAAkBzE,4BAAAA,SAAU6E,mBAAmB,IAC3C0B,IAAAA,wBAAgB,EAACvG,SAAS6E,mBAAmB,IAC7C;gBACJH,eAAc1E,4BAAAA,SAAU6E,mBAAmB,IACvC2B,IAAAA,oBAAY,EAACxG,SAAS6E,mBAAmB,IACzC;aACL;YAED,MAAM4B,iBACJ9D,EAAAA,4BAAAA,cAAc/C,KAAK,CAACuF,KAAK,qBAAzBxC,0BAA2BzE,MAAM,CAC/B,CAACd;oBAEC+C;uBADA/C,KAAKsJ,QAAQ,CAAC,aACdvG,2BAAAA,MAAMiB,MAAM,CAACkD,WAAW,qBAAxBnE,yBAA0BtD,MAAM,CAACmC,KAAK,CAACyG,QAAQ,CAACrI;mBAC/C,EAAE;YAET,IAAIqJ,eAAejC,MAAM,GAAG,GAAG;gBAC7B,MAAMmC,aAAavB,MAAMC,IAAIb,MAAM,GAAG,IAAI,MAAM;gBAEhDiC,eAAevB,OAAO,CAAC,CAAC9H,MAAMwJ,OAAO,EAAEpC,MAAM,EAAE;oBAC7C,MAAMqC,cAAcD,UAAUpC,SAAS,IAAI,MAAM;oBACjD,MAAM9G,OAAOyC,MAAMmB,KAAK,CAAC3B,GAAG,CAACvC;oBAC7BgH,SAASpD,IAAI,CAAC;wBACZ,GAAG2F,WAAW,GAAG,EAAEE,YAAY,CAAC,EAAE9C,aAAa3G,OAAO;wBACtD,OAAOM,SAAS,WAAWqF,cAAcrF,QAAQ;wBACjD;wBACA;wBACA;qBACD;gBACH;YACF;YAEA,IAAIsC,6BAAAA,0BAAAA,SAAU8G,aAAa,qBAAvB9G,wBAAyBwE,MAAM,EAAE;gBACnC,MAAMuC,cAAc/G,SAAS8G,aAAa,CAACtC,MAAM;gBACjD,MAAMmC,aAAavB,MAAMC,IAAIb,MAAM,GAAG,IAAI,MAAM;gBAEhD,IAAIwC;gBACJ,IACEhH,SAAS4F,gBAAgB,IACzB5F,SAAS4F,gBAAgB,CAACqB,IAAI,CAAC,CAACC,IAAMA,IAAI1D,eAC1C;oBACA,MAAM2D,eAAeJ,gBAAgB,IAAI,IAAIK,KAAKC,GAAG,CAACN,aAAa;oBACnE,MAAMO,qBAAqBtH,SAAS8G,aAAa,CAC9CxH,GAAG,CAAC,CAAC6C,OAAOoF,MAAS,CAAA;4BACpBpF;4BACAwB,UAAU3D,SAAS4F,gBAAgB,AAAC,CAAC2B,IAAI,IAAI;wBAC/C,CAAA,GACCxF,IAAI,CAAC,CAAC,EAAE4B,UAAU3F,CAAC,EAAE,EAAE,EAAE2F,UAAU1F,CAAC,EAAE,GACrC,mBAAmB;wBACnB,wDAAwD;wBACxDD,KAAKwF,gBAAgBvF,KAAKuF,eAAe,IAAIvF,IAAID;oBAErDgJ,SAASM,mBAAmBxF,KAAK,CAAC,GAAGqF;oBACrC,MAAMK,kBAAkBF,mBAAmBxF,KAAK,CAACqF;oBACjD,IAAIK,gBAAgBhD,MAAM,EAAE;wBAC1B,MAAMiD,YAAYD,gBAAgBhD,MAAM;wBACxC,MAAMkD,cAAcN,KAAKO,KAAK,CAC5BH,gBAAgBjJ,MAAM,CACpB,CAAC0C,OAAO,EAAE0C,QAAQ,EAAE,GAAK1C,QAAQ0C,UACjC,KACE6D,gBAAgBhD,MAAM;wBAE5BwC,OAAOhG,IAAI,CAAC;4BACVmB,OAAO,CAAC,EAAE,EAAEsF,UAAU,YAAY,CAAC;4BACnC9D,UAAU;4BACV+D;wBACF;oBACF;gBACF,OAAO;oBACL,MAAMP,eAAeJ,gBAAgB,IAAI,IAAIK,KAAKC,GAAG,CAACN,aAAa;oBACnEC,SAAShH,SAAS8G,aAAa,CAC5BhF,KAAK,CAAC,GAAGqF,cACT7H,GAAG,CAAC,CAAC6C,QAAW,CAAA;4BAAEA;4BAAOwB,UAAU;wBAAE,CAAA;oBACxC,IAAIoD,cAAcI,cAAc;wBAC9B,MAAMM,YAAYV,cAAcI;wBAChCH,OAAOhG,IAAI,CAAC;4BAAEmB,OAAO,CAAC,EAAE,EAAEsF,UAAU,YAAY,CAAC;4BAAE9D,UAAU;wBAAE;oBACjE;gBACF;gBAEAqD,OAAO9B,OAAO,CACZ,CAAC,EAAE/C,KAAK,EAAEwB,QAAQ,EAAE+D,WAAW,EAAE,EAAEd,OAAO,EAAEpC,MAAM,EAAE;wBAIhDzF;oBAHF,MAAM8H,cAAcD,UAAUpC,SAAS,IAAI,MAAM;oBAEjD,MAAMK,uBACJ9F,iBAAAA,UAAUY,GAAG,CAACwC,2BAAdpD,eAAsB8F,mBAAmB;oBAE3CT,SAASpD,IAAI,CAAC;wBACZ,GAAG2F,WAAW,GAAG,EAAEE,YAAY,CAAC,EAAE1E,QAChCwB,WAAWH,eACP,CAAC,EAAE,EAAEC,kBAAkBE,UAAU,CAAC,CAAC,GACnC,KAEJ+D,eAAeA,cAAclE,eACzB,CAAC,MAAM,EAAEC,kBAAkBiE,aAAa,CAAC,CAAC,GAC1C,IACJ;wBACF;wBACA;wBACAjD,kBAAkBI,sBACd0B,IAAAA,wBAAgB,EAAC1B,uBACjB;wBACJH,cAAcG,sBACV2B,IAAAA,oBAAY,EAAC3B,uBACb;qBACL;gBACH;YAEJ;QACF;QAEA,MAAM+C,mBAAkBzH,2BAAAA,MAAMiB,MAAM,CAACkD,WAAW,qBAAxBnE,yBAA0BgB,MAAM,CAACzD,IAAI,CAACuD,KAAK;QAEnE,MAAM4G,cAAc3E,QAAQC,GAAG,CAACC,yCAAyC,GACrE,EAAE,GACFjD,EAAAA,4BAAAA,MAAMiB,MAAM,CAACkD,WAAW,qBAAxBnE,0BAA0BgB,MAAM,CAACnC,KAAK,KAAI,EAAE;QAEhDoF,SAASpD,IAAI,CAAC;YACZ;YACA,OAAO4G,oBAAoB,WACvB7E,cAAc6E,iBAAiB;gBAAE3E,QAAQ;YAAK,KAC9C;YACJ;YACA;YACA;SACD;QACD,MAAM6E,iBAA2B,EAAE;QACnC,MAAMC,iBAAiB;eAClBF,YACA3J,MAAM,CAAC,CAACd;gBACP,IAAIA,KAAKsJ,QAAQ,CAAC,SAAS;oBACzBoB,eAAe9G,IAAI,CAAC5D;oBACpB,OAAO;gBACT;gBACA,OAAO;YACT,GACCkC,GAAG,CAAC,CAACuC,IAAMA,EAAEoC,OAAO,CAACzB,SAAS,cAC9BT,IAAI;eACJ+F,eAAexI,GAAG,CAAC,CAACuC,IAAMA,EAAEoC,OAAO,CAACzB,SAAS,cAAcT,IAAI;SACnE;QAED,0GAA0G;QAC1G,MAAMiG,aAAa,KAAK;QACxB,IAAIC,gBAAgB;QACpB,IAAIC,iBAAiB;QACrBH,eAAe7C,OAAO,CAAC,CAAClB,UAAU4C,OAAO,EAAEpC,MAAM,EAAE;YACjD,MAAMqC,cAAcD,QAAQsB,mBAAmB1D,SAAS,IAAI,MAAM;YAElE,MAAM2D,eAAenE,SAASC,OAAO,CAAC,aAAazB;YACnD,MAAM4F,YAAYrE,aAAaC;YAC/B,MAAMtG,OAAOyC,MAAMmB,KAAK,CAAC3B,GAAG,CAACwI;YAE7B,IAAI,CAACzK,QAAQA,OAAOsK,YAAY;gBAC9BE;gBACAD,iBAAiBvK,QAAQ;gBACzB;YACF;YAEA0G,SAASpD,IAAI,CAAC;gBACZ,CAAC,EAAE,EAAE6F,YAAY,CAAC,EAAEuB,WAAW;gBAC/BrF,cAAcrF;gBACd;gBACA;gBACA;aACD;QACH;QAEA,IAAIwK,iBAAiB,GAAG;YACtB9D,SAASpD,IAAI,CAAC;gBACZ,CAAC,+BAA+B,CAAC;gBACjC+B,cAAckF;gBACd;gBACA;gBACA;aACD;QACH;IACF;IAEA,yDAAyD;IACzD,IAAI1F,MAAMnD,GAAG,IAAIe,MAAMiB,MAAM,CAAChC,GAAG,EAAE;QACjC,MAAMiF,cAAc;YAClBC,YAAY;YACZ5C,MAAMa,MAAMnD,GAAG;QACjB;QAEAgF,SAASpD,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;YAAI;SAAG;IACpC;IAEAjC,UAAUU,GAAG,CAAC,QAAQ;QACpB,GAAIV,UAAUY,GAAG,CAAC,WAAWZ,UAAUY,GAAG,CAAC,UAAU;QACrDuG,UAAUpD;IACZ;IAEA,uFAAuF;IACvF,IACE,CAACP,MAAM3C,KAAK,CAAC6F,QAAQ,CAAC,WACtB,GAAClD,aAAAA,MAAMnD,GAAG,qBAATmD,WAAWkD,QAAQ,CAAC4C,sCAA0B,IAC/C;QACA9F,MAAM3C,KAAK,GAAG;eAAI2C,MAAM3C,KAAK;YAAE;SAAO;IACxC;IAEA,+CAA+C;IAC/C,MAAMyE,cAAc;QAClBC,YAAY;QACZ5C,MAAMa,MAAM3C,KAAK;IACnB;IAEA,MAAM0I,kBAAiBzF,iCAAAA,mBAAmB0F,UAAU,qBAA7B1F,8BAA+B,CAAC,IAAI;IAC3D,IAAIyF,CAAAA,kCAAAA,eAAgBtJ,KAAK,CAACwF,MAAM,IAAG,GAAG;QACpC,MAAMgE,kBAAkB,MAAMpI,QAAQC,GAAG,CACvCiI,eAAetJ,KAAK,CACjBM,GAAG,CAAC,CAACmJ,MAAQ,GAAG5J,SAAS,CAAC,EAAE4J,KAAK,EACjCnJ,GAAG,CAACR,WAAW3B,aAAaS;QAGjCwG,SAASpD,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;YAAI;SAAG;QAClCoD,SAASpD,IAAI,CAAC;YACZ;YACA+B,cAAczE,IAAIkK,kBAAkB;gBAAEvF,QAAQ;YAAK;YACnD;YACA;YACA;SACD;IACH;IAEAnG,MACE4L,IAAAA,kBAAS,EAACtE,UAAU;QAClBuE,OAAO;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI;QAChCC,cAAc,CAACC,MAAQC,IAAAA,kBAAS,EAACD,KAAKrE,MAAM;IAC9C;IAGF,MAAMuE,qBACJxG,MAAMnD,GAAG,IAAIe,MAAMiB,MAAM,CAAChC,GAAG,GAAG,yBAAyB;IAC3DtC;IACAA,MACE4L,IAAAA,kBAAS,EACP;QACEvE,YAAY/F,GAAG,CAAC,QAAQ;YACtB;YACA;YACA;SACD;QACD+F,YAAY/F,GAAG,CAAC,QAAQ;YACtB;YACA;YACA,CAAC,iCAAiC,EAAEiI,IAAAA,gBAAI,EAAC0C,oBAAoB,CAAC,CAAC;SAChE;QACD5E,YAAY/F,GAAG,CAAC,QAAQ;YACtB;YACA;YACA;SACD;QACD+F,YAAY/F,GAAG,CAAC,QAAQ;YAAC;YAAK;YAAa,CAAC,yBAAyB,CAAC;SAAC;KACxE,CAACF,MAAM,CAAC,CAACC,IAAMA,IAChB;QACEwK,OAAO;YAAC;YAAK;YAAK;SAAI;QACtBC,cAAc,CAACC,MAAQC,IAAAA,kBAAS,EAACD,KAAKrE,MAAM;IAC9C;IAIJ1H;AACF;AAEO,SAASJ,kBAAkB,EAChCsM,SAAS,EACTC,QAAQ,EACRC,OAAO,EACM;IACb,MAAMC,cAAc,CAClBnC,QACAoC;QAEA,MAAMC,cAAcD,SAAS;QAC7B,MAAME,YAAYF,SAAS;QAC3BtM,MAAMmI,IAAAA,qBAAS,EAACmE;QAEhB;;;;KAIC,GACD,MAAMG,YAAY,AAACvC,OAChB1H,GAAG,CAAC,CAAC6C;YACJ,IAAIqH,WAAW,CAAC,UAAU,EAAErH,MAAMsH,MAAM,CAAC,EAAE,CAAC;YAE5C,IAAI,CAACH,WAAW;gBACd,MAAMI,IAAIvH;gBACVqH,YAAY,GAAGH,cAAc,MAAM,IAAI,cAAc,EACnDK,EAAEC,WAAW,CACd,EAAE,CAAC;YACN;YACA,IAAIN,aAAa;gBACf,MAAMK,IAAIvH;gBACVqH,YAAY,CAAC,EAAE,EACbE,EAAEE,UAAU,GACR,CAAC,QAAQ,EAAEF,EAAEE,UAAU,EAAE,GACzB,CAAC,WAAW,EAAEF,EAAEG,SAAS,EAAE,CAChC,EAAE,CAAC;YACN;YAEA,IAAIP,WAAW;gBACb,MAAMI,IAAIvH;gBACVqH,YAAY,CAAC,YAAY,CAAC;gBAE1B,IAAK,IAAIpE,IAAI,GAAGA,IAAIsE,EAAER,OAAO,CAAC1E,MAAM,EAAEY,IAAK;oBACzC,MAAM0E,SAASJ,EAAER,OAAO,CAAC9D,EAAE;oBAC3B,MAAM2E,OAAO3E,MAAM8D,QAAQ1E,MAAM,GAAG;oBAEpCgF,YAAY,CAAC,EAAE,EAAEO,OAAO,MAAM,IAAI,CAAC,EAAED,OAAOvK,GAAG,CAAC,EAAE,EAAEuK,OAAOE,KAAK,CAAC,EAAE,CAAC;gBACtE;YACF;YAEA,OAAOR;QACT,GACC/I,IAAI,CAAC;QAER3D,MAAM,GAAGyM,UAAU,EAAE,CAAC;IACxB;IAEAzM;IACA,IAAIkM,UAAUxE,MAAM,EAAE;QACpB2E,YAAYH,WAAW;IACzB;IACA,IAAIE,QAAQ1E,MAAM,EAAE;QAClB2E,YAAYD,SAAS;IACvB;IAEA,MAAMe,mBAAmB;WACpBhB,SAASiB,WAAW;WACpBjB,SAASkB,UAAU;WACnBlB,SAASmB,QAAQ;KACrB;IACD,IAAIH,iBAAiBzF,MAAM,EAAE;QAC3B2E,YAAYc,kBAAkB;IAChC;AACF;AAEO,eAAezO,kBACpB8I,UAAuB,EACvBK,IAAY,EACZ9F,QAAgB,EAChB8D,aAA4B,EAC5BC,gBAAmC,EACnC9D,WAAoB,IAAI,EACxBuL,WAAwC;IAExC,MAAMC,eAAehG,eAAe,UAAU3B,gBAAgBC;IAC9D,IAAI,CAAC0H,cAAc;QACjB,MAAM,qBAA6D,CAA7D,IAAIC,MAAM,qDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA4D;IACpE;IAEA,kCAAkC;IAClC,IAAIjG,eAAe,OAAO;QACxBgG,aAAa1K,KAAK,GAAGX,OAAO2B,OAAO,CAAC0J,aAAa1K,KAAK,EAAErB,MAAM,CAC5D,CAACwC,KAA+B,CAACxB,KAAKyK,MAAM;YAC1C,MAAMQ,SAASC,IAAAA,0BAAgB,EAAClL;YAChCwB,GAAG,CAACyJ,OAAO,GAAGR;YACd,OAAOjJ;QACT,GACA,CAAC;IAEL;IAEA,oDAAoD;IACpD,MAAMZ,QACJkK,eACC,MAAMlP,oBACL;QAAEgE,OAAOwD;QAAevD,KAAKwD;IAAiB,GAC9C/D,UACAC;IAGJ,MAAM4L,WAAWvK,MAAMiB,MAAM,CAACkD,WAAW;IACzC,IAAI,CAACoG,UAAU;QACb,kEAAkE;QAClE,MAAM,qBAAgE,CAAhE,IAAIH,MAAM,wDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA+D;IACvE;IAEA,MAAMI,WACJrG,eAAe,UACXsG,IAAAA,wCAAmB,EAACjG,QACpBkG,IAAAA,0CAAsB,EAAClG;IAE7B,MAAMmG,aAAa,CAAC9F,QAAkBA,MAAM0B,QAAQ,CAAC;IAErD,MAAMqE,YAAY,AAACT,CAAAA,aAAa1K,KAAK,CAAC+K,SAAS,IAAI,EAAE,AAAD,EAAGzM,MAAM,CAAC4M;IAC9D,MAAME,WAAW,AAACV,CAAAA,aAAa1K,KAAK,CAAC,QAAQ,IAAI,EAAE,AAAD,EAAG1B,MAAM,CAAC4M;IAE5D,MAAMG,gBAAgB,CAACxC,MAAgB,GAAG5J,SAAS,CAAC,EAAE4J,KAAK;IAE3D,MAAMyC,eAAerO,OAAOkO,WAAWC,UAAU1L,GAAG,CAAC2L;IACrD,MAAME,gBAAgB7P,WACpB,mEAAmE;IACnE+C,UAAU0M,WAAWL,SAAS7N,MAAM,CAACmC,KAAK,GAC1C,gCAAgC;IAChC0L,SAASvJ,MAAM,CAACnC,KAAK,EACrBM,GAAG,CAAC2L;IAEN,MAAM/K,UAAUpB,WAAW3B,aAAaS;IAExC,2EAA2E;IAC3E,eAAe;IACf,MAAMwN,gBAAgB,OAAOhO;QAC3B,MAAMmC,MAAMnC,KAAK0E,KAAK,CAACjD,SAAS2F,MAAM,GAAG;QACzC,MAAM9G,OAA2ByC,MAAMmB,KAAK,CAAC3B,GAAG,CAACJ;QAEjD,oEAAoE;QACpE,YAAY;QACZ,IAAI,OAAO7B,SAAS,UAAU;YAC5B,OAAOwC,QAAQ9C;QACjB;QAEA,OAAOM;IACT;IAEA,IAAI;QACF,0EAA0E;QAC1E,kEAAkE;QAClE,MAAM2N,eAAe/M,IAAI,MAAM8B,QAAQC,GAAG,CAAC6K,aAAa5L,GAAG,CAAC8L;QAC5D,MAAME,gBAAgBhN,IACpB,MAAM8B,QAAQC,GAAG,CAAC8K,cAAc7L,GAAG,CAAC8L;QAGtC,OAAO;YAACE;YAAeD;SAAa;IACtC,EAAE,OAAM,CAAC;IACT,OAAO;QAAC,CAAC;QAAG,CAAC;KAAE;AACjB;AAkBO,eAAelP,aAAa,EACjCoP,GAAG,EACH5G,IAAI,EACJ6G,OAAO,EACPC,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,YAAY,EACZC,aAAa,EACbC,iBAAiB,EACjBC,SAAS,EACTnK,OAAO,EACPoK,UAAU,EA4BX;IACC,MAAMC,IAAAA,8CAAsB,EAAC;QAC3BL;QACAC;QACAjB;QACAD;QACAuB,aAAaT;QACbU,oBAAoBT;IACtB;IAEA,MAAMU,mBAAmBC,IAAAA,YAAK,EAAC,wBAAwBnB;IACvD,OAAOkB,iBACJE,YAAY,CAAC;QACZC,QAAQ,yCAAyCC,SAAS,CACxD1B;QAEF2B,IAAAA,+CAA4B,EAAC;YAC3B1B;QACF;QAEA,IAAI2B;QACJ,IAAIC;QACJ,IAAIC;QACJ,IAAIC,YAA8B,CAAC;QACnC,IAAIC;QACJ,IAAIC,oBAA6B;QACjC,MAAMC,oBAAoBvL,IAAAA,4BAAa,EAAC0J;QAExC,IAAI6B,mBAAmB;YACrB,MAAMtL,UAAU,MAAMuL,IAAAA,0BAAiB,EAAC;gBACtCC,OAAO9B,SAAShN,KAAK,CAACM,GAAG,CAAC,CAAClC,OAAiBoD,aAAI,CAACC,IAAI,CAAC+K,SAASpO;gBAC/D2Q,mBAAmB;oBACjB,GAAG/B,QAAQ;oBACXgC,MAAM,AAAChC,CAAAA,SAASgC,IAAI,IAAI,EAAE,AAAD,EAAG1O,GAAG,CAAC,CAAC2O,UAA2B,CAAA;4BAC1D,GAAGA,OAAO;4BACVC,UAAU1N,aAAI,CAACC,IAAI,CAAC+K,SAASyC,QAAQC,QAAQ;wBAC/C,CAAA;gBACF;gBACAC,MAAMnC,SAASmC,IAAI;gBACnBC,UAAU;gBACV5C;YACF;YACA,MAAM6C,MAAM,AACV,CAAA,MAAM/L,QAAQgM,OAAO,CAACC,QAAQ,CAAC,CAAC,WAAW,EAAEvC,SAASmC,IAAI,EAAE,CAAC,AAAD,EAC5DK,YAAY;YAEd,qCAAqC;YACrC,MAAM7L,gBAAgB,CAAC;YAEvBgL,oBAAoBc,IAAAA,4CAAiB,EAACJ;YACtCf,mBAAmB;gBACjBoB,WAAWL,IAAIM,OAAO;gBACtBC,UAAUP,IAAIO,QAAQ;gBACtBC,KAAKR,IAAIQ,GAAG;gBACZC,aAAaT,IAAIS,WAAW;gBAC5BnK;gBACA6J,cAAcH;gBACdU,YAAYV,IAAIW,MAAM,IAAI,CAAC;gBAC3BrM;gBACAsM,uBAAuB,CAAC;gBACxBC,oBAAoBb,IAAIa,kBAAkB;gBAC1CC,gBAAgBd,IAAIc,cAAc;gBAClCC,gBAAgBf,IAAIe,cAAc;YACpC;QACF,OAAO;YACL9B,mBAAmB,MAAM+B,IAAAA,8BAAc,EAAC;gBACtC7D;gBACA7G,MAAMyH,mBAAmBzH;gBACzB2K,WAAWrD,aAAa;gBACxBsD,OAAO;gBACP3C;YACF;QACF;QACA,MAAM4C,OAAOlC,iBAAiBoB,SAAS;QAEvC,MAAMI,cAA2BxB,iBAAiBwB,WAAW;QAE7D,IAAIhJ,oBAA6B;QAEjC,IAAImG,aAAa,OAAO;YACtB,MAAMuC,eAA8BlB,iBAAiBkB,YAAY;YAEjEb,oBAAoBc,IAAAA,4CAAiB,EAACnB,iBAAiBkB,YAAY;YAEnE,IAAIiB;YACJ,IAAI;gBACFA,WAAW,MAAMC,IAAAA,4BAAe,EAACpC;YACnC,EAAE,OAAOqC,KAAK;gBACZ,MAAM,qBAEJ,CAFI,IAAIpF,MAAM,CAAC,oCAAoC,EAAE5F,MAAM,EAAE;oBAC7DiL,OAAOD;gBACT,IAFM,qBAAA;2BAAA;gCAAA;kCAAA;gBAEL;YACH;YAEAlC,YAAY7Q,gBAAgB6S;YAE5B,IAAIhC,UAAUoC,OAAO,KAAK,kBAAkBjC,mBAAmB;gBAC7DkC,KAAIC,IAAI,CACN,CAAC,MAAM,EAAEpL,KAAK,gKAAgK,CAAC;YAEnL;YAEA+I,gBAAgBsC,IAAAA,0CAAoB,EAAC1C;YAErC,uEAAuE;YACvE,wEAAwE;YACxE,uBAAuB;YACvBxH,oBACEgJ,YAAYmB,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACC,QAAQ,IAClD,CAACC,IAAAA,8CAA0B,EAAC1L,SAC5B2L,IAAAA,2BAAsB,EAAC3D,WAAWc;YAEpC,uEAAuE;YACvE,mBAAmB;YACnB,yDAAyD;YACzD,IAAIA,UAAUoC,OAAO,KAAK,mBAAmB,CAAC/J,mBAAmB;gBAC/D2H,UAAU3I,UAAU,GAAG;YACzB;YAEA,wEAAwE;YACxE,kEAAkE;YAClE,SAAS;YACT,IAAIyL,IAAAA,yBAAc,EAAC5L,SAAS,CAACiJ,mBAAmB;;gBAC5C,CAAA,EAAEL,iBAAiB,EAAEiD,cAAchD,qBAAqB,EAAE,GAC1D,MAAMiD,IAAAA,wBAAmB,EAAC;oBACxBlF;oBACA5G;oBACAuH;oBACAC;oBACAsD;oBACAjE;oBACAkF,gBAAgB,CAAC;oBACjBrE;oBACAC;oBACAE;oBACAE;oBACA8B;oBACAjC;oBACAzG;oBACAtD;oBACAkL;gBACF,EAAC;YACL;QACF,OAAO;YACL,IAAI,CAAC8B,QAAQ,CAACmB,IAAAA,2BAAkB,EAACnB,SAAS,OAAOA,SAAS,UAAU;gBAClE,MAAM,qBAAmC,CAAnC,IAAIjF,MAAM,2BAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAkC;YAC1C;QACF;QAEA,MAAMqG,qBAAqB,CAAC,EAACpB,wBAAAA,KAAMqB,eAAe;QAClD,MAAMC,iBAAiB,CAAC,CAACxD,iBAAiB8B,cAAc;QACxD,MAAM2B,iBAAiB,CAAC,CAACzD,iBAAiB6B,cAAc;QACxD,MAAM6B,iBAAiB,CAAC,CAAC1D,iBAAiB4B,kBAAkB;QAE5D,uEAAuE;QACvE,iBAAiB;QACjB,IAAI0B,sBAAsBE,gBAAgB;YACxC,MAAM,qBAAyC,CAAzC,IAAIvG,MAAM0G,yCAA8B,GAAxC,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;QAChD;QAEA,IAAIL,sBAAsBI,gBAAgB;YACxC,MAAM,qBAA+C,CAA/C,IAAIzG,MAAM2G,+CAAoC,GAA9C,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;QACtD;QAEA,IAAIJ,kBAAkBE,gBAAgB;YACpC,MAAM,qBAAoC,CAApC,IAAIzG,MAAM4G,oCAAyB,GAAnC,qBAAA;uBAAA;4BAAA;8BAAA;YAAmC;QAC3C;QAEA,MAAMC,gBAAgBb,IAAAA,yBAAc,EAAC5L;QACrC,oEAAoE;QACpE,IAAImM,kBAAkBC,kBAAkB,CAACK,eAAe;YACtD,MAAM,qBAGL,CAHK,IAAI7G,MACR,CAAC,yDAAyD,EAAE5F,KAAK,EAAE,CAAC,GAClE,CAAC,4DAA4D,CAAC,GAF5D,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAImM,kBAAkBM,iBAAiB,CAACL,gBAAgB;YACtD,MAAM,qBAGL,CAHK,IAAIxG,MACR,CAAC,qEAAqE,EAAE5F,KAAK,EAAE,CAAC,GAC9E,CAAC,0EAA0E,CAAC,GAF1E,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAImM,kBAAkBC,gBAAgB;;YAClC,CAAA,EAAExD,iBAAiB,EAAEiD,cAAchD,qBAAqB,EAAE,GAC1D,MAAM6D,IAAAA,4BAAqB,EAAC;gBAC1B1M;gBACAiH;gBACAC;gBACAJ;gBACA0D,gBAAgB7B,iBAAiB6B,cAAc;YACjD,EAAC;QACL;QAEA,MAAMmC,sBAAsB,AAACC,WAAmBC,qBAAqB;QACrE,MAAMxC,SAAqBrB,oBACvB,CAAC,IACDL,iBAAiByB,UAAU;QAE/B,IAAI7I,WAAW;QACf,IAAI,CAAC4K,kBAAkB,CAACF,sBAAsB,CAACI,gBAAgB;YAC7D9K,WAAW;QACb;QAEA,8DAA8D;QAC9D,6BAA6B;QAC7B,IAAIJ,mBAAmB;YACrBI,WAAW;QACb;QAEA,OAAO;YACLA;YACAJ;YACA7F,aAAa+O,OAAOyC,GAAG,KAAK;YAC5BC,WAAW1C,OAAOyC,GAAG,KAAK;YAC1BjE;YACAD;YACAG;YACAoD;YACAE;YACAM;YACA7D;QACF;IACF,GACCkE,KAAK,CAAC,CAAChC;QACN,IAAIA,IAAIiC,OAAO,KAAK,0BAA0B;YAC5C,MAAMjC;QACR;QACA5S,QAAQ8U,KAAK,CAAClC;QACd,MAAM,qBAAoD,CAApD,IAAIpF,MAAM,CAAC,gCAAgC,EAAE5F,MAAM,GAAnD,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;AACJ;AAoBO,SAAS/H,gBACd6S,QAAsC;IAEtC,MAAMT,SAA2B,CAAC;IAElC,KAAK,MAAM8C,WAAWrC,SAAU;QAC9B,MAAM,EACJI,OAAO,EACPkC,UAAU,EACVC,eAAe,EACflN,UAAU,EACVmN,gBAAgB,EAChB3P,OAAO,EACP4P,WAAW,EACZ,GAAGJ,QAAQ9C,MAAM,IAAI,CAAC;QAEvB,uDAAuD;QACvD,6DAA6D;QAE7D,IAAI,OAAOgD,oBAAoB,aAAa;YAC1ChD,OAAOgD,eAAe,GAAGA;QAC3B;QAEA,IAAI,OAAOnC,YAAY,aAAa;YAClCb,OAAOa,OAAO,GAAGA;QACnB;QAEA,IAAI,OAAOkC,eAAe,aAAa;YACrC/C,OAAO+C,UAAU,GAAGA;QACtB;QAEA,IAAI,OAAOjN,eAAe,aAAa;YACrCkK,OAAOlK,UAAU,GAAGA;QACtB;QAEA,0EAA0E;QAC1E,sBAAsB;QACtB,IACE,OAAOA,eAAe,YACrB,CAAA,OAAOkK,OAAOlK,UAAU,KAAK,YAAYA,aAAakK,OAAOlK,UAAU,AAAD,GACvE;YACAkK,OAAOlK,UAAU,GAAGA;QACtB;QAEA,wEAAwE;QACxE,oEAAoE;QACpE,IAAI,OAAOmN,qBAAqB,aAAa;YAC3CjD,OAAOiD,gBAAgB,GAAGA;QAC5B;QAEA,IAAI,OAAO3P,YAAY,aAAa;YAClC0M,OAAO1M,OAAO,GAAGA;QACnB;QAEA,IAAI,OAAO4P,gBAAgB,aAAa;YACtClD,OAAOkD,WAAW,GAAGA;QACvB;IACF;IAEA,OAAOlD;AACT;AAEO,eAAepT,yBAAyB,EAC7C+I,IAAI,EACJ6G,OAAO,EACPE,gBAAgB,EAChByG,WAAW,EACXvF,UAAU,EAOX;IACCO,QAAQ,yCAAyCC,SAAS,CAAC1B;IAE3D,MAAM0G,aAAa,MAAM/C,IAAAA,8BAAc,EAAC;QACtC7D;QACA7G,MAAMA;QACN2K,WAAW;QACXC,OAAO;QACP3C;IACF;IACA,IAAIyB,MAAM+D,WAAW5D,YAAY;IAEjC,IAAI2D,aAAa;QACf9D,MAAM,AAAC,MAAMA,IAAIgE,IAAI,IAAKhE,IAAIM,OAAO,IAAIN;IAC3C,OAAO;QACLA,MAAMA,IAAIM,OAAO,IAAIN;IACvB;IACAA,MAAM,MAAMA;IACZ,OAAOA,IAAIwC,eAAe,KAAKxC,IAAIiE,mBAAmB;AACxD;AAEO,eAAe/W,uBAAuB,EAC3CoJ,IAAI,EACJ6G,OAAO,EACPE,gBAAgB,EAChBkB,UAAU,EAMX;IACCO,QAAQ,yCAAyCC,SAAS,CAAC1B;IAC3D,MAAM0G,aAAa,MAAM/C,IAAAA,8BAAc,EAAC;QACtC7D;QACA7G,MAAMA;QACN2K,WAAW;QACXC,OAAO;QACP3C;IACF;IAEA,OAAO3N,OAAOqB,IAAI,CAAC8R,WAAW5D,YAAY,EAAEtQ,MAAM,CAAC,CAACqB;QAClD,OAAO,OAAO6S,WAAW5D,YAAY,CAACjP,IAAI,KAAK;IACjD;AACF;AAEO,SAASlE,uBACdkX,aAAuB,EACvBC,QAAqB,EACrBC,2BAAkD;IAElD,MAAMC,mBAAmB,IAAI5S;IAQ7B,MAAM6S,kBAAkB;WAAIH;KAAS,CAACtU,MAAM,CAAC,CAACyG,OAAS4L,IAAAA,yBAAc,EAAC5L;IACtE,MAAMiO,2BAEF,CAAC;IAELH,4BAA4BvN,OAAO,CAAC,CAAC4I,OAAO+E;QAC1CD,wBAAwB,CAACC,UAAU,KAAK,CAAC;QACzC/E,MAAM5I,OAAO,CAAC,CAAC4N;YACb,MAAMC,cAAcD,QAAQE,WAAW;YACvCJ,wBAAwB,CAACC,UAAU,CAACE,YAAY,GAAGD;QACrD;IACF;IAEAL,4BAA4BvN,OAAO,CAAC,CAAC4I,OAAO+E;QAC1C/E,MAAM5I,OAAO,CAAC,CAAC4N;YACb,MAAMG,YAAYH,QAAQE,WAAW;YACrC,IAAIE,kBAAkBX,cAAcY,IAAI,CACtC,CAACxO,OAASA,KAAKqO,WAAW,OAAOC;YAGnC,IAAIC,iBAAiB;gBACnBR,iBAAiBjT,GAAG,CAACwT,WAAW;oBAC9B;wBAAEzS,MAAMsS;wBAASnO,MAAMkO;oBAAU;oBACjC;wBAAErS,MAAM0S;wBAAiBvO,MAAMuO;oBAAgB;iBAChD;YACH,OAAO;gBACL,IAAIE;gBAEJF,kBAAkBP,gBAAgBQ,IAAI,CAAC,CAACxO;oBACtC,IAAIA,SAASkO,WAAW,OAAO;oBAE/BO,kBACEX,4BAA4B9S,GAAG,CAACgF,SAAS,OACrCtD,YACAuR,wBAAwB,CAACjO,KAAK,CAACsO,UAAU;oBAC/C,OAAOG;gBACT;gBAEA,IAAIF,mBAAmBE,iBAAiB;oBACtCV,iBAAiBjT,GAAG,CAACwT,WAAW;wBAC9B;4BAAEzS,MAAMsS;4BAASnO,MAAMkO;wBAAU;wBACjC;4BAAErS,MAAM4S;4BAAiBzO,MAAMuO;wBAAgB;qBAChD;gBACH;YACF;QACF;IACF;IAEA,IAAIR,iBAAiBhV,IAAI,GAAG,GAAG;QAC7B,IAAI2V,yBAAyB;QAE7BX,iBAAiBxN,OAAO,CAAC,CAACoO;YACxBA,UAAUpO,OAAO,CAAC,CAACqO,UAAUhM;gBAC3B,MAAMiM,YAAYD,SAAS5O,IAAI,KAAK4O,SAAS/S,IAAI;gBAEjD,IAAI+G,MAAM,GAAG;oBACX8L,0BAA0B;gBAC5B;gBAEAA,0BAA0B,CAAC,OAAO,EAAEE,SAAS/S,IAAI,CAAC,CAAC,EACjDgT,YAAY,CAAC,aAAa,EAAED,SAAS5O,IAAI,CAAC,EAAE,CAAC,GAAG,KAChD;YACJ;YACA0O,0BAA0B;QAC5B;QAEAvD,KAAI+B,KAAK,CACP,qFACE,mFACAwB;QAEJnQ,QAAQuQ,IAAI,CAAC;IACf;AACF;AAEO,eAAerY,gBACpBmQ,GAAW,EACXC,OAAe,EACfkI,QAA2B,EAC3BC,WAA0C,EAC1CC,WAAmB,EACnBC,YAAgC,EAChChR,kBAAsC,EACtCiR,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB;IAExB,MAAMC,aAAazT,aAAI,CAACC,IAAI,CAAC+K,SAAS;IACtC,IAAI0I,aAAa;IACjB,MAAMC,aAAa;QACjB,GAAGN,YAAY;QACfrI,SAAS,CAAC,EAAE,EAAEhL,aAAI,CAAC4T,QAAQ,CAAC7I,KAAKC,UAAU;IAC7C;IACA,IAAI;QACF,MAAM6I,kBAAkB7T,aAAI,CAACC,IAAI,CAAC+K,SAAS;QAC3C,MAAM8I,qBAAqB,MAAM9W,YAAE,CAAC+W,QAAQ,CAACF,iBAAiB;QAC9D,MAAMG,cAAcC,KAAKC,KAAK,CAACJ;QAC/BJ,aAAaM,YAAYpL,IAAI,KAAK;QAElC,oDAAoD;QACpD,qDAAqD;QACrD,MAAMuL,wBAAwBnU,aAAI,CAACC,IAAI,CACrCwT,YACAzT,aAAI,CAAC4T,QAAQ,CAACR,aAAarI,MAC3B;QAEF,MAAM/N,YAAE,CAACoX,KAAK,CAACpU,aAAI,CAACqU,OAAO,CAACF,wBAAwB;YAAEG,WAAW;QAAK;QACtE,MAAMtX,YAAE,CAACuX,SAAS,CAACJ,uBAAuBL;IAC5C,EAAE,OAAM,CAAC;IACT,MAAMU,cAAc,IAAIjX;IACxB,MAAMP,YAAE,CAACyX,EAAE,CAAChB,YAAY;QAAEa,WAAW;QAAMI,OAAO;IAAK;IAEvD,eAAeC,iBAAiBC,aAAqB;QACnD,MAAMC,YAAYZ,KAAKC,KAAK,CAAC,MAAMlX,YAAE,CAAC+W,QAAQ,CAACa,eAAe;QAG9D,MAAME,WAAW,IAAIC,eAAI,CAAC,IAAI;YAAEC,UAAUH,UAAUrW,KAAK,CAACwF,MAAM;QAAC;QACjE,MAAMiR,eAAejV,aAAI,CAACqU,OAAO,CAACO;QAElC,MAAMhV,QAAQC,GAAG,CACfgV,UAAUrW,KAAK,CAACM,GAAG,CAAC,OAAOoW;YACzB,MAAMJ,SAASK,OAAO;YAEtB,MAAMC,iBAAiBpV,aAAI,CAACC,IAAI,CAACgV,cAAcC;YAC/C,MAAMG,iBAAiBrV,aAAI,CAACC,IAAI,CAC9BwT,YACAzT,aAAI,CAAC4T,QAAQ,CAACR,aAAagC;YAG7B,IAAI,CAACZ,YAAY5W,GAAG,CAACyX,iBAAiB;gBACpCb,YAAY5O,GAAG,CAACyP;gBAEhB,MAAMrY,YAAE,CAACoX,KAAK,CAACpU,aAAI,CAACqU,OAAO,CAACgB,iBAAiB;oBAAEf,WAAW;gBAAK;gBAC/D,MAAMgB,UAAU,MAAMtY,YAAE,CAACuY,QAAQ,CAACH,gBAAgBjE,KAAK,CAAC,IAAM;gBAE9D,IAAImE,SAAS;oBACX,IAAI;wBACF,MAAMtY,YAAE,CAACsY,OAAO,CAACA,SAASD;oBAC5B,EAAE,OAAOhU,GAAQ;wBACf,IAAIA,EAAEmU,IAAI,KAAK,UAAU;4BACvB,MAAMnU;wBACR;oBACF;gBACF,OAAO;oBACL,MAAMrE,YAAE,CAACyY,QAAQ,CAACL,gBAAgBC;gBACpC;YACF;YAEA,MAAMP,SAASY,OAAO;QACxB;IAEJ;IAEA,eAAeC,mBAAmBxR,IAA4B;YAa1DA,YACAA;QAbF,eAAeyR,WAAWhZ,IAAY;YACpC,MAAMiZ,eAAe7V,aAAI,CAACC,IAAI,CAAC+K,SAASpO;YACxC,MAAMyY,iBAAiBrV,aAAI,CAACC,IAAI,CAC9BwT,YACAzT,aAAI,CAAC4T,QAAQ,CAACR,aAAapI,UAC3BpO;YAEF,MAAMI,YAAE,CAACoX,KAAK,CAACpU,aAAI,CAACqU,OAAO,CAACgB,iBAAiB;gBAAEf,WAAW;YAAK;YAC/D,MAAMtX,YAAE,CAACyY,QAAQ,CAACI,cAAcR;QAClC;QACA,MAAMzV,QAAQC,GAAG,CAAC;YAChBsE,KAAK3F,KAAK,CAACM,GAAG,CAAC8W;aACfzR,aAAAA,KAAKqJ,IAAI,qBAATrJ,WAAWrF,GAAG,CAAC,CAAClC,OAASgZ,WAAWhZ,KAAK8Q,QAAQ;aACjDvJ,eAAAA,KAAK2R,MAAM,qBAAX3R,aAAarF,GAAG,CAAC,CAAClC,OAASgZ,WAAWhZ,KAAK8Q,QAAQ;SACpD;IACH;IAEA,MAAMqI,uBAAuC,EAAE;IAE/C,KAAK,MAAMhO,cAActJ,OAAOuX,MAAM,CAAC3T,mBAAmB0F,UAAU,EAAG;QACrE,IAAIrM,qBAAqBqM,WAAW4F,IAAI,GAAG;YACzCoI,qBAAqBvV,IAAI,CAACmV,mBAAmB5N;QAC/C;IACF;IAEA,KAAK,MAAM5D,QAAQ1F,OAAOuX,MAAM,CAAC3T,mBAAmB4T,SAAS,EAAG;QAC9DF,qBAAqBvV,IAAI,CAACmV,mBAAmBxR;IAC/C;IAEA,MAAMvE,QAAQC,GAAG,CAACkW;IAElB,KAAK,MAAM5R,QAAQ+O,SAAU;QAC3B,IAAI7Q,mBAAmB4T,SAAS,CAACC,cAAc,CAAC/R,OAAO;YACrD;QACF;QACA,MAAMxC,QAAQwU,IAAAA,oCAAiB,EAAChS;QAEhC,IAAIqP,YAAY5V,GAAG,CAAC+D,QAAQ;YAC1B;QACF;QAEA,MAAMyU,WAAWpW,aAAI,CAACC,IAAI,CACxB+K,SACA,UACA,SACA,GAAGmL,IAAAA,oCAAiB,EAAChS,MAAM,GAAG,CAAC;QAEjC,MAAMkS,gBAAgB,GAAGD,SAAS,SAAS,CAAC;QAC5C,MAAMzB,iBAAiB0B,eAAelF,KAAK,CAAC,CAAChC;YAC3C,IAAIA,IAAIqG,IAAI,KAAK,YAAarR,SAAS,UAAUA,SAAS,QAAS;gBACjEmL,KAAIC,IAAI,CAAC,CAAC,gCAAgC,EAAE6G,UAAU,EAAEjH;YAC1D;QACF;IACF;IAEA,IAAImE,mBAAmB;QACrB,MAAMgD,iBAAiBtW,aAAI,CAACC,IAAI,CAAC+K,SAAS,UAAU;QACpD,MAAMuL,kBAAkB,GAAGD,eAAe,SAAS,CAAC;QACpD,MAAM3B,iBAAiB4B;IACzB;IAEA,IAAIpD,aAAa;QACf,KAAK,MAAMhP,QAAQgP,YAAa;YAC9B,IAAI9Q,mBAAmB4T,SAAS,CAACC,cAAc,CAAC/R,OAAO;gBACrD;YACF;YACA,MAAMiS,WAAWpW,aAAI,CAACC,IAAI,CAAC+K,SAAS,UAAU,OAAO,GAAG7G,KAAK,GAAG,CAAC;YACjE,MAAMkS,gBAAgB,GAAGD,SAAS,SAAS,CAAC;YAC5C,MAAMzB,iBAAiB0B,eAAelF,KAAK,CAAC,CAAChC;gBAC3CG,KAAIC,IAAI,CAAC,CAAC,gCAAgC,EAAE6G,UAAU,EAAEjH;YAC1D;QACF;IACF;IAEA,IAAIoE,wBAAwB;QAC1B,MAAMoB,iBACJ3U,aAAI,CAACC,IAAI,CAAC+K,SAAS,UAAU;IAEjC;IAEA,MAAM2J,iBAAiB3U,aAAI,CAACC,IAAI,CAAC+K,SAAS;IAC1C,MAAMwL,mBAAmBxW,aAAI,CAACC,IAAI,CAChCwT,YACAzT,aAAI,CAAC4T,QAAQ,CAACR,aAAarI,MAC3B;IAEF,MAAM/N,YAAE,CAACoX,KAAK,CAACpU,aAAI,CAACqU,OAAO,CAACmC,mBAAmB;QAAElC,WAAW;IAAK;IAEjE,MAAMtX,YAAE,CAACuX,SAAS,CAChBiC,kBACA,GACE9C,aACI,CAAC;;;;;;AAMX,CAAC,GACS,CAAC,4BAA4B,CAAC,CACnC;;;;;;;;;;;mBAWc,EAAEO,KAAKwC,SAAS,CAAC9C,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;GA0B7C,CAAC;AAEJ;AAEO,SAAS/X,eAAeuI,IAAY;IACzC,OAAO1H,cAAcia,IAAI,CAACvS;AAC5B;AAEO,SAAS9I,yBAAyB8I,IAAY;IACnD,OAAO,8DAA8DuS,IAAI,CACvEvS;AAEJ;AAEO,SAAS7I,kBAAkB6I,IAAY;IAC5C,OAAOA,SAAS,UAAUA,SAAS;AACrC;AAEO,SAAS1I,iBAAiBmB,IAAY;IAC3C,OACEA,SAAS,CAAC,CAAC,EAAEmE,8BAAmB,EAAE,IAAInE,SAAS,CAAC,KAAK,EAAEmE,8BAAmB,EAAE;AAEhF;AAEO,SAASxF,0BAA0BqB,IAAY;IACpD,OACEA,SAAS,CAAC,CAAC,EAAEoE,wCAA6B,EAAE,IAC5CpE,SAAS,CAAC,KAAK,EAAEoE,wCAA6B,EAAE;AAEpD;AAEO,SAAS/F,wCACd0b,MAAc,EACdC,UAAoB;IAEpB,MAAMpY,QAAQ,EAAE;IAChB,KAAK,MAAMqY,aAAaD,WAAY;QAClCpY,MAAMgC,IAAI,CACRR,aAAI,CAACC,IAAI,CAAC0W,QAAQ,GAAG3V,wCAA6B,CAAC,CAAC,EAAE6V,WAAW,GACjE7W,aAAI,CAACC,IAAI,CAAC0W,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG3V,wCAA6B,CAAC,CAAC,EAAE6V,WAAW;IAE5E;IAEA,OAAOrY;AACT;AAEO,SAAStD,+BACdyb,MAAc,EACdC,UAAoB;IAEpB,OAAOA,WAAW9X,GAAG,CAAC,CAAC+X,YACrB7W,aAAI,CAACC,IAAI,CAAC0W,QAAQ,GAAG5V,8BAAmB,CAAC,CAAC,EAAE8V,WAAW;AAE3D;AAEO,MAAMrc,8BAA8BuP;IACzC+M,YACEC,eAAyB,EACzBC,OAAe,EACfC,aAAqB,CACrB;QACA,KAAK,CACH,CAAC,0CAA0C,CAAC,GAC1C,GAAGF,gBAAgBjY,GAAG,CAAC,CAAClC,OAAS,CAAC,KAAK,EAAEA,MAAM,EAAEqD,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/D,CAAC,0CAA0C,EAAED,aAAI,CAACC,IAAI,CACpDD,aAAI,CAACkX,KAAK,CAACC,GAAG,EACdnX,aAAI,CAAC4T,QAAQ,CAACoD,SAAShX,aAAI,CAACoX,OAAO,CAACH,eAAe,QACnD,cACA,WAAW,CAAC,GACd,CAAC,8DAA8D,CAAC;IAEtE;AACF;AAEO,SAAS9b,qBACd4P,GAAW,EACXsM,aAAsB;IAEtB,IAAIC;IACJ,IAAI;QACF,MAAMC,qBAAqBC,qBAAY,CAACC,UAAU,CAAC;YACjDzX,MAAM+K;YACNpI,KAAK0U,gBAAgB,gBAAgB;QACvC;QACA,8FAA8F;QAC9F,IAAIE,sBAAsBA,mBAAmBvT,MAAM,GAAG,GAAG;YACvDsT,WAAWE,IAAAA,qBAAY,EAACD;QAC1B;IACF,EAAE,OAAM,CAAC;IAET,6CAA6C;IAC7C,IAAID,YAAYA,SAAStT,MAAM,GAAG,GAAG;QACnC,OAAOsT;IACT;IAEA,uCAAuC;IACvC,OAAOI,sCAA0B;AACnC;AAEO,SAASzb,yBACd0b,KAA0C;IAE1C,OAAOC,QACLD,SAASE,yBAAc,CAACC,KAAK,CAACC,UAAU,CAAC9S,QAAQ,CAAC0S;AAEtD;AAEO,SAAS5b,yBACd4b,KAA0C;IAE1C,OAAOC,QACLD,SAASE,yBAAc,CAACC,KAAK,CAACE,UAAU,CAAC/S,QAAQ,CAAC0S;AAEtD;AAEO,SAAS3b,sBACd2b,KAA0C;IAE1C,OACEA,UAAU,QACVA,UAAU9W,aACV8W,UAAUE,yBAAc,CAACI,eAAe,IACxCN,UAAUE,yBAAc,CAACK,YAAY,IACrCP,UAAUE,yBAAc,CAACM,YAAY;AAEzC;AAEO,SAASrc,sBACd6b,KAA0C;IAE1C,OAAOC,QAAQD,SAASE,yBAAc,CAACC,KAAK,CAACM,OAAO,CAACnT,QAAQ,CAAC0S;AAChE;AAEO,SAAS9b,uBACd8b,KAA0C;IAE1C,OAAOC,QAAQD,SAASE,yBAAc,CAACC,KAAK,CAACO,QAAQ,CAACpT,QAAQ,CAAC0S;AACjE;AAEO,SAASld,YAAY,EAC1B6d,MAAM,EACN5P,OAAO,EAIR;IAIC,MAAM6P,OAGF,CAAC;IAEL,IAAID,WAAW,KAAK;QAClBC,KAAKD,MAAM,GAAGA;IAChB;IAEA,IAAI5P,WAAWjK,OAAOqB,IAAI,CAAC4I,SAAS1E,MAAM,EAAE;QAC1CuU,KAAK7P,OAAO,GAAG,CAAC;QAEhB,4CAA4C;QAC5C,iCAAiC;QACjC,IAAK,MAAM3J,OAAO2J,QAAS;YACzB,qEAAqE;YACrE,sEAAsE;YACtE,IAAI3J,QAAQ,2BAA2B;YAEvC,IAAIyK,QAAQd,OAAO,CAAC3J,IAAI;YAExB,IAAIyZ,MAAMC,OAAO,CAACjP,QAAQ;gBACxB,IAAIzK,QAAQ,cAAc;oBACxByK,QAAQA,MAAMvJ,IAAI,CAAC;gBACrB,OAAO;oBACLuJ,QAAQA,KAAK,CAACA,MAAMxF,MAAM,GAAG,EAAE;gBACjC;YACF;YAEA,IAAI,OAAOwF,UAAU,UAAU;gBAC7B+O,KAAK7P,OAAO,CAAC3J,IAAI,GAAGyK;YACtB;QACF;IACF;IAEA,OAAO+O;AACT"}