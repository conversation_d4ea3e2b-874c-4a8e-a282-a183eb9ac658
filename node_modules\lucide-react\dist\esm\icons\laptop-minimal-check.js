/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M2 20h20", key: "owomy5" }],
  ["path", { d: "m9 10 2 2 4-4", key: "1gnqz4" }],
  ["rect", { x: "3", y: "4", width: "18", height: "12", rx: "2", key: "8ur36m" }]
];
const LaptopMinimalCheck = createLucideIcon("laptop-minimal-check", __iconNode);

export { __iconNode, LaptopMinimalCheck as default };
//# sourceMappingURL=laptop-minimal-check.js.map
