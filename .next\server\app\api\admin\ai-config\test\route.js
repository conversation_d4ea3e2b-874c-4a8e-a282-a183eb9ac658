/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/ai-config/test/route";
exports.ids = ["app/api/admin/ai-config/test/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_admin_ai_config_test_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/ai-config/test/route.ts */ \"(rsc)/./src/app/api/admin/ai-config/test/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/ai-config/test/route\",\n        pathname: \"/api/admin/ai-config/test\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/ai-config/test/route\"\n    },\n    resolvedPagePath: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\api\\\\admin\\\\ai-config\\\\test\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_admin_ai_config_test_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/ai-config/test/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/admin/ai-config/test/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ai-service */ \"(rsc)/./src/lib/ai-service.ts\");\n\n\n\n\n// POST - Probar conexión con proveedores de IA\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== 'ADMIN') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { provider, apiKey } = body;\n        if (!provider || ![\n            'OPENAI',\n            'GEMINI'\n        ].includes(provider)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Proveedor inválido'\n            }, {\n                status: 400\n            });\n        }\n        let isConnected = false;\n        let error = null;\n        try {\n            console.log(`🧪 Iniciando prueba de conexión para ${provider}`);\n            if (provider === 'OPENAI') {\n                isConnected = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_3__.testOpenAIConnection)();\n            } else if (provider === 'GEMINI') {\n                isConnected = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_3__.testGeminiConnection)();\n            }\n            console.log(`🧪 Resultado de prueba ${provider}:`, {\n                isConnected,\n                error\n            });\n        } catch (testError) {\n            console.error(`❌ Error en prueba de ${provider}:`, testError);\n            error = testError instanceof Error ? testError.message : 'Error desconocido';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            provider,\n            isConnected,\n            error,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Error testing AI connection:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/ai-config/test/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ai-service.ts":
/*!*******************************!*\
  !*** ./src/lib/ai-service.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAIConfig: () => (/* binding */ getAIConfig),\n/* harmony export */   rewriteNoticia: () => (/* binding */ rewriteNoticia),\n/* harmony export */   testGeminiConnection: () => (/* binding */ testGeminiConnection),\n/* harmony export */   testOpenAIConnection: () => (/* binding */ testOpenAIConnection)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/.pnpm/openai@5.10.1_zod@3.25.75/node_modules/openai/index.mjs\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_2__.PrismaClient();\n// Obtener configuración de IA\nasync function getAIConfig() {\n    let config = await prisma.aIConfig.findFirst({\n        where: {\n            isActive: true\n        },\n        orderBy: {\n            createdAt: 'desc'\n        }\n    });\n    // Migrar modelos obsoletos\n    if (config && (config.geminiModel === 'gemini-pro' || config.geminiModel === 'gemini-1.5-flash')) {\n        const newModel = 'gemini-2.5-flash';\n        console.log(`🔄 Migrando modelo Gemini obsoleto de ${config.geminiModel} a ${newModel}`);\n        config = await prisma.aIConfig.update({\n            where: {\n                id: config.id\n            },\n            data: {\n                geminiModel: newModel\n            }\n        });\n    }\n    if (!config) {\n        // Configuración por defecto\n        return {\n            openaiApiKey: process.env.OPENAI_API_KEY,\n            openaiModel: 'gpt-3.5-turbo',\n            openaiMaxTokens: 2000,\n            openaiTemperature: 0.7,\n            geminiApiKey: process.env.GEMINI_API_KEY,\n            geminiModel: 'gemini-2.5-flash',\n            geminiMaxTokens: 2000,\n            geminiTemperature: 0.7,\n            defaultProvider: 'OPENAI'\n        };\n    }\n    return {\n        openaiApiKey: config.openaiApiKey || process.env.OPENAI_API_KEY,\n        openaiModel: config.openaiModel,\n        openaiMaxTokens: config.openaiMaxTokens,\n        openaiTemperature: config.openaiTemperature,\n        geminiApiKey: config.geminiApiKey || process.env.GEMINI_API_KEY,\n        geminiModel: config.geminiModel,\n        geminiMaxTokens: config.geminiMaxTokens,\n        geminiTemperature: config.geminiTemperature,\n        defaultProvider: config.defaultProvider\n    };\n}\n// Reescribir noticia usando OpenAI\nasync function rewriteWithOpenAI(request, config) {\n    const startTime = Date.now();\n    if (!config.openaiApiKey) {\n        throw new Error('OpenAI API key no configurada');\n    }\n    const openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        apiKey: config.openaiApiKey\n    });\n    const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- NO generes subtítulo, solo volanta, título, resumen y contenido\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n    const completion = await openai.chat.completions.create({\n        model: config.openaiModel,\n        messages: [\n            {\n                role: \"system\",\n                content: \"Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional.\"\n            },\n            {\n                role: \"user\",\n                content: fullPrompt\n            }\n        ],\n        temperature: config.openaiTemperature,\n        max_tokens: config.openaiMaxTokens\n    });\n    const responseText = completion.choices[0]?.message?.content;\n    if (!responseText) {\n        throw new Error('No se recibió respuesta de OpenAI');\n    }\n    // Intentar parsear la respuesta JSON\n    let parsedResponse;\n    try {\n        parsedResponse = JSON.parse(responseText);\n    } catch (parseError) {\n        // Si falla el parsing, intentar extraer JSON del texto\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            parsedResponse = JSON.parse(jsonMatch[0]);\n        } else {\n            throw new Error('La respuesta de OpenAI no está en formato JSON válido');\n        }\n    }\n    const endTime = Date.now();\n    const generationTime = endTime - startTime;\n    return {\n        titulo: parsedResponse.titulo || request.titulo,\n        volanta: parsedResponse.volanta,\n        contenido: parsedResponse.contenido || request.contenido,\n        resumen: parsedResponse.resumen,\n        metadatos: {\n            modelo: completion.model,\n            tokens_usados: completion.usage?.total_tokens || 0,\n            tiempo_generacion: generationTime,\n            diario: request.diarioNombre,\n            proveedor: 'OpenAI'\n        }\n    };\n}\n// Reescribir noticia usando Google Gemini\nasync function rewriteWithGemini(request, config) {\n    const startTime = Date.now();\n    if (!config.geminiApiKey) {\n        throw new Error('Google Gemini API key no configurada');\n    }\n    const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(config.geminiApiKey);\n    const model = genAI.getGenerativeModel({\n        model: config.geminiModel,\n        generationConfig: {\n            temperature: config.geminiTemperature,\n            maxOutputTokens: config.geminiMaxTokens\n        }\n    });\n    const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- NO generes subtítulo, solo volanta, título, resumen y contenido\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n    const result = await model.generateContent(fullPrompt);\n    const response = await result.response;\n    const responseText = response.text();\n    if (!responseText) {\n        throw new Error('No se recibió respuesta de Google Gemini');\n    }\n    // Intentar parsear la respuesta JSON\n    let parsedResponse;\n    try {\n        parsedResponse = JSON.parse(responseText);\n    } catch (parseError) {\n        // Si falla el parsing, intentar extraer JSON del texto\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            parsedResponse = JSON.parse(jsonMatch[0]);\n        } else {\n            throw new Error('La respuesta de Google Gemini no está en formato JSON válido');\n        }\n    }\n    const endTime = Date.now();\n    const generationTime = endTime - startTime;\n    return {\n        titulo: parsedResponse.titulo || request.titulo,\n        volanta: parsedResponse.volanta,\n        contenido: parsedResponse.contenido || request.contenido,\n        resumen: parsedResponse.resumen,\n        metadatos: {\n            modelo: config.geminiModel,\n            tokens_usados: 0,\n            tiempo_generacion: generationTime,\n            diario: request.diarioNombre,\n            proveedor: 'Google Gemini'\n        }\n    };\n}\n// Función principal para reescribir noticias\nasync function rewriteNoticia(request, diarioId) {\n    try {\n        const config = await getAIConfig();\n        let provider = config.defaultProvider;\n        // Si se especifica un diario, verificar su configuración específica\n        if (diarioId) {\n            const diario = await prisma.diario.findUnique({\n                where: {\n                    id: diarioId\n                }\n            });\n            if (diario && !diario.useGlobalConfig && diario.aiProvider) {\n                provider = diario.aiProvider;\n                // Si el diario tiene un modelo específico, usarlo\n                if (diario.aiModel) {\n                    if (provider === 'OPENAI') {\n                        config.openaiModel = diario.aiModel;\n                    } else {\n                        config.geminiModel = diario.aiModel;\n                    }\n                }\n            }\n        }\n        // Ejecutar reescritura según el proveedor\n        if (provider === 'GEMINI') {\n            return await rewriteWithGemini(request, config);\n        } else {\n            return await rewriteWithOpenAI(request, config);\n        }\n    } catch (error) {\n        console.error('Error en rewriteNoticia:', error);\n        throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);\n    }\n}\n// Función para probar conexión con OpenAI\nasync function testOpenAIConnection() {\n    try {\n        console.log('🔍 Probando conexión con OpenAI...');\n        const config = await getAIConfig();\n        const keyToUse = config.openaiApiKey || process.env.OPENAI_API_KEY;\n        console.log('🔑 API Key disponible:', !!keyToUse);\n        console.log('📝 Modelo a usar:', config.openaiModel);\n        if (!keyToUse) {\n            console.error('❌ No hay API key disponible');\n            return false;\n        }\n        const openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            apiKey: keyToUse\n        });\n        console.log('📡 Enviando petición de prueba...');\n        const completion = await openai.chat.completions.create({\n            model: config.openaiModel,\n            messages: [\n                {\n                    role: \"user\",\n                    content: \"Responde solo con 'OK'\"\n                }\n            ],\n            max_tokens: 5,\n            temperature: 0\n        });\n        console.log('📥 Respuesta recibida:', completion.choices[0]?.message?.content);\n        const isOk = completion.choices[0]?.message?.content?.includes('OK') || false;\n        console.log('✅ Conexión exitosa:', isOk);\n        return isOk;\n    } catch (error) {\n        console.error('❌ Error testing OpenAI connection:', error);\n        if (error instanceof Error) {\n            console.error('❌ Error message:', error.message);\n            console.error('❌ Error stack:', error.stack);\n        }\n        return false;\n    }\n}\n// Función para probar conexión con Google Gemini\nasync function testGeminiConnection() {\n    try {\n        console.log('🔍 Probando conexión con Google Gemini...');\n        const config = await getAIConfig();\n        const keyToUse = config.geminiApiKey || process.env.GEMINI_API_KEY;\n        console.log('🔑 API Key disponible:', !!keyToUse);\n        console.log('📝 Modelo a usar:', config.geminiModel);\n        if (!keyToUse) {\n            console.error('❌ No hay API key disponible para Gemini');\n            return false;\n        }\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(keyToUse);\n        const model = genAI.getGenerativeModel({\n            model: config.geminiModel,\n            generationConfig: {\n                temperature: 0,\n                maxOutputTokens: 10\n            }\n        });\n        console.log('📡 Enviando petición de prueba a Gemini...');\n        const result = await model.generateContent(\"Responde solo con 'OK'\");\n        const response = await result.response;\n        const text = response.text();\n        console.log('📥 Respuesta recibida de Gemini:', text);\n        const isOk = text.includes('OK');\n        console.log('✅ Conexión Gemini exitosa:', isOk);\n        return isOk;\n    } catch (error) {\n        console.error('❌ Error testing Gemini connection:', error);\n        if (error instanceof Error) {\n            console.error('❌ Error message:', error.message);\n            console.error('❌ Error stack:', error.stack);\n        }\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ai-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const user = await prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        }\n                    });\n                    if (!user) {\n                        return null;\n                    }\n                    const isPasswordValid = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        return null;\n                    }\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error during authentication:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    },\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@babel+runtime@7.27.6","vendor-chunks/jose@4.15.9","vendor-chunks/openid-client@5.7.1","vendor-chunks/oauth@0.9.15","vendor-chunks/object-hash@2.2.0","vendor-chunks/preact@10.26.9","vendor-chunks/uuid@8.3.2","vendor-chunks/yallist@4.0.0","vendor-chunks/preact-render-to-string@5.2.6_preact@10.26.9","vendor-chunks/lru-cache@6.0.0","vendor-chunks/cookie@0.7.2","vendor-chunks/oidc-token-hash@5.1.0","vendor-chunks/@panva+hkdf@1.2.1","vendor-chunks/openai@5.10.1_zod@3.25.75","vendor-chunks/@google+generative-ai@0.24.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();