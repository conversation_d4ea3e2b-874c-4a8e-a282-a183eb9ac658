/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/ai-config/test/route";
exports.ids = ["app/api/admin/ai-config/test/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_admin_ai_config_test_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/ai-config/test/route.ts */ \"(rsc)/./src/app/api/admin/ai-config/test/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/ai-config/test/route\",\n        pathname: \"/api/admin/ai-config/test\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/ai-config/test/route\"\n    },\n    resolvedPagePath: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\api\\\\admin\\\\ai-config\\\\test\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_admin_ai_config_test_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/ai-config/test/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/admin/ai-config/test/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ai-service */ \"(rsc)/./src/lib/ai-service.ts\");\n\n\n\n\n// POST - Probar conexión con proveedores de IA\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== 'ADMIN') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { provider, apiKey } = body;\n        if (!provider || ![\n            'OPENAI',\n            'GEMINI'\n        ].includes(provider)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Proveedor inválido'\n            }, {\n                status: 400\n            });\n        }\n        let isConnected = false;\n        let error = null;\n        try {\n            console.log(`🧪 Iniciando prueba de conexión para ${provider}`);\n            if (provider === 'OPENAI') {\n                isConnected = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_3__.testOpenAIConnection)(apiKey);\n            } else if (provider === 'GEMINI') {\n                isConnected = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_3__.testGeminiConnection)(apiKey);\n            }\n            console.log(`🧪 Resultado de prueba ${provider}:`, {\n                isConnected,\n                error\n            });\n        } catch (testError) {\n            console.error(`❌ Error en prueba de ${provider}:`, testError);\n            error = testError instanceof Error ? testError.message : 'Error desconocido';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            provider,\n            isConnected,\n            error,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Error testing AI connection:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/ai-config/test/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ai-service.ts":
/*!*******************************!*\
  !*** ./src/lib/ai-service.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAIConfig: () => (/* binding */ getAIConfig),\n/* harmony export */   rewriteNoticia: () => (/* binding */ rewriteNoticia),\n/* harmony export */   testGeminiConnection: () => (/* binding */ testGeminiConnection),\n/* harmony export */   testOpenAIConnection: () => (/* binding */ testOpenAIConnection)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/.pnpm/openai@5.10.1_zod@3.25.75/node_modules/openai/index.mjs\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_2__.PrismaClient();\n// Obtener configuración de IA\nasync function getAIConfig() {\n    const config = await prisma.aIConfig.findFirst({\n        where: {\n            isActive: true\n        },\n        orderBy: {\n            createdAt: 'desc'\n        }\n    });\n    if (!config) {\n        // Configuración por defecto\n        return {\n            openaiApiKey: process.env.OPENAI_API_KEY,\n            openaiModel: 'gpt-3.5-turbo',\n            openaiMaxTokens: 2000,\n            openaiTemperature: 0.7,\n            geminiApiKey: process.env.GEMINI_API_KEY,\n            geminiModel: 'gemini-pro',\n            geminiMaxTokens: 2000,\n            geminiTemperature: 0.7,\n            defaultProvider: 'OPENAI'\n        };\n    }\n    return {\n        openaiApiKey: config.openaiApiKey || process.env.OPENAI_API_KEY,\n        openaiModel: config.openaiModel,\n        openaiMaxTokens: config.openaiMaxTokens,\n        openaiTemperature: config.openaiTemperature,\n        geminiApiKey: config.geminiApiKey || process.env.GEMINI_API_KEY,\n        geminiModel: config.geminiModel,\n        geminiMaxTokens: config.geminiMaxTokens,\n        geminiTemperature: config.geminiTemperature,\n        defaultProvider: config.defaultProvider\n    };\n}\n// Reescribir noticia usando OpenAI\nasync function rewriteWithOpenAI(request, config) {\n    const startTime = Date.now();\n    if (!config.openaiApiKey) {\n        throw new Error('OpenAI API key no configurada');\n    }\n    const openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        apiKey: config.openaiApiKey\n    });\n    const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- NO generes subtítulo, solo volanta, título, resumen y contenido\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n    const completion = await openai.chat.completions.create({\n        model: config.openaiModel,\n        messages: [\n            {\n                role: \"system\",\n                content: \"Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional.\"\n            },\n            {\n                role: \"user\",\n                content: fullPrompt\n            }\n        ],\n        temperature: config.openaiTemperature,\n        max_tokens: config.openaiMaxTokens\n    });\n    const responseText = completion.choices[0]?.message?.content;\n    if (!responseText) {\n        throw new Error('No se recibió respuesta de OpenAI');\n    }\n    // Intentar parsear la respuesta JSON\n    let parsedResponse;\n    try {\n        parsedResponse = JSON.parse(responseText);\n    } catch (parseError) {\n        // Si falla el parsing, intentar extraer JSON del texto\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            parsedResponse = JSON.parse(jsonMatch[0]);\n        } else {\n            throw new Error('La respuesta de OpenAI no está en formato JSON válido');\n        }\n    }\n    const endTime = Date.now();\n    const generationTime = endTime - startTime;\n    return {\n        titulo: parsedResponse.titulo || request.titulo,\n        volanta: parsedResponse.volanta,\n        contenido: parsedResponse.contenido || request.contenido,\n        resumen: parsedResponse.resumen,\n        metadatos: {\n            modelo: completion.model,\n            tokens_usados: completion.usage?.total_tokens || 0,\n            tiempo_generacion: generationTime,\n            diario: request.diarioNombre,\n            proveedor: 'OpenAI'\n        }\n    };\n}\n// Reescribir noticia usando Google Gemini\nasync function rewriteWithGemini(request, config) {\n    const startTime = Date.now();\n    if (!config.geminiApiKey) {\n        throw new Error('Google Gemini API key no configurada');\n    }\n    const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(config.geminiApiKey);\n    const model = genAI.getGenerativeModel({\n        model: config.geminiModel,\n        generationConfig: {\n            temperature: config.geminiTemperature,\n            maxOutputTokens: config.geminiMaxTokens\n        }\n    });\n    const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- NO generes subtítulo, solo volanta, título, resumen y contenido\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n    const result = await model.generateContent(fullPrompt);\n    const response = await result.response;\n    const responseText = response.text();\n    if (!responseText) {\n        throw new Error('No se recibió respuesta de Google Gemini');\n    }\n    // Intentar parsear la respuesta JSON\n    let parsedResponse;\n    try {\n        parsedResponse = JSON.parse(responseText);\n    } catch (parseError) {\n        // Si falla el parsing, intentar extraer JSON del texto\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            parsedResponse = JSON.parse(jsonMatch[0]);\n        } else {\n            throw new Error('La respuesta de Google Gemini no está en formato JSON válido');\n        }\n    }\n    const endTime = Date.now();\n    const generationTime = endTime - startTime;\n    return {\n        titulo: parsedResponse.titulo || request.titulo,\n        volanta: parsedResponse.volanta,\n        contenido: parsedResponse.contenido || request.contenido,\n        resumen: parsedResponse.resumen,\n        metadatos: {\n            modelo: config.geminiModel,\n            tokens_usados: 0,\n            tiempo_generacion: generationTime,\n            diario: request.diarioNombre,\n            proveedor: 'Google Gemini'\n        }\n    };\n}\n// Función principal para reescribir noticias\nasync function rewriteNoticia(request, diarioId) {\n    try {\n        const config = await getAIConfig();\n        let provider = config.defaultProvider;\n        // Si se especifica un diario, verificar su configuración específica\n        if (diarioId) {\n            const diario = await prisma.diario.findUnique({\n                where: {\n                    id: diarioId\n                }\n            });\n            if (diario && !diario.useGlobalConfig && diario.aiProvider) {\n                provider = diario.aiProvider;\n                // Si el diario tiene un modelo específico, usarlo\n                if (diario.aiModel) {\n                    if (provider === 'OPENAI') {\n                        config.openaiModel = diario.aiModel;\n                    } else {\n                        config.geminiModel = diario.aiModel;\n                    }\n                }\n            }\n        }\n        // Ejecutar reescritura según el proveedor\n        if (provider === 'GEMINI') {\n            return await rewriteWithGemini(request, config);\n        } else {\n            return await rewriteWithOpenAI(request, config);\n        }\n    } catch (error) {\n        console.error('Error en rewriteNoticia:', error);\n        throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);\n    }\n}\n// Función para probar conexión con OpenAI\nasync function testOpenAIConnection(apiKey) {\n    try {\n        console.log('🔍 Probando conexión con OpenAI...');\n        const config = await getAIConfig();\n        const keyToUse = apiKey || config.openaiApiKey || process.env.OPENAI_API_KEY;\n        console.log('🔑 API Key disponible:', !!keyToUse);\n        console.log('📝 Modelo a usar:', config.openaiModel);\n        if (!keyToUse) {\n            console.error('❌ No hay API key disponible');\n            return false;\n        }\n        const openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            apiKey: keyToUse\n        });\n        console.log('📡 Enviando petición de prueba...');\n        const completion = await openai.chat.completions.create({\n            model: config.openaiModel,\n            messages: [\n                {\n                    role: \"user\",\n                    content: \"Responde solo con 'OK'\"\n                }\n            ],\n            max_tokens: 5,\n            temperature: 0\n        });\n        console.log('📥 Respuesta recibida:', completion.choices[0]?.message?.content);\n        const isOk = completion.choices[0]?.message?.content?.includes('OK') || false;\n        console.log('✅ Conexión exitosa:', isOk);\n        return isOk;\n    } catch (error) {\n        console.error('❌ Error testing OpenAI connection:', error);\n        if (error instanceof Error) {\n            console.error('❌ Error message:', error.message);\n            console.error('❌ Error stack:', error.stack);\n        }\n        return false;\n    }\n}\n// Función para probar conexión con Google Gemini\nasync function testGeminiConnection(apiKey) {\n    try {\n        console.log('🔍 Probando conexión con Google Gemini...');\n        const config = await getAIConfig();\n        const keyToUse = apiKey || config.geminiApiKey || process.env.GEMINI_API_KEY;\n        console.log('🔑 API Key disponible:', !!keyToUse);\n        console.log('📝 Modelo a usar:', config.geminiModel);\n        if (!keyToUse) {\n            console.error('❌ No hay API key disponible para Gemini');\n            return false;\n        }\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(keyToUse);\n        const model = genAI.getGenerativeModel({\n            model: config.geminiModel,\n            generationConfig: {\n                temperature: 0,\n                maxOutputTokens: 10\n            }\n        });\n        console.log('📡 Enviando petición de prueba a Gemini...');\n        const result = await model.generateContent(\"Responde solo con 'OK'\");\n        const response = await result.response;\n        const text = response.text();\n        console.log('📥 Respuesta recibida de Gemini:', text);\n        const isOk = text.includes('OK');\n        console.log('✅ Conexión Gemini exitosa:', isOk);\n        return isOk;\n    } catch (error) {\n        console.error('❌ Error testing Gemini connection:', error);\n        if (error instanceof Error) {\n            console.error('❌ Error message:', error.message);\n            console.error('❌ Error stack:', error.stack);\n        }\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ai-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const user = await prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        }\n                    });\n                    if (!user) {\n                        return null;\n                    }\n                    const isPasswordValid = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        return null;\n                    }\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error during authentication:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    },\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@babel+runtime@7.27.6","vendor-chunks/jose@4.15.9","vendor-chunks/openid-client@5.7.1","vendor-chunks/oauth@0.9.15","vendor-chunks/object-hash@2.2.0","vendor-chunks/preact@10.26.9","vendor-chunks/uuid@8.3.2","vendor-chunks/yallist@4.0.0","vendor-chunks/preact-render-to-string@5.2.6_preact@10.26.9","vendor-chunks/lru-cache@6.0.0","vendor-chunks/cookie@0.7.2","vendor-chunks/oidc-token-hash@5.1.0","vendor-chunks/@panva+hkdf@1.2.1","vendor-chunks/openai@5.10.1_zod@3.25.75","vendor-chunks/@google+generative-ai@0.24.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();