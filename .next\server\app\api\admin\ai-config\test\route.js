/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/ai-config/test/route";
exports.ids = ["app/api/admin/ai-config/test/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_admin_ai_config_test_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/ai-config/test/route.ts */ \"(rsc)/./src/app/api/admin/ai-config/test/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/ai-config/test/route\",\n        pathname: \"/api/admin/ai-config/test\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/ai-config/test/route\"\n    },\n    resolvedPagePath: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\api\\\\admin\\\\ai-config\\\\test\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_DEL_SUR_FINAL_panel_unificado_version_2_src_app_api_admin_ai_config_test_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/ai-config/test/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/admin/ai-config/test/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ai-service */ \"(rsc)/./src/lib/ai-service.ts\");\n\n\n\n\n// POST - Probar conexión con proveedores de IA\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== 'ADMIN') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { provider, apiKey } = body;\n        if (!provider || ![\n            'OPENAI',\n            'GEMINI'\n        ].includes(provider)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Proveedor inválido'\n            }, {\n                status: 400\n            });\n        }\n        let isConnected = false;\n        let error = null;\n        try {\n            if (provider === 'OPENAI') {\n                isConnected = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_3__.testOpenAIConnection)(apiKey);\n            } else if (provider === 'GEMINI') {\n                isConnected = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_3__.testGeminiConnection)(apiKey);\n            }\n        } catch (testError) {\n            error = testError instanceof Error ? testError.message : 'Error desconocido';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            provider,\n            isConnected,\n            error,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Error testing AI connection:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/ai-config/test/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ai-service.ts":
/*!*******************************!*\
  !*** ./src/lib/ai-service.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAIConfig: () => (/* binding */ getAIConfig),\n/* harmony export */   rewriteNoticia: () => (/* binding */ rewriteNoticia),\n/* harmony export */   testGeminiConnection: () => (/* binding */ testGeminiConnection),\n/* harmony export */   testOpenAIConnection: () => (/* binding */ testOpenAIConnection)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/.pnpm/openai@5.10.1_zod@3.25.75/node_modules/openai/index.mjs\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_2__.PrismaClient();\n// Obtener configuración de IA\nasync function getAIConfig() {\n    const config = await prisma.aIConfig.findFirst({\n        where: {\n            isActive: true\n        },\n        orderBy: {\n            createdAt: 'desc'\n        }\n    });\n    if (!config) {\n        // Configuración por defecto\n        return {\n            openaiApiKey: process.env.OPENAI_API_KEY,\n            openaiModel: 'gpt-3.5-turbo',\n            openaiMaxTokens: 2000,\n            openaiTemperature: 0.7,\n            geminiApiKey: process.env.GEMINI_API_KEY,\n            geminiModel: 'gemini-pro',\n            geminiMaxTokens: 2000,\n            geminiTemperature: 0.7,\n            defaultProvider: 'OPENAI'\n        };\n    }\n    return {\n        openaiApiKey: config.openaiApiKey || process.env.OPENAI_API_KEY,\n        openaiModel: config.openaiModel,\n        openaiMaxTokens: config.openaiMaxTokens,\n        openaiTemperature: config.openaiTemperature,\n        geminiApiKey: config.geminiApiKey || process.env.GEMINI_API_KEY,\n        geminiModel: config.geminiModel,\n        geminiMaxTokens: config.geminiMaxTokens,\n        geminiTemperature: config.geminiTemperature,\n        defaultProvider: config.defaultProvider\n    };\n}\n// Reescribir noticia usando OpenAI\nasync function rewriteWithOpenAI(request, config) {\n    const startTime = Date.now();\n    if (!config.openaiApiKey) {\n        throw new Error('OpenAI API key no configurada');\n    }\n    const openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        apiKey: config.openaiApiKey\n    });\n    const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- NO generes subtítulo, solo volanta, título, resumen y contenido\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n    const completion = await openai.chat.completions.create({\n        model: config.openaiModel,\n        messages: [\n            {\n                role: \"system\",\n                content: \"Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional.\"\n            },\n            {\n                role: \"user\",\n                content: fullPrompt\n            }\n        ],\n        temperature: config.openaiTemperature,\n        max_tokens: config.openaiMaxTokens\n    });\n    const responseText = completion.choices[0]?.message?.content;\n    if (!responseText) {\n        throw new Error('No se recibió respuesta de OpenAI');\n    }\n    // Intentar parsear la respuesta JSON\n    let parsedResponse;\n    try {\n        parsedResponse = JSON.parse(responseText);\n    } catch (parseError) {\n        // Si falla el parsing, intentar extraer JSON del texto\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            parsedResponse = JSON.parse(jsonMatch[0]);\n        } else {\n            throw new Error('La respuesta de OpenAI no está en formato JSON válido');\n        }\n    }\n    const endTime = Date.now();\n    const generationTime = endTime - startTime;\n    return {\n        titulo: parsedResponse.titulo || request.titulo,\n        volanta: parsedResponse.volanta,\n        contenido: parsedResponse.contenido || request.contenido,\n        resumen: parsedResponse.resumen,\n        metadatos: {\n            modelo: completion.model,\n            tokens_usados: completion.usage?.total_tokens || 0,\n            tiempo_generacion: generationTime,\n            diario: request.diarioNombre,\n            proveedor: 'OpenAI'\n        }\n    };\n}\n// Reescribir noticia usando Google Gemini\nasync function rewriteWithGemini(request, config) {\n    const startTime = Date.now();\n    if (!config.geminiApiKey) {\n        throw new Error('Google Gemini API key no configurada');\n    }\n    const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(config.geminiApiKey);\n    const model = genAI.getGenerativeModel({\n        model: config.geminiModel,\n        generationConfig: {\n            temperature: config.geminiTemperature,\n            maxOutputTokens: config.geminiMaxTokens\n        }\n    });\n    const fullPrompt = `${request.prompt}\n\nNOTICIA ORIGINAL:\nVolanta: ${request.volanta || 'Sin volanta'}\nTítulo: ${request.titulo}\nSubtítulo: ${request.subtitulo || 'Sin subtítulo'}\nResumen: ${request.resumen || 'Sin resumen'}\nContenido: ${request.contenido}\n\nINSTRUCCIONES:\n- Reescribe completamente la noticia para ${request.diarioNombre}\n- Mantén la información factual pero adapta el estilo y tono\n- NO generes subtítulo, solo volanta, título, resumen y contenido\n- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:\n{\n  \"volanta\": \"nueva volanta\",\n  \"titulo\": \"nuevo título\",\n  \"resumen\": \"nuevo resumen\",\n  \"contenido\": \"nuevo contenido completo\"\n}\n\nNo incluyas explicaciones adicionales, solo el JSON.`;\n    const result = await model.generateContent(fullPrompt);\n    const response = await result.response;\n    const responseText = response.text();\n    if (!responseText) {\n        throw new Error('No se recibió respuesta de Google Gemini');\n    }\n    // Intentar parsear la respuesta JSON\n    let parsedResponse;\n    try {\n        parsedResponse = JSON.parse(responseText);\n    } catch (parseError) {\n        // Si falla el parsing, intentar extraer JSON del texto\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            parsedResponse = JSON.parse(jsonMatch[0]);\n        } else {\n            throw new Error('La respuesta de Google Gemini no está en formato JSON válido');\n        }\n    }\n    const endTime = Date.now();\n    const generationTime = endTime - startTime;\n    return {\n        titulo: parsedResponse.titulo || request.titulo,\n        volanta: parsedResponse.volanta,\n        contenido: parsedResponse.contenido || request.contenido,\n        resumen: parsedResponse.resumen,\n        metadatos: {\n            modelo: config.geminiModel,\n            tokens_usados: 0,\n            tiempo_generacion: generationTime,\n            diario: request.diarioNombre,\n            proveedor: 'Google Gemini'\n        }\n    };\n}\n// Función principal para reescribir noticias\nasync function rewriteNoticia(request, diarioId) {\n    try {\n        const config = await getAIConfig();\n        let provider = config.defaultProvider;\n        // Si se especifica un diario, verificar su configuración específica\n        if (diarioId) {\n            const diario = await prisma.diario.findUnique({\n                where: {\n                    id: diarioId\n                }\n            });\n            if (diario && !diario.useGlobalConfig && diario.aiProvider) {\n                provider = diario.aiProvider;\n                // Si el diario tiene un modelo específico, usarlo\n                if (diario.aiModel) {\n                    if (provider === 'OPENAI') {\n                        config.openaiModel = diario.aiModel;\n                    } else {\n                        config.geminiModel = diario.aiModel;\n                    }\n                }\n            }\n        }\n        // Ejecutar reescritura según el proveedor\n        if (provider === 'GEMINI') {\n            return await rewriteWithGemini(request, config);\n        } else {\n            return await rewriteWithOpenAI(request, config);\n        }\n    } catch (error) {\n        console.error('Error en rewriteNoticia:', error);\n        throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);\n    }\n}\n// Función para probar conexión con OpenAI\nasync function testOpenAIConnection(apiKey) {\n    try {\n        const config = await getAIConfig();\n        const openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            apiKey: apiKey || config.openaiApiKey || process.env.OPENAI_API_KEY\n        });\n        const completion = await openai.chat.completions.create({\n            model: config.openaiModel,\n            messages: [\n                {\n                    role: \"user\",\n                    content: \"Responde solo con 'OK'\"\n                }\n            ],\n            max_tokens: 5\n        });\n        return completion.choices[0]?.message?.content?.includes('OK') || false;\n    } catch (error) {\n        console.error('Error testing OpenAI connection:', error);\n        return false;\n    }\n}\n// Función para probar conexión con Google Gemini\nasync function testGeminiConnection(apiKey) {\n    try {\n        const config = await getAIConfig();\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(apiKey || config.geminiApiKey || process.env.GEMINI_API_KEY || '');\n        const model = genAI.getGenerativeModel({\n            model: config.geminiModel\n        });\n        const result = await model.generateContent(\"Responde solo con 'OK'\");\n        const response = await result.response;\n        const text = response.text();\n        return text.includes('OK');\n    } catch (error) {\n        console.error('Error testing Gemini connection:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ai-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const user = await prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        }\n                    });\n                    if (!user) {\n                        return null;\n                    }\n                    const isPasswordValid = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        return null;\n                    }\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error during authentication:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    },\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@babel+runtime@7.27.6","vendor-chunks/jose@4.15.9","vendor-chunks/openid-client@5.7.1","vendor-chunks/oauth@0.9.15","vendor-chunks/object-hash@2.2.0","vendor-chunks/preact@10.26.9","vendor-chunks/uuid@8.3.2","vendor-chunks/yallist@4.0.0","vendor-chunks/preact-render-to-string@5.2.6_preact@10.26.9","vendor-chunks/lru-cache@6.0.0","vendor-chunks/cookie@0.7.2","vendor-chunks/oidc-token-hash@5.1.0","vendor-chunks/@panva+hkdf@1.2.1","vendor-chunks/openai@5.10.1_zod@3.25.75","vendor-chunks/@google+generative-ai@0.24.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&page=%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fai-config%2Ftest%2Froute.ts&appDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CDEL%20SUR%20FINAL%5Cpanel%20unificado%20version%202&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();