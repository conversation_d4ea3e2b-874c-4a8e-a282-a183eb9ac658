{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/categorias/route": "app/api/categorias/route.js", "/api/noticias/route": "app/api/noticias/route.js", "/dashboard/page": "app/dashboard/page.js", "/noticias/page": "app/noticias/page.js", "/noticias/nueva/page": "app/noticias/nueva/page.js", "/noticias/[id]/editar/page": "app/noticias/[id]/editar/page.js", "/noticias/[id]/page": "app/noticias/[id]/page.js"}