{"/api/noticias/route": "app/api/noticias/route.js", "/api/diarios/route": "app/api/diarios/route.js", "/api/categorias/route": "app/api/categorias/route.js", "/api/noticias/[id]/route": "app/api/noticias/[id]/route.js", "/api/noticias/[id]/generate-versions/route": "app/api/noticias/[id]/generate-versions/route.js", "/api/noticias/[id]/versions/route": "app/api/noticias/[id]/versions/route.js", "/dashboard/page": "app/dashboard/page.js", "/admin/diarios/page": "app/admin/diarios/page.js", "/noticias/page": "app/noticias/page.js", "/noticias/[id]/page": "app/noticias/[id]/page.js", "/noticias/[id]/editar/page": "app/noticias/[id]/editar/page.js", "/noticias/[id]/revision/page": "app/noticias/[id]/revision/page.js"}