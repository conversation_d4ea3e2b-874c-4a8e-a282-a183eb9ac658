{"/api/categorias/route": "app/api/categorias/route.js", "/api/noticias/[id]/route": "app/api/noticias/[id]/route.js", "/api/noticias/route": "app/api/noticias/route.js", "/api/diarios/route": "app/api/diarios/route.js", "/api/noticias/[id]/generate-versions/route": "app/api/noticias/[id]/generate-versions/route.js", "/api/noticias/[id]/versions/route": "app/api/noticias/[id]/versions/route.js", "/admin/ai-config/page": "app/admin/ai-config/page.js", "/admin/page": "app/admin/page.js", "/admin/diarios/page": "app/admin/diarios/page.js", "/noticias/nueva/page": "app/noticias/nueva/page.js", "/noticias/[id]/page": "app/noticias/[id]/page.js", "/noticias/page": "app/noticias/page.js", "/noticias/[id]/revision/page": "app/noticias/[id]/revision/page.js"}