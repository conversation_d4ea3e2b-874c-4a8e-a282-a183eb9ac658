{"/api/noticias/route": "app/api/noticias/route.js", "/api/categorias/route": "app/api/categorias/route.js", "/api/noticias/stats/route": "app/api/noticias/stats/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/api/noticias/[id]/route": "app/api/noticias/[id]/route.js", "/api/noticias/[id]/versions/route": "app/api/noticias/[id]/versions/route.js", "/api/diarios/route": "app/api/diarios/route.js", "/api/admin/diarios-config/route": "app/api/admin/diarios-config/route.js", "/noticias/[id]/revision/page": "app/noticias/[id]/revision/page.js", "/noticias/page": "app/noticias/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/dashboard/page": "app/dashboard/page.js", "/noticias/[id]/editar/page": "app/noticias/[id]/editar/page.js", "/admin/page": "app/admin/page.js", "/admin/diarios/page": "app/admin/diarios/page.js"}