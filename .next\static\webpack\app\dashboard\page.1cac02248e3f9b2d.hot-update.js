"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Shield)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z\",\n            key: \"oel41y\"\n        }\n    ]\n];\nconst Shield = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"shield\", __iconNode);\n //# sourceMappingURL=shield.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/next-auth@4.24.11_next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0__react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,List,LogOut,Newspaper,Plus,Shield,TrendingUp,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.523.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_theme_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-toggle */ \"(app-pages-browser)/./src/components/theme-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Dashboard() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        status,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                loadStats();\n                checkAdminStatus();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    const checkAdminStatus = async ()=>{\n        try {\n            const response = await fetch('/api/admin/users?limit=1');\n            setIsAdmin(response.ok && response.status !== 403);\n        } catch (error) {\n            setIsAdmin(false);\n        }\n    };\n    const loadStats = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/noticias/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setStats(data);\n            }\n        } catch (error) {\n            console.error('Error al cargar estadísticas:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (status === 'loading' || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 dark:border-blue-400\"\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null;\n    }\n    const handleSignOut = async ()=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n            callbackUrl: '/auth/signin'\n        });\n    };\n    const navigateToNoticias = ()=>{\n        router.push('/noticias');\n    };\n    const navigateToNewNoticia = ()=>{\n        router.push('/noticias/nueva');\n    };\n    const navigateToAdminUsuarios = ()=>{\n        router.push('/admin/usuarios');\n    };\n    const navigateToAdmin = ()=>{\n        router.push('/admin');\n    };\n    const getEstadoColor = (estado)=>{\n        switch(estado){\n            case 'PUBLICADA':\n                return 'text-green-600 dark:text-green-400';\n            case 'APROBADA':\n                return 'text-blue-600 dark:text-blue-400';\n            case 'EN_REVISION':\n                return 'text-yellow-500 dark:text-yellow-400';\n            case 'BORRADOR':\n                return 'text-gray-500 dark:text-gray-400';\n            default:\n                return 'text-gray-500 dark:text-gray-400';\n        }\n    };\n    const getEstadoIcon = (estado)=>{\n        switch(estado){\n            case 'PUBLICADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600 dark:text-green-400\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 16\n                }, this);\n            case 'APROBADA':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600 dark:text-blue-400\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, this);\n            case 'EN_REVISION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500 dark:text-yellow-400\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, this);\n            case 'BORRADOR':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500 dark:text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500 dark:text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"ml-3 text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                                        children: \"Sapia Noticias v2.0\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                children: ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || 'Usuario'\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSignOut,\n                                        className: \"p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: navigateToNoticias,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"-ml-1 mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Ver Noticias\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToAdmin,\n                                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"-ml-1 mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Panel Admin\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToAdminUsuarios,\n                                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"-ml-1 mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Usuarios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: navigateToNewNoticia,\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"-ml-1 mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Nueva Noticia\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                children: \"Estad\\xedsticas Generales\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-5 w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 truncate dark:text-gray-400\",\n                                                                    children: \"Noticias Totales\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                                                    children: (stats === null || stats === void 0 ? void 0 : stats.total) || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-6 w-6 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-5 w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 truncate dark:text-gray-400\",\n                                                                    children: \"Publicadas\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                                                    children: (stats === null || stats === void 0 ? void 0 : stats.publicadas) || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-6 w-6 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-5 w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 truncate dark:text-gray-400\",\n                                                                    children: \"En Revisi\\xf3n\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                                                    children: (stats === null || stats === void 0 ? void 0 : stats.enRevision) || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_List_LogOut_Newspaper_Plus_Shield_TrendingUp_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-6 w-6 text-purple-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-5 w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 truncate dark:text-gray-400\",\n                                                                    children: \"Destacadas\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                                                    children: (stats === null || stats === void 0 ? void 0 : stats.destacadas) || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 bg-white dark:bg-gray-800 shadow rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                            children: \"Noticias Recientes\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"divide-y divide-gray-200 dark:divide-gray-700\",\n                                        children: (stats === null || stats === void 0 ? void 0 : stats.recientes) && stats.recientes.length > 0 ? stats.recientes.map((noticia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                getEstadoIcon(noticia.estado),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-md\",\n                                                                            children: noticia.titulo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                                            children: [\n                                                                                noticia.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium\",\n                                                                                    style: {\n                                                                                        backgroundColor: \"\".concat(noticia.categoria.color, \"20\"),\n                                                                                        color: noticia.categoria.color\n                                                                                    },\n                                                                                    children: noticia.categoria.nombre\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 320,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                                    children: new Date(noticia.createdAt).toLocaleDateString()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                noticia.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                                    children: [\n                                                                                        \"por \",\n                                                                                        noticia.user.name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium \".concat(getEstadoColor(noticia.estado)),\n                                                            children: noticia.estado.replace('_', ' ')\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, noticia.id, false, {\n                                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-8 text-center text-gray-500 dark:text-gray-400\",\n                                            children: \"No hay noticias recientes\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\DEL SUR FINAL\\\\panel unificado version 2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"+eIqgIItOAEHBS0SKpG/pzoHFU0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});